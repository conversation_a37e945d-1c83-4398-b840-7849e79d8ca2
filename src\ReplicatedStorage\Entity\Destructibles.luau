local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");
local CollectionService = game:GetService("CollectionService");

local modInfoBubbles = shared.require(game.ReplicatedStorage.Library.InfoBubbles);
local modPropertiesVariable = shared.require(game.ReplicatedStorage.Library.PropertiesVariable);
local modHealthComponent = shared.require(game.ReplicatedStorage.Components.HealthComponent);
local modRewardsLibrary = shared.require(game.ReplicatedStorage.Library.RewardsLibrary);
local modVector = shared.require(game.ReplicatedStorage.Library.Util.Vector);

local destructiblePackages = game.ReplicatedStorage.Library.Destructibles;

local Destructibles = {};
Destructibles.__index = Destructibles;
Destructibles.meta = Destructibles;
Destructibles.ClassName = "Destructible";

Destructibles.InstanceList = {};
Destructibles.Db = {};
--==

function Destructibles.getOrNew(config: Configuration)
	if not config:IsA("Configuration") then return end;

	local destructible: Destructible = Destructibles.InstanceList[config];
	if destructible == nil then
		destructible = Destructibles:Instance(config);
		Destructibles.InstanceList[config] = destructible;

		config.Destroying:Once(function()
			Destructibles.InstanceList[config] = nil;
			Destructibles.clearOldInstance();
		end)
	end

	return destructible;
end

function Destructibles.createDestructible()
    local new = Instance.new("Configuration");
    new.Name = `Destructible`;
    new:SetAttribute("_Name", "Generic");
    new:AddTag("Destructible");
    return new;
end

function Destructibles.CanTakeDamageFrom(destructible: DestructibleInstance, attackerCharacter: CharacterClass)
    if destructible.Enabled == false then return false end;

    if attackerCharacter.ClassName == "PlayerClass" then
        local player = (attackerCharacter :: PlayerClass):GetInstance();
        if destructible.NetworkOwners == nil then return true end;

        for a=1, #destructible.NetworkOwners do
            if destructible.NetworkOwners[a] == player then
                return true;
            end
        end

        return false;

    elseif attackerCharacter.ClassName == "NpcClass" then
        local wieldComp: WieldComp = attackerCharacter.WieldComp;
        if wieldComp == nil then return false end;

        if wieldComp.TargetableTags.Destructibles ~= true then
            return false;
        end

        if wieldComp.TargetableTags[destructible.Name] ~= true then
            return false;
        end
    end

    return true;
end

function Destructibles.TakeDamage(destructible: DestructibleInstance, damageData: DamageData)
    if destructible.Enabled == false then return end;
    local healthComp: HealthComp = destructible.HealthComp;

    local damage = damageData.Damage;

    if healthComp.CurArmor > 0 then
        healthComp:SetArmor(healthComp.CurArmor - damage);
        return;
    end
    if damageData.DamageType == "ArmorOnly" then return end;

    healthComp:SetHealth(healthComp.CurHealth - damage, damageData);
    
    local damageType = damageData.DamageType;
    local targetPart = damageData.TargetPart;

    if damageType == "Hidden" then
    else
        local players = {};
        for _, player in pairs(game.Players:GetPlayers()) do
            if destructible.NetworkOwners == nil then
                if player:DistanceFromCharacter(targetPart.Position) > 100 then continue end;
                table.insert(players, player);
            else
                for a=1, #destructible.NetworkOwners do
                    table.insert(players, player);
                end
            end
        end

        if damage >= 1 then
            modInfoBubbles.Create{
                Players = players;
                Position = targetPart.Position;
                Type = (damageType or "Damage");
                Value = damage;
            };

        elseif damage <= -1 then
            modInfoBubbles.Create{
                Players = players;
                Position = targetPart.Position;
                Type = "Heal";
                Value = math.abs(damage);
            };

        else
            modInfoBubbles.Create{
                Players = players;
                Position = targetPart.Position;
                Type = "Immune";
            };
        end
    end
end

function Destructibles.initHealthComp(destructible)
    local healthComp: HealthComp = destructible.HealthComp;

	-- @override TakeDamage
	function healthComp:TakeDamage(damageData: DamageData)
        return Destructibles.TakeDamage(destructible, damageData);
    end

    -- @override CanTakeDamageFrom
    function healthComp:CanTakeDamageFrom(attackerCharacter: CharacterClass)
        return Destructibles.CanTakeDamageFrom(destructible, attackerCharacter);
    end

    healthComp.OnIsDeadChanged:Connect(function(isDead, prevIsDead, reasonData)
        if not isDead or isDead == prevIsDead then return end;
        Debugger:Warn(`IsDead {destructible.Model:GetFullName()}`);

        destructible.OnDestroy:Fire(reasonData);
        destructible:Destroy();
    end)

end

function Destructibles.new(config: Configuration, name: string, model: Model)
    local self = {
        -- @properties
        Config = config;
        Name = name;
        Model = model;
        Package = Destructibles.Db[name];

        Properties = modPropertiesVariable.new();

		Enabled = true;

        HealthComp = nil;

        NetworkOwners = nil;

        -- @signals
		OnDestroy = shared.EventSignal.new("OnDestroy");
		OnEnabledChanged = shared.EventSignal.new("OnEnabledChanged");
    };
    setmetatable(self, Destructibles);
  
    self.Model:AddTag("DestructibleModels");

    local healthComp: HealthComp = modHealthComponent.new(self :: any);
    self.HealthComp = healthComp; 
    
    Destructibles.initHealthComp(self);

    local maxHealthRange = config:GetAttribute("MaxHealth") :: NumberRange;
    if maxHealthRange then
        healthComp:SetMaxHealth(math.random(maxHealthRange.Min, maxHealthRange.Max));
        healthComp:SetHealth(healthComp.MaxHealth);
    end
    
    local isEnabled = config:GetAttribute("Enabled") :: boolean;
    if isEnabled ~= nil then
        self:SetEnabled(isEnabled);
    end

    if self.Package.BindNew then
        self.Package.BindNew(self);
    end

    if RunService:IsServer() then
        local modItemDrops = shared.require(game.ServerScriptService.ServerLibrary.ItemDrops);

        self.OnDestroy:Connect(function()
            local rewardId = config:GetAttribute("RewardId");
            if rewardId == nil then return end;

			local resourceTable = modRewardsLibrary:Find(self.RewardId);
			if resourceTable then
				local cframe = self.Model:GetPivot();

				local itemDrop = modItemDrops.ChooseDrop(resourceTable);
				if itemDrop then
					modItemDrops.Spawn(itemDrop, cframe);
				end
			end
        end)

    end

    return self;
end

function Destructibles:Instance(config: Configuration)
    local name = config:GetAttribute("_Name") or "Generic";

	local package = Destructibles.Db[name];
	if package == nil then
        local packageModule = destructiblePackages:FindFirstChild(name);
        if packageModule then 
            package = shared.require(packageModule);
            Destructibles.Db[name] = package;
        end;
    end
	if package == nil then
		Debugger:Warn(`Attempted to instance non-existent destructible: {name}`);
		return;
    end

    local model = config.Parent;
	while model:GetAttribute("DestructibleParent") do 
		model = model.Parent :: Model; 
	end

    return Destructibles.new(config, name, model);
end

function Destructibles:SetEnabled(value: boolean)
    if self.Enabled == value then return end;
    self.Enabled = value;
    self.OnEnabledChanged:Fire(value);
end

function Destructibles:Destroy()
    if self.Model then
        local debrisModel = self.Model;
        local pivot = debrisModel:GetPivot();
        debrisModel.Parent = workspace.Debris;

        local min = 150;
        local max = 250;
        
        for _, obj in pairs(debrisModel:GetDescendants()) do
            if obj:IsA("JointInstance") then
                obj:Destroy();

            elseif obj:IsA("BasePart") then
				if obj.Name == "Clip" or obj.Name == "Debris" or obj.Name == "PrimaryPart" then
					obj:Destroy();
                    continue;
				end
                obj.CollisionGroup = "Debris";
                obj.CanCollide = true;
                obj.Anchored = false;

                local dir = (obj.Position - pivot.Position).Unit;
                if shared.IsNan(dir) then
                    dir = modVector.RandomUnitVector(3);
                end

                obj:ApplyImpulse(dir * obj.AssemblyMass * math.random(min*100, max*100)/100);
            end
        end

        game.Debris:AddItem(debrisModel, 60);
    end

    self.OnDestroy:Destroy();
    self.OnEnabledChanged:Destroy();
    self.HealthComp:Destroy();
end

function Destructibles.clearOldInstance()
	for config, destructible in pairs(Destructibles.InstanceList) do
		local destroy = false;

		if not game:IsAncestorOf(config) then
			destroy = true;
		end

		if destroy then
			if destructible.Package.destroy then
				destructible.Package.destroy(destructible);
			end
			Destructibles.InstanceList[config] = nil;
		end
	end
end

function Destructibles.onRequire()
    if RunService:IsServer() then
        CollectionService:GetInstanceAddedSignal("Destructible"):Connect(function(config)
            Destructibles.getOrNew(config);
        end)
        for _, config in pairs(CollectionService:GetTagged("Destructible")) do
            Destructibles.getOrNew(config);
        end

    end
end

return Destructibles;