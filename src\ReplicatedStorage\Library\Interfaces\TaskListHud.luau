local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local modPropertiesVariable = shared.require(game.ReplicatedStorage.Library.PropertiesVariable);

local interfacePackage = {
    Type = "Character";
};
--==

function interfacePackage.newInstance(interface: InterfaceInstance)
    local templateTaskListing = script:WaitForChild("TaskListing");

    local mainFrame = script:WaitForChild("TaskListHud"):Clone();
    mainFrame.Parent = interface.ScreenGui;

    local scrollList = mainFrame:WaitForChild("ScrollList");

    local window: InterfaceWindow = interface:NewWindow("TaskListHud", mainFrame);
    window.IgnoreHideAll = true;
    window.ReleaseMouse = false;
    window.Layers = {"CompactHidden"};
    window:SetClosePosition(UDim2.new(1.6, 0, 0, 90));

    local binds = window.Binds;
    binds.GenericLabel = script:WaitForChild("genericLabel");
    
    local hudTaskList = {};
    binds.Tasks = hudTaskList;

    local HudTask = {};
    HudTask.__index = HudTask;

    function HudTask.new(id)
        local self = {
            Id = id;
            Frame = templateTaskListing:Clone();
            Order = 1;
            Properties = modPropertiesVariable.new({
                TitleText = "<title>";
                HeaderText = "<header>";
                DescText = "<desc>";
            });
        };
        setmetatable(self, HudTask);

        self.Properties.OnChanged:Connect(function()
            self:Update();
        end);
        
        local function updateFrameSize()
            self.Frame.Size = UDim2.new(0, mainFrame.AbsoluteSize.X, 0, 0);
        end
        mainFrame:GetPropertyChangedSignal("AbsoluteSize"):Connect(updateFrameSize);
        updateFrameSize();

        self.Frame.Name = id;
        self.Frame.Parent = scrollList;
        
        return self;
    end

    function HudTask:Destroy()
        self.Frame:Destroy();
        self.Properties:Destroy();
    end

    function HudTask:Update()
        self.Frame.LayoutOrder = self.Order;

        local titleLabel = self.Frame.Content.titleLabel;
        local headerLabel = self.Frame.Content.headerLabel;
        local descLabel = self.Frame.Content.descLabel;

        titleLabel.Text = `{self.Properties.TitleText}`;
        
        headerLabel.Text = `{self.Properties.HeaderText}`;
        if #headerLabel.Text > 0 then
            headerLabel.Visible = true;
        else
            headerLabel.Visible = false;
        end

        descLabel.Text = `{self.Properties.DescText}`;
        if #descLabel.Text > 0 then
            descLabel.Visible = true;
        else
            descLabel.Visible = false;
        end
    end

    local function updateZIndex()
        if interface.Properties.MouseCaptured == true then
            binds.setZIndex(2);
        else
            binds.setZIndex(0);
        end
    end

    function binds.getOrNewTask(id, new)
        local hudTask;
        for a=1, #hudTaskList do
            if hudTaskList[a].Id == id then
                hudTask = hudTaskList[a];
                break;
            end
        end

        if hudTask == nil and new == true then
            hudTask = HudTask.new(id);
            table.insert(hudTaskList, hudTask);
            updateZIndex();
            window:Update();
        end

        if hudTask then
            hudTask:Update();
        end

        return hudTask;
    end

    function binds.destroyHudTask(id)
        local destroyed = false;
        for a=#hudTaskList, 1, -1 do
            if hudTaskList[a].Id == id then
                hudTaskList[a]:Destroy();
                updateZIndex();
                table.remove(hudTaskList, a);
                destroyed = true;
            end
        end

        if destroyed then
            window:Update();
        end
    end

    function binds.setZIndex(z)
        for _, obj in pairs(scrollList:GetDescendants()) do
            if not obj:IsA("GuiObject") then continue end;
            obj.ZIndex = z;
        end
    end

    interface.Properties.OnChanged:Connect(function(k, v)
        if k == "MouseCaptured" then
            updateZIndex();
        end
    end)

    interface.Garbage:Tag(function()
        for a=#hudTaskList, 1, -1 do
            hudTaskList[a]:Destroy();
            table.remove(hudTaskList, a);
        end
    end)

    --MARK: OnToggle
    window.OnToggle:Connect(function(visible)
    end)

    --MARK: OnUpdate
    window.OnUpdate:Connect(function()
        if #hudTaskList > 0 then
            window:Open();
        else
            window:Close();
        end

        for a=#hudTaskList, 1, -1 do
            local hudTask = hudTaskList[a];
            hudTask:Update();
        end
    end)

    interface.Scheduler.OnStepped:Connect(function(tickData: TickData)
        for a=1, #hudTaskList do
            local hudTask = hudTaskList[a];
            if hudTask.OnTick then
                hudTask:OnTick(tickData);
            end
        end
    end)

    interface.OnReady:Connect(function()
        window:Update();
    end)
end

return interfacePackage;

