local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local TweenService = game:GetService("TweenService");
local GuiService = game:GetService("GuiService");
local UserInputService = game:GetService("UserInputService");
local ContextActionService = game:GetService("ContextActionService");
local StarterGui = game:GetService("StarterGui");
local TextService = game:GetService("TextService");
local RunService = game:GetService("RunService");

local camera = workspace.CurrentCamera;
local localPlayer = game.Players.LocalPlayer;
local playerGui = localPlayer.PlayerGui;

local modGlobalVars = shared.require(game.ReplicatedStorage.GlobalVariables);
local modGarbageHandler = shared.require(game.ReplicatedStorage.Library.GarbageHandler);
local modConfigurations = shared.require(game.ReplicatedStorage.Library.Configurations);
local modBranchConfigs = shared.require(game.ReplicatedStorage.Library.BranchConfigurations);
local modSyncTime = shared.require(game.ReplicatedStorage.Library.SyncTime);
local modScheduler = shared.require(game.ReplicatedStorage.Library.Scheduler);
local modKeyBindsHandler = shared.require(game.ReplicatedStorage.Library.KeyBindsHandler);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modPropertiesVariable = shared.require(game.ReplicatedStorage.Library.PropertiesVariable);
local modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);

local modRichFormatter = shared.require(game.ReplicatedStorage.Library.UI.RichFormatter);
local modLeaderboardInterface = shared.require(game.ReplicatedStorage.Library.UI.LeaderboardInterface);
local modStorageInterface = shared.require(game.ReplicatedStorage.Library.UI.StorageInterface);
local modItemInterface = shared.require(game.ReplicatedStorage.Library.UI.ItemInterface);

local templateMenuBlur, templateQuickButton, templateCutsceneNextButton, 
    templateBlindsFrame, templateVersionLabel, templateAimPointer,
    templateDeathScreen, templateMouseLockHint;

local remoteCutsceneService;

local HOTBAR_KEYCODES = {
	Enum.KeyCode.One;
	Enum.KeyCode.Two;
	Enum.KeyCode.Three;
	Enum.KeyCode.Four;
	Enum.KeyCode.Five;
	Enum.KeyCode.Six;
	Enum.KeyCode.Seven;
	Enum.KeyCode.Eight;
	Enum.KeyCode.Nine;
	Enum.KeyCode.Zero;
};

local InterfaceClass = {} :: Class;
InterfaceClass.__index = InterfaceClass;
InterfaceClass.Script = script;

local InterfaceWindow = {};
InterfaceWindow.__index = InterfaceWindow;

local InterfaceElement = {};
InterfaceElement.__index = InterfaceElement;
--==


function InterfaceClass.onRequire()
    templateMenuBlur = script:WaitForChild("MenuBlur");
    templateQuickButton = script:WaitForChild("templateQuickButton");
    templateCutsceneNextButton = script:WaitForChild("CutsceneNextButton");
    templateBlindsFrame = script:WaitForChild("GameBlinds");
    templateVersionLabel = script:WaitForChild("VersionLabel");
    templateAimPointer = script:WaitForChild("AimPointer");
    templateDeathScreen = script:WaitForChild("DeathScreen");
    templateMouseLockHint = script:WaitForChild("MouseLockHint");

    remoteCutsceneService = modRemotesManager:Get("CutsceneService"); 
end

function InterfaceClass.setPositionWithPadding(guiObject, position, padding)
	if guiObject.Parent == nil then return end;
	padding = padding or 5;

	local targetPos = position or (UserInputService:GetMouseLocation() 
                    + Vector2.new(0, -game.GuiService.TopbarInset.Height));

	local parentPos = guiObject.Parent:IsA("GuiObject") and guiObject.Parent.AbsolutePosition or Vector2.zero;
	local frameSize = guiObject.AbsoluteSize;
	
	local vpSize = camera.ViewportSize;
	
	if targetPos.X <= vpSize.X/2 then
		targetPos = targetPos;
	else
		targetPos = targetPos + Vector2.new(-frameSize.X, 0);
	end

	local posX = targetPos.X+frameSize.X+(padding*2) > vpSize.X-10 and targetPos.X-frameSize.X-padding or targetPos.X;
	local posY = targetPos.Y+frameSize.Y+(padding*2) > vpSize.Y-10 and targetPos.Y-frameSize.Y+padding or targetPos.Y;

	guiObject.Position = UDim2.new(0, 
		math.clamp(posX - parentPos.X, 10, math.max(vpSize.X-frameSize.X-10, 11)), 
		0, 
		math.clamp(posY - parentPos.Y, 10, math.max(vpSize.Y-frameSize.Y-10, 11))
	);
end


-- MARK: InterfaceClass
function InterfaceClass.new(screenGui: ScreenGui)
    local self = {
        -- @properties
        ScreenGui = screenGui;

        Garbage = modGarbageHandler.new();
        Scheduler = modScheduler.new("InterfaceScheduler", 1/10);
        Windows = {};
        Elements = {};

        Properties = modPropertiesVariable.new({
            MouseCaptured = true;
            DisableInteractables = false;
            DisableHotKeys = false;
            DisableHotKeysHint = false;
            CanOpenWindows = true;
            PrimaryInputDown = false;
            ShiftInputDown = false;
            AltInputDown = false;
            IsCompactFullscreen = false;
            ActiveHotbarKeys = 5;

            TopbarInset = GuiService.TopbarInset;
        });

        StorageInterfaces = {};
        StorageInterfaceIndex = 1;

        GameBlindsFrame = nil;
        QuickBarButtons = nil;

        -- @signals
        OnInterfaceEvent = shared.EventSignal.new("OnInterfaceEvent");
        OnWindowToggle = shared.EventSignal.new("OnWindowToggle");
    };
    self.__index = self;

    setmetatable(self, InterfaceClass);
    return self;
end


function InterfaceClass:Init()
    local modData = shared.require(localPlayer:WaitForChild("DataModule"));

    local loadComplete = false;
    local loadList = {};

    local loadCharacterInterfaces = false;
    if localPlayer.Character then
        loadCharacterInterfaces = true;
    end

    --MARK: Engine UIs

    ContextActionService:UnbindAction("EmotesMenuToggleAction");
    StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.EmotesMenu, false);

    self.MenuBlur = templateMenuBlur:Clone();
    self.MenuBlur.Parent = self.ScreenGui;

    self.GameBlindsFrame = templateBlindsFrame:Clone();
    self.GameBlindsFrame.Parent = self.ScreenGui;

    self.CutsceneNextButton = templateCutsceneNextButton:Clone();
    self.CutsceneNextButton.Parent = self.ScreenGui;

    self.DeathScreen = templateDeathScreen:Clone();
    self.DeathScreen.Parent = self.ScreenGui;

    self.MouseLockHint = templateMouseLockHint:Clone();
    self.MouseLockHint.Parent = self.ScreenGui;

    local verLabel = templateVersionLabel:Clone();
    verLabel.Parent = self.ScreenGui;
    self.VersionLabel = verLabel;

    local function updateOmniLabel()
        local modData = shared.require(localPlayer:WaitForChild("DataModule"));
        local vText = "";
        local upTime = modSyncTime.ToString(os.time()-modSyncTime.GetUpTime());
        
        local branchText = modRichFormatter.Color(modBranchConfigs.BranchColor:ToHex(), modBranchConfigs.CurrentBranch.Name);
        if shared.gameCore == "Rotd" then
            vText = `$fps fps\t{upTime}\tRevived {modGlobalVars.GameVersion} {branchText} {modData:IsMobile() and "M" or ""}`;
        else
            vText = modGlobalVars.ModeVerLabel:gsub("$UpTime", upTime);
        end

        vText = string.gsub(vText, "$fps", Debugger.ClientFps);
        
        verLabel.Text = vText;
        if modConfigurations.VersionLabelSide == "Left" then
            verLabel.TextXAlignment = Enum.TextXAlignment.Left;
        elseif modConfigurations.VersionLabelSide == "Center" then
            verLabel.TextXAlignment = Enum.TextXAlignment.Center;
        else
            verLabel.TextXAlignment = Enum.TextXAlignment.Right;
        end

        if modConfigurations.CompactInterface then
            verLabel.Size = UDim2.new(1, -10, 0, 10);
        end
    end
    self.Garbage:Tag(modSyncTime.GetClock():GetPropertyChangedSignal("Value"):Connect(updateOmniLabel));


    local quickBarButtonsFrame: Frame = script:WaitForChild("QuickButtons"):Clone();
    quickBarButtonsFrame.Parent = self.ScreenGui;
    self.QuickBarButtons = quickBarButtonsFrame;

    local function updateTopbarGuiObjects()
        if not loadComplete then return end;
        local height = GuiService.TopbarInset.Height;
        local minX = GuiService.TopbarInset.Min.X;

        quickBarButtonsFrame.Position = UDim2.new(0, minX, 0, 5);
        quickBarButtonsFrame.Size = UDim2.new(0, 32, 0, height-10);
    end

    self.Garbage:Tag(UserInputService.InputBegan:Connect(function(...)
        if not loadComplete then return end;
        self:OnInput("Began", ...);
    end))
    self.Garbage:Tag(UserInputService.InputEnded:Connect(function(...)
        if not loadComplete then return end;
        self:OnInput("Ended", ...);
    end))


    if UserInputService.TouchEnabled then
        local quickButton = self:NewQuickButton("Chat", "Chat", "rbxasset://textures/ui/TopBar/<EMAIL>");
        quickButton.LayoutOrder = 1;
        self:ConnectQuickButton(quickButton, nil, modClientGuis.toggleChatWindow);
    end


    local function cutscenePausedChanged()
        local isCutscenePaused = localPlayer:GetAttribute("CutscenePaused");
        if isCutscenePaused ~= true then return end;

        self.CutsceneNextButton.Visible = true;
    end
    self.Garbage:Tag(localPlayer:GetAttributeChangedSignal("CutscenePaused"):Connect(cutscenePausedChanged));
    self.CutsceneNextButton.MouseButton1Click:Connect(function()
        self.CutsceneNextButton.Visible = false;
        remoteCutsceneService:InvokeServer("continue");
    end)

    self.Garbage:Tag(GuiService:GetPropertyChangedSignal("TopbarInset"):Connect(function()
        self.Properties.TopbarInset = GuiService.TopbarInset;

        updateTopbarGuiObjects();
    end))

    -- MARK: Data
	local smallViewportSize = Vector2.new(1024, 600);
	if camera.ViewportSize.X < smallViewportSize.X or camera.ViewportSize.X < smallViewportSize.Y then
		modConfigurations.Set("CompactInterface", true);
	elseif modData.Settings.CompactInterface == 1 then
		modConfigurations.Set("CompactInterface", true);
	elseif modData.Settings.CompactInterface == 2 then
		modConfigurations.Set("CompactInterface", false);
	else
		modConfigurations.Set("CompactInterface", camera.ViewportSize.X <= smallViewportSize.X or camera.ViewportSize.Y <= smallViewportSize.Y);
	end

    local function loadSoundGroups(soundGroup: SoundGroup)
        if not soundGroup:IsA("SoundGroup") then return end;

        local settingsKey = "Snd"..soundGroup.Name;
        local volume = modData.Settings[settingsKey];
        if volume then
            soundGroup.Volume = volume/100;
        end
    end
    local soundGroups = game.SoundService:GetChildren();
    for a=1, #soundGroups do
        loadSoundGroups(soundGroups[a]);
    end
    game.SoundService.ChildAdded:Connect(loadSoundGroups);

    -- MARK: Character UIs
    if loadCharacterInterfaces then
        self.AimPointer = templateAimPointer:Clone();
        self.AimPointer.Parent = self.ScreenGui;
    end

    -- Instace interfaces
    local coreInterfaces = game.ReplicatedStorage.Library.Interfaces;
    for _, ms in pairs(coreInterfaces:GetChildren()) do
        if not ms:IsA("ModuleScript") then continue end;
        if ms.Name == "Template" then continue end;
        
        local interfacePackage = shared.require(ms);
        interfacePackage.Name = ms.Name;
        if interfacePackage.Type == "Character" and not loadCharacterInterfaces then continue end;

        table.insert(loadList, interfacePackage);
    end
	local gameCoreInterfaces = game.ReplicatedStorage.Library:FindFirstChild(`Interfaces{shared.gameCore}`);
    for _, ms in pairs(gameCoreInterfaces:GetChildren()) do
        if not ms:IsA("ModuleScript") then continue end;
        if ms.Name == "Template" then continue end;
        
        local interfacePackage = shared.require(ms);
        interfacePackage.Name = ms.Name;
        if interfacePackage.Type == "Character" and not loadCharacterInterfaces then continue end;

        for a=#loadList, 1, -1 do
            if loadList[a].Name == interfacePackage.Name then
                table.remove(loadList, a);
            end
        end
        table.insert(loadList, interfacePackage);
    end
    table.sort(loadList, function(a, b)
        return (a.LoadOrder or 999) < (b.LoadOrder or 999);
    end)

    for a=1, #loadList do
        self:Instance(loadList[a]);
    end

    loadComplete = true;
    -- After load
    cutscenePausedChanged();
    updateTopbarGuiObjects();

    if RunService:IsStudio() then
        self:ToggleGameBlinds(true, 3);

    else
        if modBranchConfigs.CurrentBranch.Name == "Dev" then
            script:WaitForChild("DevBranchLabel"):Clone().Parent = self.ScreenGui;
        end
    end

	modAudio.Preload("ButtonSound");
end


function InterfaceClass:RefreshInterfaces()
	local isMouseLocked = true;
	local hideWindowsForCompactFullscreen = false;
	local blurBackground = false;
	
	if playerGui:FindFirstChild("LobbyInterface") and playerGui.LobbyInterface.Enabled then
		isMouseLocked = false;
	end
	if playerGui:FindFirstChild("ChatInterface") and playerGui.ChatInterface.DisplayOrder ~= 0 then
		isMouseLocked = false;
	end
	
	for name, window: InterfaceWindow in pairs(self.Windows) do
		if window.Visible and window.ReleaseMouse ~= false then
			isMouseLocked = false;
		end
		if window.Visible and window.CompactFullscreen == true then
			hideWindowsForCompactFullscreen = true;
		end
		if window.Visible and window.UseMenuBlur then
			blurBackground = true;
		end
	end
	
    self.Properties.MouseCaptured = isMouseLocked;
	if not isMouseLocked then
		self.MouseLockHint.Visible = false;
	end
	
	if hideWindowsForCompactFullscreen and modConfigurations.CompactInterface then
        self.Properties.IsCompactFullscreen = true;
        self.QuickBarButtons.Visible = false;

	else
        self.Properties.IsCompactFullscreen = false;
        self.QuickBarButtons.Visible = true;

	end
	
	if blurBackground then
		self.MenuBlur.Parent = workspace.CurrentCamera;
	else
		self.MenuBlur.Parent = script;
	end
end


function InterfaceClass:OnInput(action, inputObject: InputObject, gameProcessed: boolean)
    if action == "Began" then
	    if UserInputService:GetFocusedTextBox() ~= nil then return end;

        if inputObject.UserInputType == Enum.UserInputType.MouseButton1 
        or inputObject.UserInputType == Enum.UserInputType.Touch then
            self.Properties.PrimaryInputDown = true;
            modStorageInterface.PrimaryInputDown = true;
        end
        if inputObject.KeyCode == Enum.KeyCode.LeftShift then
            self.Properties.ShiftInputDown = true;
            modStorageInterface.LeftShiftDown = true;
        end
        if inputObject.KeyCode == Enum.KeyCode.LeftAlt then
            self.Properties.AltInputDown = true;
            modStorageInterface.LeftAltDown = true;
        end

        if self.Properties.PrimaryInputDown then
            if self.CutsceneNextButton.Visible then
                remoteCutsceneService:InvokeServer("continue");
                self.CutsceneNextButton.Visible = false;
            end
        end

		if inputObject.KeyCode == Enum.KeyCode.Escape then
			self:HideAll();

		elseif modKeyBindsHandler:Match(inputObject, "KeyHideHud") then
			if script.Parent.Enabled then
				localPlayer:SetAttribute("DisableHud", true);
				
				local new = script:WaitForChild("tempHiddenHudScript"):Clone();
				local keyTag = new:WaitForChild("Key");
				keyTag.Value = modKeyBindsHandler:ToString("KeyHideHud");
				new.Parent = localPlayer.PlayerGui;
				new.Disabled = false;
				script.Parent.Enabled = false;
				
				modConfigurations.Set("DisableWeaponInterface", true);
			end


        elseif inputObject.KeyCode == Enum.KeyCode.Q then
            GuiService:SetEmotesMenuOpen(true);
            

		elseif modKeyBindsHandler:Match(inputObject, "KeyInteract") and self.Properties.TopClosableWindow ~= nil then
			local window: InterfaceWindow = self.Properties.TopClosableWindow;
            window:Close();
            self.Properties.TopClosableWindow = nil;

        else
            if self.Properties.DisableHotKeys then return end;

            for keyName, keyLib in pairs(modKeyBindsHandler.DefaultKeybind) do
                if keyName:sub(1, 9) ~= "KeyWindow" then continue end;
                local windowName = keyName:sub(10, #keyName);
                
                if modKeyBindsHandler:Match(inputObject, keyName) == false then continue end;
                if modConfigurations["Disable"..windowName] == true then continue end;
                
                local window = self:GetWindow(windowName);
                if window == nil then continue end;
                
                self:ToggleWindow(windowName);
            end

        end

        if localPlayer.Character == nil then return end;

        -- MARK: Hotbar Keybinds
        if self.Properties.HotEquip ~= nil 
        and modConfigurations.CanQuickEquip == true 
        and self.DisableGameplayBinds ~= true then
            local hotEquipped = false;

            for a=1, math.clamp(self.Properties.ActiveHotbarKeys, 1, 10) do
                if inputObject.KeyCode == HOTBAR_KEYCODES[a] 
                and modConfigurations.CanQuickEquip == true
                and self.DisableGameplayBinds ~= true then
                    
                    self.Properties.HotEquip(a);
                    self.ActiveEquip = a;
                    hotEquipped = true;
                    break;
                end
            end

            if hotEquipped then
            elseif inputObject.KeyCode == Enum.KeyCode.ButtonL1 then
                if self.ActiveEquip == nil then 
                    self.ActiveEquip = 0;
                end
                self.ActiveEquip = math.clamp(self.ActiveEquip -1, 1, self.Properties.ActiveHotbarKeys);
                if self.Properties.HotEquip then
                    self.Properties.HotEquip(self.ActiveEquip);
                end
                
            elseif inputObject.KeyCode == Enum.KeyCode.ButtonR1 then
                if self.ActiveEquip == nil then
                    self.ActiveEquip = self.Properties.ActiveHotbarKeys+1;
                end
                self.ActiveEquip = math.clamp(self.ActiveEquip +1, 1, self.Properties.ActiveHotbarKeys);
                if self.Properties.HotEquip then
                    self.Properties.HotEquip(self.ActiveEquip);
                end
                
            end
        end


    elseif action == "Ended" then

        if inputObject.UserInputType == Enum.UserInputType.MouseButton1 
        or inputObject.UserInputType == Enum.UserInputType.Touch then
            self.Properties.PrimaryInputDown = false;
            modStorageInterface.PrimaryInputDown = false;
        end
        if inputObject.KeyCode == Enum.KeyCode.LeftShift then
            self.Properties.ShiftInputDown = false;
            modStorageInterface.LeftShiftDown = false;
        end
        if inputObject.KeyCode == Enum.KeyCode.LeftAlt then
            self.Properties.AltInputDown = false;
            modStorageInterface.LeftAltDown = false;
        end
    end
end


function InterfaceClass:Instance(interfacePackage)
    if interfacePackage == nil then
        error(`Attempt to instance nil interfacePackage.`);
    end
    local name = interfacePackage.Name;

    local interfaceInstance = {
        Package = interfacePackage;
    };
    setmetatable(interfaceInstance, self);

    Debugger:StudioLog(`Instancing {name}.. Type: {interfacePackage.Type}..`);
    interfacePackage.newInstance(interfaceInstance);

    return interfaceInstance;
end


function InterfaceClass:UpdateInterface()
    local modData = shared.require(localPlayer:WaitForChild("DataModule"));

    local disableInteractions = false;
    local topClosableWindow = nil;
    local hideHotkeyHints = false;

    if modData:GetSetting("HideHotkey") == 1 or modConfigurations.CompactInterface then
        hideHotkeyHints = true;
    end

    for _, window: InterfaceWindow in pairs(self.Windows) do
        if window.DisableInteractables == true and window.Visible == true then
            disableInteractions = true;
        end
        if window.CloseWithInteract == true and window.Visible == true then
            topClosableWindow = window;
        end
        if window.DisableHotKeysHint == true and window.Visible == true then
            hideHotkeyHints = true;
        end
    end
    
    local function refreshQuickButton(button, visible)
        local defaultColor = Color3.fromRGB(255, 255, 255);
        
        if button.Name == "GoldMenu" then
            defaultColor = Color3.fromRGB(255, 220, 112);
        end

        if button:FindFirstChild("BkFrame") and button.BkFrame.Visible then
            button.BkFrame.BackgroundColor3 = visible and Color3.fromRGB(1, 162, 254) or defaultColor;
        else
            button.ImageColor3 = visible and Color3.fromRGB(1, 162, 254) or defaultColor;
        end
        button.hotKey.Size = UDim2.new(0, 18+(#button.hotKey.button.Text-1)*8, 0, 18);
        
        if hideHotkeyHints then
            button.hotKey.Visible = false;
        else
            button.hotKey.Visible = true;
        end
    end
    for _, window: InterfaceWindow in pairs(self.Windows) do
        if window.QuickButton ~= nil then
            refreshQuickButton(window.QuickButton, window.Visible);
        end
    end

    if self.Properties.DisableInteractables ~= disableInteractions then
        if disableInteractions then
            self.Properties.DisableInteractables = true;
        else
            task.delay(0.3, function()
                self.Properties.DisableInteractables = false;
            end)
        end
    end
    if self.Properties.TopClosableWindow ~= topClosableWindow then
        self.Properties.TopClosableWindow = topClosableWindow;
    end
    if self.Properties.DisableHotKeysHint ~= hideHotkeyHints then
        self.Properties.DisableHotKeysHint = hideHotkeyHints;
    end
    
end


function InterfaceClass:BindEvent(key: string, func)
    return self.OnInterfaceEvent:Connect(function(k: string, ...)
        if k ~= key then return end;
        func(...);
    end)
end


function InterfaceClass:FireEvent(bindKey, ...)
	self.OnInterfaceEvent:Fire(bindKey, ...);
end


function InterfaceClass:BindConfigKey(key, windows: {InterfaceWindow}, frames: {Instance}, conditions)
	local function toggle(d)
		local isDisabled = d;
		if conditions then
			isDisabled = conditions()
		end

        if windows then
            for a=1, #windows do
                local window: InterfaceWindow = windows[a];
                if isDisabled then
                    window.Frame.Visible = false;
                    window:Close();
                    if window.QuickButton then
                        window.QuickButton.Visible = false;
                    end
                else
                    if window.QuickButton then
                        window.QuickButton.Visible = true;
                    end
                end
            end
        end

		if frames then
			for a=1, #frames do
                local obj = frames[a];
                if obj:IsA("ScreenGui") then
                    obj.Enabled = not isDisabled;
                elseif obj:IsA("GuiObject") then
                    obj.Visible = not isDisabled;
                end
			end
		end
	end

    toggle(modConfigurations[key] or false);
	modConfigurations.OnChanged(key, function(oldValue, disabled)
		toggle(disabled);
	end)
end


function InterfaceClass:Destroy()
    self.Scheduler:Destroy();
    
    for windowId, window: InterfaceWindow in pairs(self.Windows) do
        window:Destroy();
    end
    for _, element: InterfaceElement in pairs(self.Elements) do
        element:Destroy();
    end
    for index, storageInterface: StorageInterface in pairs(self.StorageInterfaces) do
        storageInterface:Destroy();
    end

    game.Debris:AddItem(self.MenuBlur, 0);
    self.Properties.HotEquip = nil;
    self.Properties:Destroy();
    self.Garbage:Destruct();
    self.OnInterfaceEvent:Destroy();
end


function InterfaceClass:NewQuickButton(name: string, hint: string, image: string)
	local new = templateQuickButton:Clone();

	new.Name = name;
	new.Image = image;

	local quickButtonLabel = new:WaitForChild("Hint");
	quickButtonLabel.Text = hint or name;
	
	new.Parent = self.QuickBarButtons;
	
	return new;
end


function InterfaceClass:ConnectQuickButton(obj: ImageButton, keyId: string, onClickFunc)
	if obj:GetAttribute("ConnectedQuickButton") == true then
		return;
	end
	obj:SetAttribute("ConnectedQuickButton", true);
	
	local quickButton = obj;

    if onClickFunc == nil then
        onClickFunc = function()
            self:PlayButtonClick();
            self:ToggleWindow(quickButton.Name);
        end
    end

	if UserInputService.MouseEnabled then
		quickButton.MouseEnter:Connect(function()
			if not quickButton.Hint.Visible then 
                delay(1, function() 
                    quickButton.Hint.Visible = false; 
                end) 
            end;
			quickButton.Hint.Visible = true;
		end)
		
		quickButton.MouseLeave:Connect(function()
			quickButton.Hint.Visible = false;
		end)
	end
	quickButton.MouseButton1Click:Connect(onClickFunc)
	
    local window: InterfaceWindow = self:GetWindow(quickButton.Name);
    if window then
        window.QuickButton = quickButton;
    end
    
	if keyId ~= nil then
		local hotKeyLabel = quickButton:WaitForChild("hotKey"):WaitForChild("button");
		hotKeyLabel.Text = modKeyBindsHandler:ToString(keyId) or "";
	end
	
	quickButton.hotKey.Visible = not modConfigurations.DisableHotKeyLabels 
                                and UserInputService.KeyboardEnabled or false;
end


function InterfaceClass:ToggleGameBlinds(openBlinds: boolean, duration: number)
	duration = duration or 2;

    local blindsFrame = self.GameBlindsFrame;
    if blindsFrame == nil then return end;

	if openBlinds then
		if blindsFrame.BackgroundTransparency >= 0.95 then return end;
	else
		if blindsFrame.BackgroundTransparency <= 0.05 then return end;
	end

	blindsFrame.Visible = true;
	TweenService:Create(blindsFrame, TweenInfo.new(duration), {BackgroundTransparency = openBlinds and 1 or 0;}):Play();
end


function InterfaceClass:PlayButtonClick(pitch: number?)
	modAudio.Play("ButtonSound", nil, nil, false).PlaybackSpeed = pitch or 1;
end


local toggleDebounce = false;
function InterfaceClass:HideAll(blacklist: anydict)
	if toggleDebounce then return end;
	toggleDebounce = true;

	for name, window in pairs(self.Windows) do
		if window.Visible and (blacklist == nil or blacklist[name] == nil) and window.IgnoreHideAll ~= true then
			window.LastHideAllTick = tick();
			window:Close();
		end
	end

	toggleDebounce = false;
end


function InterfaceClass:GetWindow(name)
	return self.Windows[name];
end


function InterfaceClass:ToggleWindow(name, visible, ...)
	if self.Windows[name] then
		self.Windows[name]:Toggle(visible, ...);
        
        return self.Windows[name];
	else
		Debugger:Warn("Window named (",name,") does not exist.");
	end
    return;
end


function InterfaceClass:ListWindows(conditionFunc)
    local windows = {};

    for _, win in pairs(self.Windows) do
        if conditionFunc(win) then
            table.insert(windows, win);
        end
    end

    return windows;
end

-- MARK: InterfaceWindow
function InterfaceClass:NewWindow(name: string, frame: GuiObject, properties: anydict)
    local window = self.Windows[name];
    if window == nil then
        window = InterfaceWindow.new(name, frame, properties);

        window.OnToggle:Connect(function()
            self:UpdateInterface();
            self:RefreshInterfaces();
        end)
    else
        Debugger:Warn(`Window already exists.`, debug.traceback());
    end

    self.Windows[name] = window;

    return window;
end


function InterfaceWindow.new(name: string, frame: GuiObject, properties: anydict)
    local self = {
        Name = name;
        Frame = frame;
        Visible = false;

        OnToggle = shared.EventSignal.new(`On{name}Toggle`);
        OnUpdate = shared.EventSignal.new(`On{name}Update`);

        OpenPoint = nil;
        ClosePoint = nil;

        properties = properties or {};
        Properties = modPropertiesVariable.new(properties);

        Binds = {};

        ReleaseMouse = true;
        DisableInteractables = false;
        DisableOpeningWindows = false;
        CloseWithInteract = false;
        IgnoreHideAll = false;
        CompactFullscreen = false;

        _init = false;
    };
    
    frame.Visible = false;

    self.OpenPoint = frame.Position;


    setmetatable(self, InterfaceWindow);
    return self;
end


function InterfaceWindow:Open(...)
    if self._init == false and self.Init then
        self._init = nil;

        self:Init();
    end

    if self.Visible ~= true then
        if self.OpenPoint then
            if self.UseTween ~= false then
                self.Frame.Visible = true;

                local tweenInfo = TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out);
                local tween: Tween = TweenService:Create(self.Frame, tweenInfo, {
                    Position = self.OpenPoint;
                });
                tween:Play();
                
            end
        else
            self.Frame.Visible = true;
        end
        self.Visible = true;
        self.OnToggle:Fire(true, ...);
        
        if modClientGuis.ActiveInterface then
            modClientGuis.ActiveInterface.OnWindowToggle:Fire(self, true, ...);
        end
    end;
end


function InterfaceWindow:Close(...)
    if not self.Visible then return end;

    if self.ClosePoint then
        if self.UseTween ~= false then

            local tweenInfo = TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.In);
            local tween: Tween = TweenService:Create(self.Frame, tweenInfo, {
                Position = self.ClosePoint;
            });
            tween.Completed:Once(function(status)
                if status ~= Enum.TweenStatus.Completed then return end;
                self.Frame.Visible = false;
            end)
            tween:Play();
        end
    else
        self.Frame.Visible = false;
    end
    self.Visible = false;
    self.OnToggle:Fire(false, ...);

    if modClientGuis.ActiveInterface then
        modClientGuis.ActiveInterface.OnWindowToggle:Fire(self, false, ...);
    end
end


function InterfaceWindow:Update(...)
    self.OnUpdate:Fire(...);
end


function InterfaceWindow:Toggle(visible, ...)
    if visible == nil then 
        visible = not self.Visible 
    end;
    if visible then
        self:Open(...);
    else
        self:Close(...);
    end
end


function InterfaceWindow:SetClosePosition(close, open)
    self.ClosePoint = close;
    self.Frame.Position = self.ClosePoint;
    self.Frame.Visible = false;

    if open ~= nil then
        self.OpenPoint = open;
    end
end


function InterfaceWindow:AddCloseButton(parent)
    local hotKeyButton = script:WaitForChild("hotKeyButton"):Clone();
    local closeButton = hotKeyButton:WaitForChild("closeButton");
    local buttonLabel = hotKeyButton:WaitForChild("button");
    hotKeyButton.Parent = parent:WaitForChild("BackgroundFrame");

    self.CloseButtonLabel = buttonLabel;
    closeButton.MouseButton1Click:Connect(function()
        if not self.Visible then return end
        self:Close();
    end)

    local modData = shared.require(localPlayer:WaitForChild("DataModule"));
    if modData:GetSetting("HideHotkey") == 1 then
        hotKeyButton.Visible = false;
    else
        hotKeyButton.Visible = true;
    end

    self.CloseWithInteract = true;
end


function InterfaceWindow:Destroy()
    table.clear(self.Binds);
    self.Properties:Destroy();
    self.OnToggle:Destroy();
    self.OnUpdate:Destroy();
end


-- InterfaceElement
function InterfaceClass:GetOrDefaultElement(name: string, default: anydict)
    local element = self.Elements[name];
    if element == nil and default ~= nil then
        element = InterfaceElement.new(default);
        self.Elements[name] = element;
    end

    return element;
end


function InterfaceElement.new(t)
    local self = modPropertiesVariable.new(t);

    return self;
end



return InterfaceClass;