local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local Types={
	Hold="Hold";
	Trigger="Trigger";
	Message="Message";

	
	Hint="Hint";
	Door="Door";
	Pickup="Pickup";
	Travel="Travel";
	BossExit="BossExit";
	Collectible="Collectible";
	SupplyCrate="SupplyCrate";
	Toggle="Toggle";
	Revive="Revive";
	CardGame="CardGame";
	Seat="Seat";
	Terminal="Terminal";
	InteractProxy="InteractProxy";
	
};

-- Variables;
local Interactables={};
Interactables.__index=Interactables;
Interactables.Types=Types;
Interactables.Cache = {};
Interactables.Remote = nil;

Interactables.TypeIcons={
	["BossDoor_"] = {Icon="rbxassetid://6328469902"; Color=Color3.fromRGB(255, 255, 255)};
	["RogueDoor_"] = {Icon="rbxassetid://6328466558"; Color=Color3.fromRGB(255, 255, 255)};
	["ExtremeDoor_"] = {Icon="rbxassetid://6328526813"; Color=Color3.fromRGB(152, 45, 45)};
	["Travel_"] = {Icon="rbxassetid://3694599252"; Color=Color3.fromRGB(255, 255, 255)};
	["TravelLock_"] = {Icon="rbxassetid://3694599252"; Color=Color3.fromRGB(255, 255, 255)};
	["Shop_"] = {Icon="http://www.roblox.com/asset/?id=4629984614"; Color=Color3.fromRGB(255, 255, 255)};
	["RaidSolo_"] = {Icon="http://www.roblox.com/asset/?id=6328469902";  Color=Color3.fromRGB(255, 255, 255)};
	["Raid_"] = {Icon="http://www.roblox.com/asset/?id=6328528439"; Color=Color3.fromRGB(255, 255, 255)};
	["RaidBandit_"] = {Icon="http://www.roblox.com/asset/?id=6361537388"; Color=Color3.fromRGB(255, 255, 255)};
	["Survival_"] = {Icon="http://www.roblox.com/asset/?id=6328528439"; Color=Color3.fromRGB(152, 45, 45)};
	["Coop_"] = {Icon="http://www.roblox.com/asset/?id=13336462553"; Color=Color3.fromRGB(255, 255, 255)};
};

Interactables.InstanceList = {};
Interactables.Db = {};

local RunService = game:GetService("RunService");
local SoundService = game:GetService("SoundService");
local CollectionService = game:GetService("CollectionService");

local localPlayer = game.Players.LocalPlayer;

local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modItemsLibrary = shared.require(game.ReplicatedStorage.Library.ItemsLibrary);
local modBranchConfigs = shared.require(game.ReplicatedStorage.Library.BranchConfigurations);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modTableManager = shared.require(game.ReplicatedStorage.Library.TableManager);
local modBitFlags = shared.require(game.ReplicatedStorage.Library.BitFlags);
local modScheduler = shared.require(game.ReplicatedStorage.Library.Scheduler);
local modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);
local modTables = shared.require(game.ReplicatedStorage.Library.Util.Tables);

local InteractablePermissions = modBitFlags.new();
InteractablePermissions:AddFlag("CanInteract", "Can interact");

local remotes = game.ReplicatedStorage.Remotes;
local remotePickUpRequest = remotes.Interactable.PickUpRequest;
local remoteWorldTravelRequest = remotes.Interactable.WorldTravelRequest;
local remoteOnTrigger = remotes.Interactable.OnTrigger;
local bindLeavingBossArena = remotes.BossInterface.LeavingBossArena;

local IdCounter = 1;

Interactables.OnCancel = nil;
Interactables.ActionSource = {
	Client = "Client";
	Server = "Server";
};
Interactables.Premade = {};
Interactables.InteractablePermissions = InteractablePermissions;

Interactables.InteractableScheduler = modScheduler.new("Interactables", 1);

local remoteInteractableSync;
local remoteInteractionUpdate;
local remoteInteractableToggle;
local remoteEnterDoorRequest;
local remoteCardGame;
local remoteCharacterInteractions;
-- Script;


local function acquireInteractable(obj)
	if obj:IsA("ModuleScript") and obj.Name == "Interactable" then
		CollectionService:AddTag(obj, "Interactable");
	end
end


function Interactables.onRequire()
	local remoteInteractService = modRemotesManager:Get("InteractService");
	Interactables.Remote = remoteInteractService;

	remoteInteractableSync = modRemotesManager:Get("InteractableSync");
	remoteInteractionUpdate = modRemotesManager:Get("InteractionUpdate");
	remoteInteractableToggle = modRemotesManager:Get("InteractableToggle");
	remoteEnterDoorRequest = modRemotesManager:Get("EnterDoorRequest");
	remoteCardGame = modRemotesManager:Get("CardGame");
	remoteCharacterInteractions = modRemotesManager:Get("CharacterInteractions");

	if RunService:IsServer() then
		remoteInteractionUpdate.OnServerEvent:Connect(function(player, moduleScript, parentPart, action)
			local classPlayer = shared.modPlayers.get(player);
			if action == "stop" then
				local interactData = classPlayer.ActiveInteract
				
				if interactData then
					if interactData.Type ==  "Hold" then
						interactData.Active = false;
						
						if interactData.OnHoldUpdate then
							interactData:OnHoldUpdate(player)
						end
					end
				end
				
				classPlayer.ActiveInteract = nil;
				return;
			end
			
			-- Script sanity check;
			if moduleScript == nil or typeof(moduleScript) ~= "Instance" or not moduleScript:IsA("ModuleScript") or moduleScript.Name ~= "Interactable" then 
				return;
			end;
			
			-- Object sanity check;
			if parentPart == nil or (not moduleScript:IsDescendantOf(parentPart) and not parentPart:IsDescendantOf(moduleScript.Parent)) then 
				Debugger:Warn("Invalid Interact Part", parentPart); 
				return;
			end;
			
				
			local interactData = shared.saferequire(player, moduleScript);
			if interactData == nil then return end;
			
			interactData.Script = moduleScript;
			interactData.Object = parentPart;
			interactData.Part = parentPart;
			
			if action == "start" then
				classPlayer.ActiveInteract = interactData;
				
				if interactData.Type ==  "Hold" then
					interactData.Active = true;
					
					if interactData.OnHoldUpdate then
						interactData:OnHoldUpdate(player)
					end
				end
				
			elseif action == "trigger" then
				if interactData.OnServerTrigger then
					interactData:OnServerTrigger(player);
				end
				
			end
		end)
		
		remoteInteractableSync.OnServerEvent:Connect(function(player, action, ...)
			if action == "syncall" then
				local interactObj = ...;
				if typeof(interactObj) ~= "Instance" or interactObj.Name ~= "Interactable" then 
					return;
				end;
				if interactObj.Parent == nil then return end;

				local interactable: InteractableInstance;

				if interactObj:IsA("ModuleScript") then -- deprecated block
					interactable = shared.saferequire(player, interactObj);
					if interactable == nil then return end;
					interactable.Script = interactObj;
					interactable:Sync({player});
					return;
				end

				interactable = Interactables.getOrNew(interactObj :: Configuration);
				interactable:Sync({player});

			elseif action == "reqsync" then
				local config, lastDataChanged = ...;
				if typeof(config) ~= "Instance" or config.Name ~= "Interactable" or not config:IsA("Configuration") then 
					return;
				end;
				if config.Parent == nil then return end;
				
				local interactable: InteractableInstance = Interactables.getOrNew(config);
				if interactable and lastDataChanged ~= interactable.LastDataChanged then
					interactable:Sync({player});
				end

			elseif action == "reqperm" then
				local config, lastPermSync = ...;
				if typeof(config) ~= "Instance" or config.Name ~= "Interactable" or not config:IsA("Configuration") then 
					return;
				end;
				if config.Parent == nil then return end;
				
				local interactable: InteractableInstance = Interactables.getOrNew(config);
				if interactable and lastPermSync ~= interactable.LastPermChanged then
					interactable:SyncPerms(player);
				end
			end
			
		end)

		--MARK: InteractService
		function remoteInteractService.OnServerInvoke(player, action, ...)
			if remoteInteractService:Debounce(player) then return end;

			if action == "interact" then
				local config: Configuration, values: anydict = ...;
				if config == nil or typeof(config) ~= "Instance" or not config:IsA("Configuration") then
					return;
				end

				local interactable: InteractableInstance = Interactables.getOrNew(config);

				local interactPart = interactable.Part;
				if interactPart == nil or not workspace:IsAncestorOf(interactPart) then 
					Debugger:Warn(`{player.Name} interacted with missing interactPart. {config:GetFullName()}`);
					return;
				end

				local distFromInteractable = player:DistanceFromCharacter(interactPart.Position);
				if distFromInteractable > (interactable.InteractableRange or 15) then
					Debugger:Warn(`{player.Name} interacted too far from interactable. {distFromInteractable} > {interactable.InteractableRange}`);
					return;
				end;

				local canInteract = interactable:HasPermissions("CanInteract", player.Name);
				if canInteract == false then
					Debugger:Warn(`{player.Name} does not have permissions to interact. {config:GetFullName()}`);
					return;
				end

				Interactables.serverInteract(config, {
					Player = player;
					Values = values;
				});
			end

			return;
		end
		
		if workspace.Environment:FindFirstChild("Game") then
			workspace.Environment.Game.ChildAdded:Connect(function(child)
				for _, obj in pairs(child:GetDescendants()) do
					acquireInteractable(obj);
				end
			end)
		end
		
		workspace.Interactables.ChildAdded:Connect(function(child)
			for _, obj in pairs(child:GetDescendants()) do
				acquireInteractable(obj);
			end
		end)

		shared.modEngineCore:ConnectOnPlayerAdded(script, function(player: Player)
			Interactables.clearOldInstance();

		end)
		shared.modEngineCore:ConnectOnPlayerRemoved(script, function(player: Player)
			Interactables.clearOldInstance();
		end)

	elseif RunService:IsClient() then
		remoteInteractableSync.OnClientEvent:Connect(function(action, config, ...)
			if action == "sync" then
				local data = ...;
				if config == nil or not workspace:IsAncestorOf(config) then return end;
				if config.Name ~= "Interactable" then return end;

				if config:IsA("ModuleScript") then -- deprecated block
					local interact = shared.require(config);
					if interact.OnSync then
						interact:OnSync(data);
						
						if config:GetAttribute("Debug") == true then
							Debugger:Warn(config:GetFullName(), " ManualSynced.");
						end
						
					else
						for k, v in pairs(data) do
							interact[k] = v;
						end
						if config:GetAttribute("Debug") == true then
							Debugger:Warn(config:GetFullName(), " AutoSynced.");
						end
						
					end
					return;
				end

				local interactable: InteractableInstance = Interactables.getOrNew(config);
				if interactable == nil then return end;

				interactable.LastDataChanged = data.LastDataChanged;

				if interactable.BindSync then
					interactable:BindSync(data);

				else
					interactable.Label = data.Label;
					interactable.CanInteract = data.CanInteract;
					if data.Values then
						for k, v in pairs(data.Values) do
							interactable.Values[k] = v;
						end
					end

				end


			elseif action == "syncperm" then
				local lastPermChanged, userBitString = ...;
				Debugger:StudioWarn(`Sync perm {config:GetFullName()}`, userBitString);
				local interactable: InteractableInstance = Interactables.getOrNew(config);
				if interactable == nil then return end;

				interactable.LastPermChanged = lastPermChanged;

				interactable.UserBitString[localPlayer.Name] = userBitString;
				config:SetAttribute("_UserBitString", userBitString);
				
			end
		end)

	end

	local function cmdListInteractables(player, args)
		local action = args[1];

		if action == "a" then
			local testButtonInteractable = workspace.Interactables.newButtonTest.Interactable;
			if testButtonInteractable then
				Interactables.serverInteract(
					testButtonInteractable,
					{Player = player}
				);
			end

		elseif action == "find" then
			local filter = args[2];

			local matchList = {};
			for config, interactable: InteractableInstance in pairs(Interactables.InstanceList) do
				local exist = false;
				for a=1, #matchList do
					if matchList[a] == interactable then
						exist = true;
					end
				end
				if exist then continue end;

				if interactable.Name:match(filter) then
					table.insert(matchList, interactable);
				else
					for k, v in pairs(interactable.Values) do
						if (typeof(k) == "string" and k:match(filter)) 
						or (typeof(v) == "string" and v:match(filter)) then
							table.insert(matchList, interactable);
						end
					end
				end
			end
			if #matchList == 1 then
				print(matchList[1]);
			else
				print(#matchList, matchList);
			end

		else
			Debugger:Warn(`Printing all interactables.`);
			local c = 0;
			for _, _ in pairs(Interactables.InstanceList) do
				c = c +1;
			end
			print(c, Interactables.InstanceList);

		end

		return;
	end
	shared.modCommandsLibrary.bind{
		["interactables"]={
			Permission = shared.modCommandsLibrary.PermissionLevel.DevBranch;
			Description = [[List all my interactables;
			/interactables [arg]
			]];

			RequiredArgs = 0;
			UsageInfo = "/interactables";
			Function = cmdListInteractables;
			ClientFunction = cmdListInteractables;
		}
	};

	for _, obj in pairs(script:GetChildren()) do
		if not obj:IsA("ModuleScript") then continue end;
		
		task.spawn(Interactables.loadInteractablePackages, obj);
	end
end

function Interactables.clearOldInstance()
	for config, instance in pairs(Interactables.InstanceList) do
		local destroy = false;

		if not game:IsAncestorOf(config) then
			destroy = true;
		end

		if destroy then
			if instance.Package.BindDestroy then
				instance.Package.BindDestroy(instance);
			end
			Interactables.InstanceList[config] = nil;
		end
	end
end

function Interactables.createInteractable(name, variant)
	local new = Instance.new("Configuration");
	new.Name = "Interactable";
	new:AddTag("Interactable");
	new:SetAttribute("_Name", name);
	if variant then
		new:SetAttribute("_Variant", variant);
	end
	return new;
end

function Interactables.loadInteractablePackages(moduleScript)
	if not moduleScript:IsA("ModuleScript") then return end;
	
	local interactablePackage = shared.require(moduleScript);

	if interactablePackage.init then
		interactablePackage.init(Interactables);
	end
end


--MARK: loadInteractableConfig
function Interactables.getOrNew(config: Configuration)
	if not config:IsA("Configuration") then return end;

	local name = config:GetAttribute("_Name");
	if name == nil then
		Debugger:Warn(`Interactable is missing name {config:GetFullName()}`);
	end

	local package = Interactables.Db[name];

	local interactInstance: InteractableInstance = Interactables.InstanceList[config];
	if interactInstance == nil then
		interactInstance = Interactables:Instance(name, config);
		Interactables.InstanceList[config] = interactInstance;

		config.Destroying:Once(function()
			Debugger:Warn(`Interactable config destroying:`, config:GetFullName());
			Interactables.InstanceList[config] = nil;
			Interactables.clearOldInstance();
		end)
	end

	if interactInstance.Part then
		local cframe = interactInstance.Part.CFrame;
		config:SetAttribute("_Point", cframe.Position);
	end

	return interactInstance;
end

--MARK: newProxy
function Interactables.newProxy(typeName)
	local interact = Interactables.new();

	local interactMeta = getmetatable(interact);
	interactMeta.Label = "Use";
	interactMeta.CanInteract = true;

	interact.Type = typeName or "InteractProxy";
	interact.IndicatorPresist = true;
	interact.Player = nil;
	interact.InteractDuration = 6;

    function interactMeta.BindInteract(interactable: InteractableInstance, info: InteractInfo)
		Debugger:Warn(`InteractProxy BindInteract Unimplemented:`,interact.Type);
	end

	function interactMeta.BindPrompt(interactable: InteractableInstance, info: InteractInfo)
		Debugger:Warn(`InteractProxy BindPrompt Unimplemented:`, interact.Type);
	end

	return interact;
end

function Interactables:SyncAll(func)
	if RunService:IsServer() then
		if true then return end;
		if workspace.Environment:FindFirstChild("Game") then
			for _, obj in pairs(workspace.Environment.Game:GetDescendants()) do
				acquireInteractable(obj);
			end
		end
		
		for _, obj in pairs(workspace.Interactables:GetDescendants()) do
			acquireInteractable(obj);
		end
		
		for _, interactableScript in pairs(CollectionService:GetTagged("Interactable")) do
			if interactableScript:IsA("ModuleScript") then
				local interactObject = shared.require(interactableScript);
				interactObject.Script = interactableScript;
				if func then func(interactObject); end
				interactObject:Sync();
			end
		end
	end
end

-- MARK: Sync
function Interactables.sync(config, players, data)
	if RunService:IsClient() then return end;
	if config.Parent == nil then Debugger:Warn(":Sync config.Parent == nil."); return end;

	players = type(players) == "table" and players or {players};
	
	for a=1, #players do
		local player: Player = players[a];
		
		local interactable: InteractableInstance = Interactables.getOrNew(config);

		local syncRange = interactable.Package.SyncRange or 128;
		if syncRange <= 512 then
			local pvInstance = interactable.Part;
		
			if pvInstance == nil then
			elseif pvInstance:IsA("BasePart") then
				if player:DistanceFromCharacter(pvInstance.Position) >= syncRange then
					continue;
				end
				
			else
				while not config:IsA("PVInstance") do
					local findChild = pvInstance:FindFirstChildWhichIsA("PVInstance");
					if findChild then
						pvInstance = findChild;
						break;
					end
					pvInstance = pvInstance.Parent;
					if not workspace:IsAncestorOf(pvInstance) then
						break;
					end
				end

				if pvInstance:IsA("PVInstance") then
					if player:DistanceFromCharacter(pvInstance:GetPivot().Position) >= syncRange then
						continue;
					end
				end
				
			end
		end

		if RunService:IsStudio() then
			Debugger:StudioLog("Interactable Sync: ",config:GetFullName(),
				"(",modRemotesManager.PacketSizeCounter.GetPacketSize{PacketData={data};},")",
				"to", player
			);
		end
		
		remoteInteractableSync:FireClient(player, "sync", config, interactable:Shrink());
	end
	
end

function Interactables.getPartOfInteractableConfig(interactableConfig)
	if interactableConfig.Parent == nil then return end;

	local rObj;

	if interactableConfig.Parent:IsA("BasePart") then
		rObj = interactableConfig.Parent;

	else
		local parent = interactableConfig.Parent;

		if parent:FindFirstChildWhichIsA("Humanoid") then
			rObj = parent:FindFirstChild("UpperTorso");
			if rObj == nil then
				rObj = parent.PrimaryPart;
			end
			
		elseif parent:IsA("Model") and parent.PrimaryPart then
			rObj = parent.PrimaryPart;
			
		else
			parent = parent:FindFirstChildWhichIsA("BasePart");

		end
	end

	if rObj == nil or not rObj:IsA("BasePart") then return end;

	return rObj;
end

--MARK: Instance
function Interactables:Instance(name: string, config: Configuration)
	local package = Interactables.Db[name];
	if package == nil then
		Debugger:Warn(`Attempted to instance non-existent interactable: {name}`);
		return;
	end
	
	local typePackage = nil;
	if package.Type ~= name then
		typePackage = Interactables.Db[package.Type];

		if typePackage == nil then
			error(`Missing interactable package of type: {package.Type} for {name}`);
		end
	end

	local uniqueId = config:GetAttribute("_Id");
	local variant = config:GetAttribute("_Variant");

	local animation = config:GetAttribute("_Animation");
	local label = config:GetAttribute("_Label");
	
	local new = Interactables.new(config);
	new.Name = name;
	new.Variant = variant;
	new.Id = uniqueId;
	new.Animation = animation or package.Animation;
	new.Label = label or package.Label;
	new.LastDataChanged = tick();
	
	local meta = getmetatable(new);
	meta.Package = package;
	meta.TypePackage = typePackage;

	meta.RootBitString = 0;
	meta.UserBitString = {};
	meta.LastPermChanged = tick();

	function meta:SyncPerms(player)
		if self.BindSyncPerms then
			self:BindSyncPerms(player);
		end

		local userBitString = self.UserBitString[player.Name];
		remoteInteractableSync:FireClient(
			player, 
			"syncperm", 
			self.Config,
			self.LastPermChanged,
			userBitString
		);
	end

	function meta:SetPermissions(flagTag: string, value: boolean)
		self.RootBitString = InteractablePermissions:Set(self.RootBitString, flagTag, value);
		self.Config:SetAttribute("_RootBitString", self.RootBitString);
		self.LastPermChanged = tick();
	end

	function meta:HasPermissions(flagTag: string, name: string?): boolean
		local rootBitString = self.RootBitString;

		if RunService:IsClient() then
			rootBitString = self.Config:GetAttribute("_RootBitString") or 0;
		end

		local eventFlag = InteractablePermissions:Test(flagTag, rootBitString);

		local userBitString = name and self.UserBitString[name] or nil;
		if RunService:IsClient() then
			userBitString = self.Config:GetAttribute("_UserBitString");
		end

		if userBitString == nil then return eventFlag; end

		local userFlag = InteractablePermissions:Test(flagTag, userBitString);
		return userFlag;
	end

	function meta:SetUserPermissions(name: string, flagTag: string, value: boolean)
		local userBitString = self.UserBitString[name] or 0;
		userBitString = InteractablePermissions:Set(userBitString, flagTag, value);
		self.UserBitString[name] = userBitString;
		self.LastPermChanged = tick();
	end


	local interactBasePart = Interactables.getPartOfInteractableConfig(config);
	if interactBasePart then
		new.Part = interactBasePart;
	end

	if typePackage then
		if typePackage.new then
			typePackage.new(new);
		end
		if typePackage.Values then
			modTables.CopyFromTo(typePackage.Values, new.Values);
		end
	end

	if package.new then
		package.new(new);
	end
	if package.Values then
		modTables.CopyFromTo(package.Values, new.Values);
	end

	local function updateProperties(k)
		if k:sub(1,1) == "_" then
			local key = k:sub(2, #k);
			if key == "Label" or key == "Animation" then
				new[key] = config:GetAttribute(k);
			end
			return
		end;

		local v = config:GetAttribute(k);
		new.Values[k] = v;
	end

	config.AttributeChanged:Connect(updateProperties);
	for k, v in pairs(config:GetAttributes()) do
		updateProperties(k);
	end

	return new;
end

--MARK: registerPackage
function Interactables.registerPackage(package: anydict)
	local name = package.Name;
	if name == nil then
		Debugger:Warn(`Attempt to register unnamed interactable: {package}`);
		return;
	end

	local db = Interactables.Db;
	if db[name] then
		Debugger:Warn(`Attempt to register already existing interactable: {name}`);
		return;
	end
	db[name] = package;

	if package.EventServiceKey then
		local eventKey = package.EventServiceKey;

		local eventHandler = shared.modEventService:GetOrNewHandler(eventKey, true);
		eventHandler.RelayToServer = false;
		eventHandler.ReplicateToClients = false;
		eventHandler:SetPermissions("CanInvoke", true);
		eventHandler:SetPermissions("CanListen", true);
		eventHandler:SetPermissions("CanCancel", true);

		-- shared.modEventService:OnInvoked(eventKey, function(event: EventPacket, ...)
		-- 	local player: Player?;

		-- 	if event.Source == shared.modEventService.EventSource.Client then
		-- 		if RunService:IsClient() then
		-- 			player = localPlayer;
		-- 		elseif RunService:IsServer() then
		-- 			player = event.Player;
		-- 		end
		-- 	end

		-- 	if player == nil then return end;

		-- 	local config: Configuration, values: anydict = ...;

		-- 	local interactable: InteractableInstance = Interactables.getOrNew(config);

        --     local interactPart = interactable.Part;
		-- 	local distFromInteractable = interactPart and player:DistanceFromCharacter(interactPart.Position) or -1;
        --     if distFromInteractable > (interactable.InteractableRange or 15) then
		-- 		event:SetCancelled(true);
		-- 		return;
		-- 	end;

		-- 	local canInteract = interactable:HasPermissions("CanInteract", player.Name);
		-- 	if canInteract == false then
		-- 		event:SetCancelled(true);
		-- 		return;
		-- 	end

		-- 	if RunService:IsClient() then return end;

		-- 	modTables.DictMerge(values, event.Returns);
		-- 	Interactables.serverInteract(config, {
		-- 		Player = event.Player;
		-- 		Values = values;
		-- 	});
		-- end, 1);
	end
end

function Interactables.new(config: Configuration, prefab: (Actor | Model)?): (InteractableInstance, InteractableMeta)
	local interactMeta = {};
	interactMeta.Package = nil;
	interactMeta.TypePackage = nil;

	function interactMeta:__index(k)
		if k == "Remote" then return Interactables.Remote end;
		if k == "TypePackage" then
			if interactMeta.Package.Type == nil then
				error(`Can not access TypePackage without setting a Type in package: {interactMeta.Package.Name}`);
			elseif interactMeta.Package.Name == interactMeta.Package.Type then
				error(`Can not access TypePackage because type is same as Package: {interactMeta.Package.Name}`);
			else
				return interactMeta.TypePackage;
			end
		end
		local v = rawget(self, k);
		if v ~= nil then
			return v;
		end

		local metaV = interactMeta[k];
		if metaV ~= nil then
			return metaV;
		end

		if interactMeta.Package then
			local pV = interactMeta.Package[k];
			if pV ~= nil then
				return pV;
			end
		end

		if interactMeta.TypePackage then
			local pV = interactMeta.TypePackage[k];
			if pV ~= nil then
				return pV;
			end
		end

		return;	
	end
	
	interactMeta.CanInteract = false;
	interactMeta.LastSync = tick();
	interactMeta.LastCheckPerms = tick();
	interactMeta.LastServerTrigger = tick();
	
	function interactMeta:RemoteTriggerEvent()
		if RunService:IsServer() then return end;
		
		if tick()-self.LastServerTrigger >= 1 then
			self.LastServerTrigger = tick();
			remoteInteractionUpdate:FireServer(self.Script, self.Part, "trigger");
		end
	end
	
	-- MARK: self:SyncRequest()
	function interactMeta:SyncRequest()
		if RunService:IsServer() then return end;
		
		if tick()-self.LastSync >= 2 then
			self.LastSync = tick();
			remoteInteractableSync:FireServer("reqsync", self.Config, self.LastDataChanged);

		elseif tick()-self.LastCheckPerms >= 0.5 then
			self.LastCheckPerms = tick();
			remoteInteractableSync:FireServer("reqperm", self.Config, self.LastPermChanged);
			
		end
	end
	
	-- MARK: self:Sync(players, data)
	function interactMeta:Sync(players, data)
		if self.Config == nil then Debugger:Warn("Missing Interactable Config."); return end;
		if data ~= nil then
			Debugger:StudioLog(`deprcated use of Interactable:Sync data=`, data);
		end

		Interactables.sync(self.Config, players or game.Players:GetPlayers());
	end
	
	function interactMeta:Shrink()
		local data = {
			LastDataChanged = self.LastDataChanged;
			CanInteract = self.CanInteract;
			Label = self.Label;
			Values = self.Values;
		};
		return data;
	end

	function interactMeta:SetMeta(k, v)
		interactMeta[k] = v;
	end
	
	function interactMeta:Proximity()
		if self.OnProximity then
			self:OnProximity();
		end

		-- if self.ProximityTriggerTick == nil or tick()-self.ProximityTriggerTick >= 60 then
		-- 	self.ProximityTriggerTick = tick();
		-- 	self:Trigger();
		-- end
	end

	--MARK: InteractServer
	function interactMeta:InteractServer(values)
		task.spawn(function()
			Interactables.Remote:InvokeServer("interact", self.Config, values);
		end)
	end

	--MARK: Trigger
	function interactMeta:Trigger()
		self:RemoteTriggerEvent();
		
		local metaSelf = getmetatable(self);
		
		local library = {};
		library.modCharacter = self.CharacterModule;
		library.modData = shared.require(localPlayer:WaitForChild("DataModule") :: ModuleScript);

		library.interface = library.modData:GetInterfaceModule();
		if library.interface == nil then Debugger:Warn("Missing library.interface"); return end;
		
		if self.InspectMode == true then
			self.Disabled = "Disabled in Inspect Mode";
			return;
		else
			self.Disabled = nil;
		end
		
		if self.Premium == true then
			local isPremium = library.modData and library.modData.Profile and library.modData.Profile.Premium == true or false;
			
			if isPremium then
				self.Disabled = nil;
			else
				self.Disabled = "Requires Premium";
				return;
			end
			
		else
			self.Disabled = nil;
		end
		
		if self.LevelRequired then
			local localplayerStats = library.modData.GetStats();
			
			if localplayerStats and localplayerStats.Level and localplayerStats.Level >= self.LevelRequired then
				
				self.Disabled = nil;
			else
				self.Disabled = ("Mastery Level $lvl Required"):gsub("$lvl", self.LevelRequired);
				return;
			end
		end
		
		if self.MissionRequired then
			local modData = library.modData;
			
			local missionId = self.MissionRequired.Id;
			local missionType = self.MissionRequired.Type or {3};
			local missionPoint = self.MissionRequired.ProgressionPoint;
			
			local modMissionLibrary = shared.require(game.ReplicatedStorage.Library.MissionLibrary);
			local missionLib = modMissionLibrary.Get(missionId);
			
			local missionFulfilled = false;
			
			if modData.GameSave and modData.GameSave.Missions then
				local missionsList = modData.GameSave.Missions;
				for a=1, #missionsList do
					local missionData = missionsList[a];
					if missionData.Id ~= missionId then continue end;
					
					if table.find(missionType, missionData.Type) == nil then continue; end
					if missionPoint and table.find(missionPoint, missionData.ProgressionPoint) == nil then continue; end
					
					missionFulfilled = true;
					break;
				end
			end
			
			if missionFulfilled then
				self.Locked = false;
				self.Disabled = nil;
				
			else
				self.Locked = true;
				if missionLib then
					self.Disabled = "Mission \"".. missionLib.Name .."\" required to access.";
				else
					self.Disabled = "Mission in development for accessing this area.";
				end
				return;
			end
		end
		
		if self.ItemRequired and self.ItemRequired ~= "" then
			if self.ItemRequired ~= (self.CharacterModule.EquippedItem and self.CharacterModule.EquippedItem.ItemId) then
				local itemLib = modItemsLibrary:Find(self.ItemRequired);
				
				self.Disabled = "Requires a "..(itemLib.Name or "Unknown Item");
				return;
			else
				self.Disabled = nil;
			end
		end
		
		local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);
		if playerClass.Properties.InBossBattle ~= nil and self.Type ~= "GameModeExit" then
			self.Disabled = "Cant Interact";
			return;
			
		else
			self.Disabled = nil;
			
		end
		
		self:SyncRequest();

		if metaSelf.OnTrigger then
			metaSelf.OnTrigger(self, library);
		end
		if self.OnTrigger then
			self:OnTrigger(library);
		end

		local interactInfo: InteractInfo = {
			ActionSource = Interactables.ActionSource;
			ClientData = library.modData;
			CharacterVars = self.CharacterModule;
			
			Player = localPlayer;
			Values = {};
		};
		if self.BindPrompt then
			self.BindPrompt(self, interactInfo);
		end
	end
	
	--MARK: Interact
	function interactMeta:Interact()
		if self.Disabled then return end;
		if self.CanInteract == false then return end;
		if self.Coop then return end;
		
		local modData = shared.require(localPlayer:WaitForChild("DataModule") :: ModuleScript);
		
		local library = {};
		library.modCharacter = self.CharacterModule;
		library.modData = modData;
		library.interface = modData:GetInterfaceModule();
		
		if self.LevelRequired then
			local localplayerStats = library.modData.GetStats();
			
			if localplayerStats and localplayerStats.Level and localplayerStats.Level < self.LevelRequired then
				return;
			end
		end
		
		if self.Prompt then
			local promptWindow = modClientGuis.promptQuestion(self.Prompt.Title, self.Prompt.Description);
			local YesClickedSignal, NoClickedSignal;

			YesClickedSignal = promptWindow.Frame.Yes.MouseButton1Click:Connect(function()
				library.interface:PlayButtonClick();
				promptWindow:Close();

				if self.OnInteracted then
					self:OnInteracted(library);
				end
				
				YesClickedSignal:Disconnect();
				NoClickedSignal:Disconnect();
			end);
			NoClickedSignal = promptWindow.Frame.No.MouseButton1Click:Connect(function()
				library.interface:PlayButtonClick();
				promptWindow:Close();
				YesClickedSignal:Disconnect();
				NoClickedSignal:Disconnect();
			end);
			
		else
			if self.OnInteracted then
				self:OnInteracted(library);
			end
			
			local interactInfo: InteractInfo = {
				ActionSource = Interactables.ActionSource;
				ClientData = library.modData;
				CharacterVars = self.CharacterModule;
				
				Action = Interactables.ActionSource.Client;
				Player = localPlayer;
				Values = {};
			};
			if self.BindInteract then
				self.BindInteract(self, interactInfo);
			end
			
		end
		
		
		return true;
	end

	function interactMeta:Disable()
		self.ShowIndicator = false;
		self.CanInteract = false;
	end
	
	function interactMeta:Enable()
		self.ShowIndicator = true;
		self.CanInteract = true;
	end

	interactMeta.Debounce = {};
	function interactMeta:CheckDebounce(name, duration)
		local t = tick();
		if interactMeta.Debounce[name] and t-interactMeta.Debounce[name] <= (duration or 0.1) then return false end;
		interactMeta.Debounce[name] = t;
		for k,_ in pairs(interactMeta.Debounce) do
			if game.Players:FindFirstChild(k) == nil then
				interactMeta.Debounce[k] = nil;
			end
		end
		return true;
	end
	
	function interactMeta:Change()
		
	end
	
	function interactMeta:Destroy()
		self = nil;
	end

	local interact = setmetatable({}, interactMeta);
	interact.CountId = IdCounter;
	IdCounter = IdCounter+1;
	
	local valuesMeta = {};
	function valuesMeta.__index(t, k)
		if k:sub(1,1) == "_" then return nil; end;

		local v = rawget(t, k);
		if v ~= nil then return v; end;
		
		local config = rawget(t, "Config");
		if config == nil then return nil; end; 
		
		return config:GetAttribute(k);
	end

	interact.Values = setmetatable({}, valuesMeta);
	interact.Script = config;
	interact.Prefab = prefab;

	if typeof(config) == "Instance" and config:IsA("Configuration") then
		interact.Config = config;
	end

	return interact, interactMeta;
end

function Interactables.serverInteract(config: Configuration, params)
	params = params or {};

	local interactable: InteractableInstance = Interactables.getOrNew(config);
		
	local interactInfo: InteractInfo = {
		ActionSource = Interactables.ActionSource;
		Action = Interactables.ActionSource.Server;

		Player = params.Player;
		NpcClass = params.NpcClass;
		Values = params.Values or {};
	};

	if interactable.BindPrompt then
		interactable.BindPrompt(interactable, interactInfo);
	end

	if interactable.CanInteract == false then return end;

	if interactable.BindInteract == nil then return end;
	interactable.BindInteract(interactable, interactInfo);
end

function Interactables.Hold(src, label)
	local interact = Interactables.new(src);
	local interactMeta = getmetatable(interact);
	interactMeta.Label = label or "";
	interactMeta.CanInteract = true;
	
	interact.InteractableRange = 20;
	interact.CaptureHold = 8;
	interact.Script = src;
	interact.Type = Types.Hold;
	interact.Active = false;
	interact.Label = nil;
	
	return interact;
end

function Interactables.Message(msg)
	local interact = Interactables.new();
	local interactMeta = getmetatable(interact);
	interactMeta.Label = msg or "Enter Door";
	
	interact.Type = Types.Message;
	return interact;
end

function Interactables.Hint(src, msg, paramPacket)
	local interactObj = Interactables.new(src, nil);
	local interactMeta = getmetatable(interactObj);
	interactMeta.Label = msg or "Message";

	interactObj.Script = src;
	
	paramPacket = paramPacket or {};
	if paramPacket.Blacklight and not CollectionService:HasTag(src, "BlacklightInteractables") then
		CollectionService:AddTag(src, "BlacklightInteractables");
		
		interactObj.InteractableRange = 0;
		
		local blacklightGui = src.Parent:FindFirstChild("BlacklightHint");
		if blacklightGui then
			for _, textLabel in pairs(blacklightGui:GetChildren()) do
				if not textLabel:IsA("TextLabel") then continue end;

				textLabel.TextTransparency = 1;
				textLabel.Text = interactObj.Label;
			end
		end
	end
	
	function interactMeta:OnTrigger()
	end

	interactObj.Type = Types.Hint;
	return interactObj;
end

function Interactables.Door(locked, label, premium)
	local modDoors = shared.require(game.ReplicatedStorage.Library.Doors);
	
	local interact = Interactables.new();
	local interactMeta = getmetatable(interact);
	interactMeta.Label = label or "Enter Door";
	interactMeta.CanInteract = true;
	
	interact.Type = Types.Door;
	interact.Locked = locked == true;
	interact.Label = nil;
	interact.EnterSound = "DoorClose";
	interact.InteractableRange = 10;
	interact.Premium = premium == true;
	
	interact.Animation = "OpenDoor";
	
	if locked then
		interact.CanInteract = false;
		interact.Label = interact.LockedLabel or "Door's locked";
	end
	
	function interactMeta:OnTrigger()
		if self.Locked == true then
			interact.CanInteract = false;
			interact.Label = self.LockedLabel or "Door's locked";
			return;
		else
			interact.CanInteract = true;
			interact.Label = nil;
		end
		
		if self.Part and self.Part:FindFirstChild("Blockade") ~= nil then
			interact.CanInteract = false;
			interact.Label = "Door's blocked";
			return;
		end

		local doorInstance = modDoors:GetDoor(self.Part and self.Part.Parent);
		if doorInstance then
			if not doorInstance:HasAccess(localPlayer) then
				interact.CanInteract = false;
				interact.Label = self.LockedLabel or "Door's locked";
				return;
			end
			
			if doorInstance.Prefab:FindFirstChild("Blockade") ~= nil then
				interact.CanInteract = false;
				interact.Label = "Door's blocked";
				return;
			end

			local stateRequired = doorInstance.Prefab:GetAttribute("StateRequired");
			if stateRequired then
				interact.CanInteract = false;
				interact.Label = stateRequired;
				return;
				
			end
			
			local keyItemId = doorInstance.Prefab:GetAttribute("KeyRequired");
			if keyItemId then
				local itemLib = modItemsLibrary:Find(keyItemId);

				local modData = shared.require(localPlayer:WaitForChild("DataModule") :: ModuleScript);
				if modData.FindItemIdFromCharacter(keyItemId) then
					interact.Label = "Unlock with ".. (itemLib and itemLib.Name or "unknown item");
					
				else
					interact.CanInteract = false;
					interact.Label = "Requires a ".. (itemLib and itemLib.Name or "unknown item");
					return;
					
				end
			end
			
			local isOpen = doorInstance.Script:GetAttribute("DoorOpen");
			interactMeta.Label = (isOpen and "Close" or "Open").." Door";
			
			if self.Label:match("Door") then
				
				if doorInstance.Type == "Sliding" then
					self.Animation = "Press";
					return;
				end
				
				if isOpen then
					self.Animation = "CloseDoor"; -- doorObject.DoorsCount >= 2 and "DCloseDoor" or
				else
					self.Animation = "OpenDoor"; -- doorObject.DoorsCount >= 2 and "DOpenDoor" or 
				end
			end
		end
	end
	
	function interact:OnInteracted(library)
		local doorObject = modDoors:GetDoor(self.Part and self.Part.Parent);
		if doorObject then
			if not self.Locked then
				doorObject:RequestDoorToggle();
			end
			
		else
			local destination = self.Part and self.Part:FindFirstChild("Destination")
			
			if self.Script and self.Script:FindFirstChild("CustomDestination") then
				destination = self.Script.CustomDestination.Value;
			end
			
			if destination == nil then
				warn("Door missing destination.");
			end
			
			if not self.Locked then
				if self.RootPart ~= nil and self.RootPart.Parent ~= nil then
					library.modCharacter.CharacterProperties.IsCrouching = false;
					library.modCharacter.StopSliding();
					remoteEnterDoorRequest:InvokeServer(self.Script);
					
				else
					warn("Missing character root part.");
					
				end
			end
			
		end
	end
	
	if RunService:IsServer() then
		--MARK: TODO delete when fully migrated
		
		function remoteEnterDoorRequest.OnServerInvoke(player, interactConfig)
			if remoteEnterDoorRequest:Debounce(player) then return end;
			if interactConfig == nil then return "Invalid interact object."; end
			
			local playerClass: PlayerClass = shared.modPlayers.get(player);

			if not interactConfig:IsA("ModuleScript") then return end;
			
			local modOnGameEvents = shared.require(game.ServerScriptService.ServerLibrary.OnGameEvents);

			local interactData = shared.saferequire(player, interactConfig);
			if interactData == nil then return "Invalid interact object." end;

			if not interactData:CheckDebounce(player.Name) then return "Interactable is on cooldown." end;
			local interactPart = interactData.Part;

			local distanceFromDoor = interactPart and player:DistanceFromCharacter(interactPart.Position) or -1;
			if interactPart == nil or distanceFromDoor > (interactData.MaxEnterDistance or 20) then Debugger:Warn(player.Name,"Too far from door. ("..distanceFromDoor..")"); return "Too far from object."; end;
			if interactPart and interactConfig then
				local profile = shared.modProfile:Get(player);
				
				interactData.Object = interactPart;
				local doorName = interactData.Name;
				local premiumOnly = interactData.Premium;
				
				if premiumOnly and not profile.Premium then return "Not Premium"; end
				
				local destination = interactPart:FindFirstChild("Destination")

				if interactConfig:FindFirstChild("CustomDestination") then
					destination = interactConfig.CustomDestination.Value;
				end
				if destination then
					local sound = modAudio.Play(interactData.EnterSound, destination); 
					if sound then sound.PlaybackSpeed = math.random(7, 12)/10; end;
					
					local tpCframe = CFrame.new(destination.WorldPosition + Vector3.new(0, 2.35, 0)) 
									* CFrame.Angles(0, math.rad(destination.WorldOrientation.Y-90), 0);
					playerClass:SetCFrame(tpCframe);
				end
				
				modOnGameEvents:Fire("OnDoorEnter", player, interactData);
			else
				return "Invalid interact object.";
			end


			return;
				
		end

	end

	return interact;
end

function Interactables.Travel(worldId, label, premium)
	local interact = Interactables.new();
	local interactMeta = getmetatable(interact);
	interactMeta.Label = label or "Enter Door";
	interactMeta.CanInteract = true;
	
	interact.Type = Types.Travel;
	interact.WorldId = worldId;
	interact.Premium = premium == true;
	
	if modBranchConfigs.CurrentBranch.Worlds[worldId] == nil then
		Debugger:Log("World",worldId,"does not exist.");
		interact.CanInteract = false;
		interact.Label = "Work In Progress";
	end
	
	function interact:OnInteracted(library)
		if self.Disabled then return end;
		local worldName = self.WorldId and modBranchConfigs.GetWorldDisplayName(self.WorldId) or self.WorldId;
				
		local modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);
		local promptWindow = modClientGuis.promptQuestion("You are about to leave this world", "Are you sure you want to travel to "..worldName.."?");
		local YesClickedSignal, NoClickedSignal;
		
		local function exitPrompt()
			library.interface:ToggleGameBlinds(true, 1);
			SoundService:SetListener(Enum.ListenerType.ObjectCFrame, library.modCharacter.RootPart);
			library.modCharacter.CharacterProperties.CanMove = true;
			library.modCharacter.CharacterProperties.CanInteract = true;
		end
		
		YesClickedSignal = promptWindow.Frame.Yes.MouseButton1Click:Connect(function()
			library.interface:PlayButtonClick();
			library.interface:ToggleGameBlinds(false, 3);
			promptWindow:Close();
			library.modCharacter.CharacterProperties.CanMove = false;
			library.modCharacter.CharacterProperties.CanInteract = false;
			local success = remoteWorldTravelRequest:InvokeServer("Interact", self.Script);
			if success then
				SoundService:SetListener(Enum.ListenerType.CFrame, CFrame.new(0, 1000, 0));
			else
				exitPrompt();
			end
			YesClickedSignal:Disconnect();
			NoClickedSignal:Disconnect();
		end);
		NoClickedSignal = promptWindow.Frame.No.MouseButton1Click:Connect(function()
			library.interface:PlayButtonClick();
			promptWindow:Close();
			exitPrompt();
			YesClickedSignal:Disconnect();
			NoClickedSignal:Disconnect();
		end);
	end
	
	return interact;
end

function Interactables.BossExit()
	local interact = Interactables.new();
	interact.Type = Types.BossExit;
	interact.Label = "Door's Locked";
	interact.CanInteract = false;
	interact.OnTrigger = function(self)
		if self.Part and self.Part:FindFirstChild("ExitBossArena") then
			interact.Label = "Enter Door";
			interact.CanInteract = true;
		end
	end
	
	function interact:OnInteracted()
		if self.Part:FindFirstChild("ExitBossArena") then
			self.Part.ExitBossArena:InvokeServer(self.Part);
			bindLeavingBossArena:Fire();
		end
	end
	
	return interact;
end

function Interactables.Pickup(itemId, quantity)
	local itemLib = modItemsLibrary:Find(itemId);
	local interact = Interactables.new();
	local interactMeta = getmetatable(interact);
	interactMeta.Label = "Pick up "..(itemLib and itemLib.Name or itemId)..((quantity or 1) > 1 and " x"..quantity or "");
	interactMeta.CanInteract = true;
	interactMeta.TouchInteract = true;
	
	interact.Type = Types.Pickup;
	interact.ItemId = itemId;
	interact.Quantity = quantity or 1;
	
	interact.PickupCooldown = nil;

	function interact:SetQuantity(quantity)
		self.Quantity = quantity or 1;
		if self.Script then
			self.Script:SetAttribute("Quantity", quantity);
		end
		interactMeta.Label = "Pick up "..(itemLib and itemLib.Name or itemId)..((quantity or 1) > 1 and " x"..quantity or "");
	end
	
	function interact:OnSync(data)
		self.LevelRequired = data.LevelRequired or self.LevelRequired;
		self:SetQuantity(data.Quantity or self.Quantity);
	end
	
	function interact:OnInteracted(library)
		if self.PickupCooldown and tick()-self.PickupCooldown <= 1 then return end;
		self.PickupCooldown = tick();

		local interactPart = self.Part;
		if not interactPart:IsDescendantOf(workspace) then return end;
		
		local objectParent = interactPart.Parent;
		
		interactPart.Parent = game.ReplicatedStorage;
		if objectParent:FindFirstChild("BillboardGui") then
			objectParent.BillboardGui.Enabled = false;
		end
		local invokeRequest, partialPickup = remotePickUpRequest:InvokeServer(interactPart, self.Script);

		if invokeRequest == true or partialPickup == true then
			if self.OnSuccessfulPickup then self.OnSuccessfulPickup(self) end;
			
			if partialPickup ~= true then
				if objectParent:IsA("Model") then
					objectParent:Destroy();
				else
					interactPart:Destroy();
				end
				
			else
				if objectParent then
					interactPart.Parent = objectParent;
					if objectParent:IsA("Model") then
						objectParent.PrimaryPart = interactPart;
					end
				end
				if objectParent:FindFirstChild("BillboardGui") then
					objectParent.BillboardGui.Enabled = true;
				end
				
			end

			
			local soundName;
			if self.PickUpSound then
				soundName = self.PickUpSound;

			else
				if itemLib and itemLib.Type == modItemsLibrary.Types.Resource then
					if itemLib.Name == "Metal Scraps" then
						soundName = "StorageMetalPickup";
					elseif itemLib.Name == "Glass Shards" then
						soundName = "StorageGlassPickup";
					elseif itemLib.Name == "Wooden Parts" then
						soundName = "StorageWoodPickup";
					elseif itemLib.Name == "Cloth" then
						soundName = "StorageClothPickup";
					else
						soundName = "StorageItemPickup";
					end
				elseif itemLib and itemLib.Type == modItemsLibrary.Types.Blueprint then
					soundName = "StorageBlueprintPickup";
				elseif itemLib and itemLib.Type == modItemsLibrary.Types.Tool then
					soundName = "StorageWeaponPickup";
				elseif itemLib and itemLib.Type == modItemsLibrary.Types.Clothing then
					soundName = "StorageClothPickup";
				else
					soundName = "StorageItemPickup";
				end

			end

			if soundName then
				modAudio.Preload(soundName, 5);
				local sound = modAudio.Play(soundName, nil, nil, false);
				if sound then
					sound.PlaybackSpeed = math.random(70, 120)/100;
				end;
			end

		else
			if interactPart.Parent ~= nil then
				if objectParent then
					interactPart.Parent = objectParent;
					if objectParent:IsA("Model") then
						objectParent.PrimaryPart = interactPart;
					end
				end
				if objectParent:FindFirstChild("BillboardGui") then
					objectParent.BillboardGui.Enabled = true;
				end
				
				print("Interactable:PickUp>> Failed to pick up",self.ItemName," due to:", invokeRequest);
			end
		end
	end
	
	return interact;
end


-- function Interactables.Storage(moduleScript, storageId, storageName, configurations)
--  local remoteOpenStorageRequest = remotes.Interactable.OpenStorageRequest;
-- 	local interact = Interactables.new();
-- 	local interactMeta = getmetatable(interact);
-- 	interactMeta.Label = "Open Storage";
-- 	interactMeta.CanInteract = true;
-- 	interactMeta.Configurations = {};
	
-- 	function interactMeta:SetStorageId(storageId)
-- 		interact.StorageId = storageId;
-- 		interact.Script:SetAttribute("StorageId", storageId);
-- 	end
	
-- 	interact.Script = moduleScript;
-- 	interact.Type = Types.Storage;
-- 	interact.StorageId = storageId or "empty";
-- 	interact.StorageName = storageName or "Empty";
-- 	interact.Configurations = configurations;
-- 	interact.Label = nil;
-- 	interact.EmptyLabel = "Empty";
-- 	interact.IndicatorPresist = false;
	
-- 	function interact:OnSync(data)
-- 		self.InspectMode = data.InspectMode or self.InspectMode;
		
-- 		self.StorageId = data.StorageId or self.StorageId;
-- 		self.StorageName = data.StorageName or self.StorageName;
-- 		self.Whitelist = data.Whitelist or self.Whitelist;
-- 		self.EmptyLabel = data.EmptyLabel or self.EmptyLabel;
-- 		self.LevelRequired = data.LevelRequired or self.LevelRequired;
-- 		self.Label = data.Label;
		
-- 		if data.StorageName then
-- 			interactMeta.Label = "Open "..data.StorageName;
-- 			--self.Label = "Open "..data.StorageName;
-- 		end
-- 	end
	
-- 	function interact:OnInteracted(library)
-- 		local modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);
-- 		local externalStorageWindow: InterfaceWindow = modClientGuis.getWindow("ExternalStorage");
-- 		if externalStorageWindow and externalStorageWindow.Visible == false then

-- 			local storage = remoteOpenStorageRequest:InvokeServer(self.Part, self.Script);
-- 			if storage == nil or typeof(storage) ~= "table" then return end;

-- 			externalStorageWindow.Binds.InteractPart = self.Part;
-- 			externalStorageWindow.Binds.Interactable = self.Config;
-- 			modAudio.Play(self.OpenSound or "CrateOpen", self.Part, nil, false);
-- 			externalStorageWindow:Open(storage.Id, storage);
-- 		end
-- 	end
	
-- 	local lastInvoke = tick();
-- 	function interact:OnTrigger(library)
-- 		self.StorageId = self.Script:GetAttribute("StorageId") or self.StorageId;
		
-- 		local exist = false;
-- 		if library.modData.Storages[self.StorageId] == nil then
			
-- 			if tick()-lastInvoke < 1 then return end;
-- 			lastInvoke = tick();
			
-- 			local storage = remoteOpenStorageRequest:InvokeServer(self.Part, self.Script);
-- 			if typeof(storage) ~= "table" then
-- 				Debugger:Warn("Fail to load storage:", storage);
-- 			end
-- 			if storage then library.modData.SetStorage(storage); end
			
-- 			if library.modData.Storages[self.StorageId] == nil then
-- 				self.CanInteract = false;
-- 				interactMeta.Label = self.EmptyLabel or "Empty "..self.StorageName;
				
-- 			else
-- 				exist = true;
-- 			end
-- 		else
-- 			exist = true;
-- 		end
		
-- 		if exist then
-- 			self.CanInteract = true;
-- 			interactMeta.Label = "Open "..self.StorageName;
-- 		end
-- 	end
	
-- 	return interact;
-- end

function Interactables.Trigger(tag, label, premium)
	local interact = Interactables.new();
	local interactMeta = getmetatable(interact);
	interactMeta.Label = label or "Activate";
	interactMeta.CanInteract = true;
	
	interact.Type = Types.Trigger;
	interact.TriggerTag = tag;
	interact.Label = label;
	--interact.IndicatorPresist = false;
	interact.Premium = premium == true;
	
	function interactMeta:OnInteracted(library)
		task.spawn(function()
			if self.TriggerEffect then 
				self.TriggerEffect(self);
			end
		end)
		remoteOnTrigger:InvokeServer(self.Part, self.Script);
	end
	
	return interact;
end

function Interactables.Collectible(collectibleId, desc)
	--local lib = modCollectiblesLibrary:Find(collectibleId);
	local interact = Interactables.new();
	local interactMeta = getmetatable(interact);
	interactMeta.Label = desc;
	interactMeta.CanInteract = true;
	
	interact.Type = Types.Collectible;
	interact.Id = collectibleId;
	
	function interact:OnInteracted(library)
		local interactObject = self.Part;
		local objectParent = interactObject.Parent;
		interactObject.Parent = game.ReplicatedStorage;
		local invokeRequest = remotePickUpRequest:InvokeServer(interactObject, self.Script);

		if invokeRequest == true then
			interactObject:Destroy();
			if self.OnSuccessfulPickup then self.OnSuccessfulPickup(self) end;
			modAudio.Play("Collectible", nil, nil, false);

		else
			if interactObject.Parent ~= nil then
				interactObject.Parent = objectParent;
				if objectParent:IsA("Model") then
					objectParent.PrimaryPart = interactObject;
				end
				Debugger:Print("Failed to collect",self.ItemName," due to:", invokeRequest);
			end

		end
	end
	
	function interact:OnTrigger(library)
		local collectiblesData = modTableManager.GetDataHierarchy(library.modData.Profile, "Collectibles");
		
		if collectiblesData and collectiblesData[self.Id] then
			Debugger.Expire(self.Part, 0);
		end
	end
	
	return interact;
end


function Interactables.Toggle(label)
	local interact = Interactables.new();
	local interactMeta = getmetatable(interact);
	interactMeta.Label = label;
	interactMeta.CanInteract = true;
	
	interact.Type = Types.Toggle;
	interact.Active = false;
	interact.Label = nil;
	
	function interactMeta:OnTrigger()
		if self.Label:match("Lever") then
			if self.Active then
				self.Animation = "UnpullLever";
			else
				self.Animation = "PullLever";
			end
		end
	end
	
	function interact:OnInteracted()
		if RunService:IsServer() then return end;
		
		if self.OnToggle then
			self:OnToggle(localPlayer);
		end
		
		remoteInteractableToggle:FireServer(self.Part, self.Script);
	end
	
	return interact;
end

function Interactables.CardGame(src)
	local interact = Interactables.new();
	local interactMeta = getmetatable(interact);
	interactMeta.Label = "Request";
	interactMeta.CanInteract = true;

	interact.Type = Types.CardGame
	interact.IndicatorPresist = false;
	
	local lastFetch = tick()-2;
	function interactMeta:OnTrigger(library)
		local rPacket = {
			CanQueue = false;
			CanSpectate = false;
		};

		if tick()-lastFetch >= 1 then
			lastFetch = tick();
			rPacket = remoteCardGame:InvokeServer("request", {Interactable=src;});
		end
		
		if rPacket.CanQueue then
			interact.Label = "Request to join";
			
		elseif rPacket.CanSpectate then
			interact.Label = "Spectate";
	
		end
	end
	
	function interact:OnInteracted(library)
		library.interface.Object = self.Part;
		local rPacket = remoteCardGame:InvokeServer("requestjoin", {Interactable=src;});
		
		if rPacket.Success then
			library.interface:OpenWindow("CardGameWindow");
		end
	end

	return interact;
end

function Interactables.Seat(src)
	local interact = Interactables.new();
	local interactMeta = getmetatable(interact);
	interactMeta.Label = "Sit";
	interactMeta.CanInteract = true;

	interact.Type = Types.Seat
	interact.IndicatorPresist = false;

	function interactMeta:OnTrigger(library)
		if self.SeatPart == nil then
			self.SeatPart = src.Parent:FindFirstChildWhichIsA("Seat");
		end
		
		if self.SeatPart then
			if self.SeatPart.Occupant == nil then
				interact.Label = nil;
				interact.CanInteract = true;
			else
				interact.Label = "";
				interact.CanInteract = false;
			end
			
		else
			interact.Label = "Seat is broken";
			interact.CanInteract = false;
		end
	end

	function interact:OnInteracted(library)
		library.interface.Object = self.Part;
		
		local _returnPacket = remoteCharacterInteractions:InvokeServer("sit", {InteractableScript=src});
	end

	return interact;
end

function Interactables.Terminal()
	local interact = Interactables.new();
	local interactMeta = getmetatable(interact);
	interactMeta.Label = "Access Terminal";
	interactMeta.CanInteract = true;

	interact.Type = Types.Terminal
	interact.IndicatorPresist = false;
	interact.ItemRequired = "rcetablet";

	function interactMeta:OnTrigger(library)
		if interact.ItemRequired == "rcetablet" then
			interact.Label = "Hijack with RCE Tablet";
		end
	end
	
	function interact:OnInteracted(library)
		if library.interface:IsVisible("TerminalWindow") then return end;

		library.interface.Object = self.Part;
		library.interface.InteractData = self;
		library.interface:ToggleWindow("TerminalWindow", nil, self.TerminalData);
	end

	return interact;
end

return Interactables;