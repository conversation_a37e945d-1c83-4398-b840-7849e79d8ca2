local Vector = {};

Vector.Vec3Min = Vector3.new(-math.huge, -math.huge, -math.huge);
Vector.Vec3Max = Vector3.new(math.huge, math.huge, math.huge);

function Vector.ClampVector3(vec: Vector3, min, max)
	min = min or Vector.Vec3Min;
	max = max or Vector.Vec3Max;
	
	return Vector3.new(
		math.clamp(vec.X, min.X or Vector.Vec3Min.X, max.X or Vector.Vec3Max.X),
		math.clamp(vec.Y, min.Y or Vector.Vec3Min.Y, max.Y or Vector.Vec3Max.Y),
		math.clamp(vec.Z, min.Z or Vector.Vec3Min.Z, max.Z or Vector.Vec3Max.Z)
	)
end

function Vector.DistanceSqrdXZ(vecA: Vector3, vecB: Vector3)
	return (vecA.X-vecB.X)^2 + (vecA.Z-vecB.Z)^2;
end

function Vector.DistanceSqrd(vecA: Vector3, vecB: Vector3)
	return (vecA.X-vecB.X)^2 + (vecA.Y-vecB.Y)^2 + (vecA.Z-vecB.Z)^2;
end

function Vector:InCenter(position: Vector3, center: Vector3, radius: number)
	radius = radius or 10;

	local regionSize = Vector3.new(radius, radius, radius);
	local regionMin = center - regionSize;
	local regionMax = center + regionSize;

	if position.X <= regionMin.X or position.Y <= regionMin.Y or position.Z <= regionMin.Z
		or position.X >= regionMax.X or position.Y >= regionMax.Y or position.Z >= regionMax.Z then
		return false
	end
	return true;
end

function Vector.ClampMagnitude(vector, min, max)
	return vector.Unit * math.clamp(vector.Magnitude, min, max);
end

function Vector.MagnitudeSqrd(v)
	return (v.X)^2 + (v.Y)^2 + (v.Z)^2;
end

function Vector.PointBetweenAB(a, b, p)
	local u = b-a -- line between a to b
	local pq = p-a -- line between p to a
	local t = math.clamp(pq:Dot(u)/Vector.MagnitudeSqrd(u), 0, 1) -- clamped projection scalar
	
	return a + (u * t)
end

function Vector.WithinRegion2(point: Vector2, minVec2: Vector2, maxVec2: Vector2, b: number)
	if point.X+b >= minVec2.X and point.X-b <= maxVec2.X
	and point.Y+b >= minVec2.Y and point.Y-b <= maxVec2.Y then
		return true;
	end
	return false;
end

function Vector.CleanUnitVec(posA: Vector3, posB: Vector3): Vector3
	local unit = (posA - posB).Unit;
	
	if rawequal(unit, unit) == false then
		return Vector3.zero;
	end
	
	return unit;
end

function Vector.RandomUnitVector(vecType)
	if vecType == 3 then
		return Vector3.new(
			math.random(-100, 100)/100,
			math.random(-100, 100)/100,
			math.random(-100, 100)/100
		).Unit;
	elseif vecType == 2 then
		return Vector2.new(
			math.random(-100, 100)/100,
			math.random(-100, 100)/100
		).Unit;
	end
	
	return;
end

function Vector.GetBoundingBox(hullPoints)
	local n = #hullPoints
	if n < 3 then return nil end -- Need at least 3 points for a polygon
	
	-- Compute edges (including the closing edge)
	local edges = {}
	for i = 1, n do
		local next_i = (i % n) + 1
		local dx = hullPoints[next_i].X - hullPoints[i].X
		local dy = hullPoints[next_i].Z - hullPoints[i].Z
		edges[i] = {dx, dy}
	end

	-- Calculate edge angles
	local edge_angles = {}
	for i = 1, n do
		edge_angles[i] = math.atan2(edges[i][2], edges[i][1])
	end
	
	-- Map angles to [0, pi/2) and remove duplicates
	local function unique(t)
		local seen, res = {}, {}
		for _, v in ipairs(t) do
			local normalized = math.abs(v % (math.pi/2))
			if not seen[normalized] then
				seen[normalized] = true
				table.insert(res, normalized)
			end
		end
		return res
	end
	edge_angles = unique(edge_angles)

	-- Test each angle
	local min_bbox = {0, math.huge, 0, 0, 0, 0, 0, 0}
	for _, angle in ipairs(edge_angles) do
		local cos_a = math.cos(angle)
		local sin_a = math.sin(angle)
		
		-- Proper rotation matrix
		local R = {
			{ cos_a, -sin_a },
			{ sin_a, cos_a }
		}

		-- Rotate all points
		local xvals, yvals = {}, {}
		for i, p in ipairs(hullPoints) do
			local x, y = p.X, p.Z
			xvals[i] = R[1][1]*x + R[1][2]*y
			yvals[i] = R[2][1]*x + R[2][2]*y
		end
		
		local min_x, max_x = math.min(unpack(xvals)), math.max(unpack(xvals))
		local min_y, max_y = math.min(unpack(yvals)), math.max(unpack(yvals))
		local width, height = max_x - min_x, max_y - min_y
		local area = width * height
		
		if area < min_bbox[2] then
			min_bbox = { angle, area, width, height, min_x, max_x, min_y, max_y }
		end
	end

	-- Re-create rotation matrix for best angle
	local angle = min_bbox[1]
	local cos_a = math.cos(angle)
	local cos_a_m90 = math.cos(angle - math.pi/2)
	local cos_a_p90 = math.cos(angle + math.pi/2)
	local R = {
		{ cos_a, cos_a_m90 },
		{ cos_a_p90, cos_a }
	}

	-- Center point
	local center_x = (min_bbox[5] + min_bbox[6]) / 2
	local center_y = (min_bbox[7] + min_bbox[8]) / 2
	local center_point = Vector3.new(
		R[1][1]*center_x + R[1][2]*center_y,
		0,
		R[2][1]*center_x + R[2][2]*center_y
	)

	-- Corner points
	local corners = {
		{min_bbox[6], min_bbox[7]},
		{min_bbox[5], min_bbox[7]},
		{min_bbox[5], min_bbox[8]},
		{min_bbox[6], min_bbox[8]}
	}
	local corner_points = {}
	for i = 1, 4 do
		local x, y = corners[i][1], corners[i][2]
		corner_points[i] = Vector3.new(
			R[1][1]*x + R[1][2]*y,
			0,
			R[2][1]*x + R[2][2]*y
		)
	end

	return {
		Angle = angle,
		Area = min_bbox[2],
		Width = min_bbox[3],
		Height = min_bbox[4],
		CenterPoint = center_point,
		CornerPoints = corner_points
	}
end

return Vector;
