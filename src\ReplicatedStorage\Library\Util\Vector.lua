local Vector = {};

Vector.Vec3Min = Vector3.new(-math.huge, -math.huge, -math.huge);
Vector.Vec3Max = Vector3.new(math.huge, math.huge, math.huge);

function Vector.ClampVector3(vec: Vector3, min, max)
	min = min or Vector.Vec3Min;
	max = max or Vector.Vec3Max;
	
	return Vector3.new(
		math.clamp(vec.X, min.X or Vector.Vec3Min.X, max.X or Vector.Vec3Max.X),
		math.clamp(vec.Y, min.Y or Vector.Vec3Min.Y, max.Y or Vector.Vec3Max.Y),
		math.clamp(vec.Z, min.Z or Vector.Vec3Min.Z, max.Z or Vector.Vec3Max.Z)
	)
end

function Vector.DistanceSqrdXZ(vecA: Vector3, vecB: Vector3)
	return (vecA.X-vecB.X)^2 + (vecA.Z-vecB.Z)^2;
end

function Vector.DistanceSqrd(vecA: Vector3, vecB: Vector3)
	return (vecA.X-vecB.X)^2 + (vecA.Y-vecB.Y)^2 + (vecA.Z-vecB.Z)^2;
end

function Vector:InCenter(position: Vector3, center: Vector3, radius: number)
	radius = radius or 10;

	local regionSize = Vector3.new(radius, radius, radius);
	local regionMin = center - regionSize;
	local regionMax = center + regionSize;

	if position.X <= regionMin.X or position.Y <= regionMin.Y or position.Z <= regionMin.Z
		or position.X >= regionMax.X or position.Y >= regionMax.Y or position.Z >= regionMax.Z then
		return false
	end
	return true;
end

function Vector.ClampMagnitude(vector, min, max)
	return vector.Unit * math.clamp(vector.Magnitude, min, max);
end

function Vector.MagnitudeSqrd(v)
	return (v.X)^2 + (v.Y)^2 + (v.Z)^2;
end

function Vector.PointBetweenAB(a, b, p)
	local u = b-a -- line between a to b
	local pq = p-a -- line between p to a
	local t = math.clamp(pq:Dot(u)/Vector.MagnitudeSqrd(u), 0, 1) -- clamped projection scalar
	
	return a + (u * t)
end

function Vector.WithinRegion2(point: Vector2, minVec2: Vector2, maxVec2: Vector2, b: number)
	if point.X+b >= minVec2.X and point.X-b <= maxVec2.X
	and point.Y+b >= minVec2.Y and point.Y-b <= maxVec2.Y then
		return true;
	end
	return false;
end

function Vector.CleanUnitVec(posA: Vector3, posB: Vector3): Vector3
	local unit = (posA - posB).Unit;
	
	if rawequal(unit, unit) == false then
		return Vector3.zero;
	end
	
	return unit;
end

function Vector.RandomUnitVector(vecType)
	if vecType == 3 then
		return Vector3.new(
			math.random(-100, 100)/100,
			math.random(-100, 100)/100,
			math.random(-100, 100)/100
		).Unit;
	elseif vecType == 2 then
		return Vector2.new(
			math.random(-100, 100)/100,
			math.random(-100, 100)/100
		).Unit;
	end
	
	return;
end

function Vector.GetBoundingBox(points: {Vector3}, rotation: CFrame?)
	points = Vector.GiftWrapPoints(points);

	local n = #points
	if n == 0 then return {} end

	-- Find min/max X and Z coordinates of the transformed points
	local min_x, max_x = points[1].X, points[1].X
	local min_z, max_z = points[1].Z, points[1].Z

	for i = 2, n do
		local p = points[i]
		if p.X < min_x then min_x = p.X end
		if p.X > max_x then max_x = p.X end
		if p.Z < min_z then min_z = p.Z end
		if p.Z > max_z then max_z = p.Z end
	end

	-- Create 4 corner points of the bounding rectangle in local space
	local local_corners = {
		Vector3.new(min_x, 0, min_z), -- Bottom-left
		Vector3.new(max_x, 0, min_z), -- Bottom-right
		Vector3.new(max_x, 0, max_z), -- Top-right
		Vector3.new(min_x, 0, max_z)  -- Top-left
	};

	-- Transform corners to world space if rotation is provided
	if rotation then
		local world_corners = {}
		for i = 1, 4 do
			world_corners[i] = rotation:PointToWorldSpace(local_corners[i])
		end
		return world_corners;
	else
		return local_corners;
	end
end

function Vector.GiftWrapPoints(points: {Vector3})
	local n = #points
	if n < 3 then return points end -- Need at least 3 points for a hull

	-- Find the leftmost point (smallest X, then smallest Z if tied)
	local leftmost = 1
	for i = 2, n do
		if points[i].X < points[leftmost].X or
		   (points[i].X == points[leftmost].X and points[i].Z < points[leftmost].Z) then
			leftmost = i
		end
	end

	local hull = {}
	local current = leftmost

	repeat
		-- Add current point to hull
		table.insert(hull, points[current])

		-- Find the most counterclockwise point from current
		local next_point = 1
		if next_point == current then next_point = 2 end

		for i = 1, n do
			if i ~= current then
				-- Calculate cross product to determine orientation
				local orientation = Vector.CrossProduct2D(
					points[current],
					points[next_point],
					points[i]
				)

				-- If i is more counterclockwise than next_point, or
				-- if they're collinear but i is farther, choose i
				if orientation > 0 or
				   (orientation == 0 and Vector.DistanceSquared2D(points[current], points[i]) >
				    Vector.DistanceSquared2D(points[current], points[next_point])) then
					next_point = i
				end
			end
		end

		current = next_point

	until current == leftmost -- We've wrapped around to the start

	return hull
end

-- Helper function for 2D cross product (orientation test)
function Vector.CrossProduct2D(p1: Vector3, p2: Vector3, p3: Vector3)
	-- Returns > 0 if p1->p2->p3 is counterclockwise
	-- Returns < 0 if p1->p2->p3 is clockwise
	-- Returns = 0 if points are collinear
	return (p2.X - p1.X) * (p3.Z - p1.Z) - (p2.Z - p1.Z) * (p3.X - p1.X)
end

-- Helper function for squared distance in 2D
function Vector.DistanceSquared2D(p1: Vector3, p2: Vector3)
	local dx = p2.X - p1.X
	local dz = p2.Z - p1.Z
	return dx * dx + dz * dz
end

return Vector;
