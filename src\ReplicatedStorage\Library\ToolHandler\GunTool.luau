local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService: RunService = game:GetService("RunService");
local UserInputService: UserInputService = game:GetService("UserInputService");
local CollectionService: CollectionService = game:GetService("CollectionService");

local camera: Camera = workspace.CurrentCamera;
local localPlayer: Player = game.Players.LocalPlayer;

local modToolHandler = shared.require(game.ReplicatedStorage.Library.ToolHandler);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modKeyBindsHandler = shared.require(game.ReplicatedStorage.Library.KeyBindsHandler);
local modConfigurations = shared.require(game.ReplicatedStorage.Library.Configurations);
local modWeaponMechanics = shared.require(game.ReplicatedStorage.Library.WeaponsMechanics);
local modWeaponAttributes = shared.require(game.ReplicatedStorage.Library.WeaponsLibrary.WeaponAttributes);
local modProjectile = shared.require(game.ReplicatedStorage.Library.Projectile);
local modArcTracing = shared.require(game.ReplicatedStorage.Library.ArcTracing);
local modItemModsLibrary = shared.require(game.ReplicatedStorage.Library.ItemModsLibrary);
local modParticleSprinkler = shared.require(game.ReplicatedStorage.Particles.ParticleSprinkler);

local modHealthComponent = shared.require(game.ReplicatedStorage.Components.HealthComponent);

local modMath = shared.require(game.ReplicatedStorage.Library.Util.Math);
local modTables = shared.require(game.ReplicatedStorage.Library.Util.Tables);

local prefabStorage: Folder = game.ReplicatedStorage.Prefabs.Objects;

local playerGui;
local DELTA = 1/30;
local RELOAD_RADIAL_CONFIG = '{"version":1,"size":128,"count":60,"columns":8,"rows":8,"images":["rbxassetid://**********"]}';

local toolHandler: GunToolHandler = modToolHandler.new();
--==

function toolHandler.onRequire()
    remoteToolInputHandler = modRemotesManager:Get("ToolInputHandler");

	if RunService:IsServer() then
		modToolService = shared.require(game.ServerScriptService.ServerLibrary.ToolService);
		modOnGameEvents = shared.require(game.ServerScriptService.ServerLibrary.OnGameEvents);

	elseif RunService:IsClient() then
		playerGui = localPlayer:WaitForChild("PlayerGui");
		
		modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);
		modRadialImage = shared.require(game.ReplicatedStorage.Library.UI.RadialImage);
		modData = shared.require(localPlayer:WaitForChild("DataModule") :: ModuleScript);

		weaponInterfaceTemplate = script:WaitForChild("WeaponInterface");
		weaponStatusTemplate = script:WaitForChild("WeaponStatusTemplate") :: TextLabel;

	end
end

function toolHandler.Setup(handler: ToolHandlerInstance)
	local equipmentClass: EquipmentClass = handler.EquipmentClass;

	local configurations = equipmentClass.Configurations;
	local properties = equipmentClass.Properties;

	properties.CanPrimaryFire = false;
	properties.IsPrimaryFiring = false;
	properties.Reloading = false;
	properties.CancelReload = false;
	properties.LastFired = tick()-5;

	local storageItem: StorageItem = handler.StorageItem;
	properties.Ammo = storageItem:GetValues("A") or configurations.MagazineSize;
	properties.MaxAmmo = storageItem:GetValues("MA") or configurations.AmmoCapacity;

	Debugger:Warn(`Equip ({handler.WieldComp.ItemId}) gun: {properties.Ammo}/{properties.MaxAmmo}`);
	handler:LoadWieldConfig();
end

function toolHandler.PullTrigger(handler: ToolHandlerInstance)
	local equipmentClass: EquipmentClass = handler.EquipmentClass;

	local configurations = equipmentClass.Configurations;
	local properties = equipmentClass.Properties;

	local rawRpm: number = configurations.Rpm;

	local changeRef = {RawRpm=rawRpm};
	equipmentClass:ProcessModifiers("OnPrimaryFire", changeRef);
	rawRpm = changeRef.RawRpm;

	local baseFr = 60/rawRpm;
	local firerate = baseFr;
	
	if configurations.RapidFire then
		local f = math.clamp((tick()- properties.RapidFireStart)/configurations.RapidFire, 0, 1);
		firerate = baseFr + f*(DELTA - baseFr);
		
		if properties.LoopedPrimaryFireAudio then
			properties.LoopedPrimaryFireAudio.PlaybackSpeed = 0+(f/2);
		end
	end

	if configurations.RpmScaler and configurations.RpmScaler ~= 0 then
		firerate = firerate * (1-configurations.RpmScaler);
	end

	firerate = math.clamp(firerate, DELTA, 999);
	properties.FireRate = firerate;
end


if RunService:IsClient() then -- MARK: Client
	function toolHandler.ClientEquip(handler: ToolHandlerInstance)
		local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);
		local modCharacter = modData:GetModCharacter();
		
		local character: Model = playerClass.Character;
		local playerHead: BasePart = playerClass.Head;
		local rootPart: BasePart = playerClass.RootPart;
		local humanoid: Humanoid = playerClass.Humanoid;

		local mouseProperties = modCharacter.MouseProperties;
		local characterProperties = modCharacter.CharacterProperties;

		local storageItem: StorageItem = handler.StorageItem;
		local equipmentClass: EquipmentClass = handler.EquipmentClass;
		local toolAnimator: ToolAnimator = handler.ToolAnimator;

		local siid: string = storageItem.ID;

		local toolPackage = handler.ToolPackage;
		local animations = toolPackage.Animations;
		local audio = toolPackage.Audio;

		local configurations: ConfigVariable = equipmentClass.Configurations;
		local properties: PropertiesVariable<{}> = equipmentClass.Properties;
	
		local primaryToolPrefab: Model = handler.Prefabs[1];
		local primaryHandle = primaryToolPrefab and primaryToolPrefab:WaitForChild("Handle") or nil;
		
		-- Preload
		modAudio.Preload("BulletBodyImpact");
		modAudio.Preload("BulletBodyImpact2");
		modAudio.Preload("BulletHeadImpact");
		modAudio.Preload("BulletHeadImpact2");
		modAudio.Preload("BulletFleshKillshot");
		modAudio.Preload("BulletFleshKillshot2");
		for k, audioData in pairs(audio) do
			modAudio.Preload(audioData.Id);
		end

		--MARK: Init
		properties.CanAimDown = false;
		properties.AimWaistZ = 0;
		properties.AvailableInvAmmo = 0;
		properties.LastAdsBool = false;
		properties.SpinFloat = nil;
		properties.IsSpinning = false;
		properties.SpinningReady = nil;
		properties.Inaccuracy = configurations.StandInaccuracy;
		properties.InFocusDuration = nil;
		properties.FocusCharge = 0;
		properties.AdsPartsHidden = false;
		properties.FlipPlayingWeaponAnim = nil;
		properties.LastPlayingWeaponAnim = nil;
		properties.LastSprintAnimationCanPlay = nil;
		properties.ReloadCooldown = 0;
		properties.ReloadAttemptCount = 0;
		properties.LastSuccessfulReload = 0;
		properties.OnShotTick = tick();
		properties.LastFired = tick()-5;
		properties.RapidFireStart = nil;
		properties.InfType = primaryToolPrefab:GetAttribute("InfAmmo");

		local statusDisplay = {};
		properties.StatusDisplay = statusDisplay;

		statusDisplay.Reload = {
			Order=1;
			Text="Reload";
			Hide=true;
			KeyString="KeyReload";
		}
		statusDisplay.ToggleSpecial = {
			Order=2;
			Text="Special";
			Hide=true;
			KeyString="KeyToggleSpecial";
		}
	
		-- Load Interface
		characterProperties.HideCrosshair = true;

		for _, obj in pairs(playerGui:GetChildren()) do
			if obj.Name == "WeaponInterface" then
				obj:Destroy();
			end
		end

		local weaponInterface = weaponInterfaceTemplate:Clone();
		weaponInterface.Parent = playerGui;
		handler.Garbage:Tag(weaponInterface);

		local crosshairFrame = weaponInterface:WaitForChild("CrosshairFrame");
		local tpBlockFrame = weaponInterface:WaitForChild("TPBlock");
		local scopeFrame = weaponInterface:WaitForChild("ScopeFrame");
		local ammoCounter = weaponInterface:WaitForChild("AmmoCounter");
		local weaponStatusHud = weaponInterface:WaitForChild("WeaponStatusHud") :: Frame;
		local hitmarker = weaponInterface:WaitForChild("HitmarkerFrame");
		local reloadLabel = crosshairFrame:WaitForChild("ReloadIcon");
		
		local reloadRadial = modRadialImage.new(RELOAD_RADIAL_CONFIG, reloadLabel);

		hitmarker.Visible = false;
		
		local projRaycast = RaycastParams.new();
		projRaycast.FilterType = Enum.RaycastFilterType.Include;
		projRaycast.IgnoreWater = true;
		projRaycast.CollisionGroup = "Raycast";
		projRaycast.FilterDescendantsInstances = {workspace.Environment; workspace.Terrain; workspace.Entity; workspace:FindFirstChild("Characters")};
		

		local toolModelObjsList = {};
		for a=1, #handler.Prefabs do
			local toolModel = handler.Prefabs[a];
			for _, obj in pairs(toolModel:GetDescendants()) do
				if not obj:IsA("BasePart") then continue end;
				table.insert(toolModelObjsList, obj);
			end
		end
		handler.Garbage:Tag(toolModelObjsList);
		
	
		local sightViewModel;
		local editPanelVisible = false;
		if character:FindFirstChild("EditMode") then
			sightViewModel = primaryToolPrefab:FindFirstChild("SightViewModel", true);
	
			if sightViewModel then
				editPanelVisible = true;
				if properties.AimDownViewModel == nil then
					properties.AimDownViewModel = CFrame.new();
				end
				if properties.HipFireViewModel == nil then
					properties.HipFireViewModel = CFrame.new();
				end
				sightViewModel.CFrame = properties.HipFireViewModel;
			end
		end

		local projectileId, projectileObject, arcTracer, arcTracerConfig;
		if configurations.BulletMode == modWeaponAttributes.BulletModes.Projectile then
		
			projectileId = storageItem.Values.CustomProj or configurations.ProjectileId;
			projectileObject = modProjectile.Get(projectileId);
			arcTracerConfig = projectileObject.ArcTracerConfig;
			
			arcTracer = modArcTracing.new();
			for k, v in pairs(arcTracerConfig) do
				arcTracer[k] = v;
			end
			if arcTracerConfig.IgnoreEntities ~= true then
				table.insert(arcTracer.RayWhitelist, workspace.Entity);
				table.insert(arcTracer.RayWhitelist, workspace:FindFirstChild("Characters"));
			end
		end

		local function getReserveAmmo(recount)
			if recount == true and configurations.AmmoType then
				local activeAmmoId = storageItem.Values.AmmoId or configurations.AmmoType;
				properties.AvailableInvAmmo = modData.CountItemIdFromCharacter(activeAmmoId);
			end
	
			if properties.NoMaxAmmo then return configurations.AmmoCapacity end;
	
			return properties.MaxAmmo + properties.AvailableInvAmmo;
		end
		getReserveAmmo(true);


		local function updateAmmoCounter()
			if storageItem == nil then return end;
			
			if modConfigurations.CompactInterface then
				ammoCounter.Position = UDim2.new(0.5, 0, 1, -90);
			else
				ammoCounter.Position = UDim2.new(0.5, 0, 0.94, -145);
			end
			
			if properties.NoMaxAmmo then
				ammoCounter.Text = properties.Ammo .."/".. configurations.MagazineSize;
			else
				ammoCounter.Text = properties.Ammo .."/".. getReserveAmmo();
			end
		end

		--MARK: WeaponRender
		local s1Tick = tick();
		local function weaponRender(delta)
			if not characterProperties.IsEquipped then return end;
			if crosshairFrame == nil or not crosshairFrame:IsDescendantOf(playerGui) then return end;
		
			local currentTick = tick();
			if currentTick - s1Tick >= 1 then
				s1Tick = currentTick;
				
				updateAmmoCounter();
			end

			local mousePosition = UserInputService:GetMouseLocation();
			if not mouseProperties.MouseLocked then
				crosshairFrame.Position = UDim2.new(0, mousePosition.X, 0, mousePosition.Y);
			else
				crosshairFrame.Position = UDim2.new(0.5, 0, 0.5, 0);
			end
			
			if configurations.SpinUpTime and configurations.SpinDownTime then
				if properties.SpinFloat == nil then properties.SpinFloat = 0 end;
				local spinUpRate = delta/configurations.SpinUpTime;
				local spinDownRate = delta/configurations.SpinDownTime;
				
				properties.SpinFloat = math.clamp(
					properties.IsSpinning
					and properties.SpinFloat + spinUpRate 
					or properties.SpinFloat - spinDownRate, 0, 1);
			else
				properties.SpinningReady = nil;
			end
			local spinFloat= properties.SpinFloat;
			
			local playerVelocity = characterProperties.PlayerVelocity;

			local movingInaccuracy = configurations.MovingInaccuracyScale or 1.3;
			local crouchInaccuracyReduction = configurations.CrouchInaccuracyReduction or 0.6;
			local focusInaccuracyReduction = configurations.FocusInaccuracyReduction or 0.5;
			local standInaccuracy = configurations.StandInaccuracy;

			properties.Inaccuracy = standInaccuracy
				+ (characterProperties.DefaultWalkSpeed > 0 and (playerVelocity/characterProperties.DefaultWalkSpeed)*movingInaccuracy or 0)
				- (characterProperties.IsCrouching and crouchInaccuracyReduction or 0)
				- (characterProperties.IsFocused and focusInaccuracyReduction or 0)
				+ (configurations.FullSpinInaccuracyChange and spinFloat and configurations.FullSpinInaccuracyChange*spinFloat or 0);
			
			if characterProperties.IsFocused and configurations.Deadeye then
				properties.Inaccuracy = properties.Inaccuracy * math.clamp(1-configurations.Deadeye, 0, 1);
			end
			
			-- Skill: Trained In Arms;
			if playerClass.Properties.trinar then
				properties.Inaccuracy = properties.Inaccuracy * (100-playerClass.Properties.trinar.Percent)/100;
			end
			
			-- Flinch
			if mouseProperties.FlinchInacc > 0 then
				properties.Inaccuracy = properties.Inaccuracy + mouseProperties.FlinchInacc;
			end

			if characterProperties.FirstPersonCamera then
				characterProperties.BodyLockToCam = true;
				tpBlockFrame.Visible = false;
				
			else
				if (currentTick - properties.LastFired) > 1 or properties.Reloading then
					characterProperties.BodyLockToCam = false;
				else
					characterProperties.BodyLockToCam = true;
				end
				
	
				local scanPoint = modWeaponMechanics.CastHitscanRay{
					Origin = mouseProperties.Focus.p;
					Direction = mouseProperties.Direction;
					IncludeList = projRaycast.FilterDescendantsInstances;
					Range = 512;
				};
				
				local shotOrigin = playerHead.Position + -Vector3.yAxis*0.4 + playerHead.CFrame.RightVector *(characterProperties.LeftSideCamera and -1 or 1);
				
				local headDir = (scanPoint-shotOrigin).Unit;
				local headDist = (scanPoint-shotOrigin).Magnitude;
				local landPoint = modWeaponMechanics.CastHitscanRay{
					Origin = shotOrigin;
					Direction = headDir;
					IncludeList = projRaycast.FilterDescendantsInstances;
					Range = headDist;
				};
	
				if RunService:IsStudio() then
					-- local scanPp = Debugger:PointPart(scanPoint);
					-- scanPp.Color = Color3.fromRGB(255, 0, 0);
					-- game.Debris:AddItem(scanPp, 0.1);
		
					-- local scanLp = Debugger:PointPart(landPoint);
					-- scanLp.Color = Color3.fromRGB(0, 255, 0);
					-- game.Debris:AddItem(scanLp, 0.1);
	
					-- local scanSp = Debugger:PointPart(secondPoint);
					-- scanSp.Color = Color3.fromRGB(0, 0, 255);
					-- game.Debris:AddItem(scanSp, 0.1);
				end
	
				local secAimWaist = properties.AimWaistZ + (characterProperties.LeftSideCamera and -0.4 or 0.4);
				local hpSecondPoint = (characterProperties.MotorHeadCFrameA * CFrame.Angles(0, 0, secAimWaist) * characterProperties.MotorHeadCFrameB).Position;
	
	
				local pointsDist = (scanPoint-landPoint).Magnitude;
				local isAimLineBlocked = pointsDist > 0.1;
				if isAimLineBlocked then
					tpBlockFrame.Visible = true;
				else
					tpBlockFrame.Visible = false;
				end
	
				local secondDist = 0;
				if characterProperties.IsFocused then
					local secondPoint = modWeaponMechanics.CastHitscanRay{
						Origin = hpSecondPoint;
						Direction = (scanPoint-hpSecondPoint).Unit;
						IncludeList = projRaycast.FilterDescendantsInstances;
						Range = (scanPoint-hpSecondPoint).Magnitude;
					};
					secondDist = (scanPoint-secondPoint).Magnitude;
				end
	
				local limZ = math.rad(26);
				if secondDist > 0.1 then
					-- third person blocked;
					if characterProperties.LeftSideCamera then
						properties.AimWaistZ = properties.AimWaistZ +0.02;
					else
						properties.AimWaistZ = properties.AimWaistZ -0.02;
					end
	
					properties.AimWaistZ = math.clamp(properties.AimWaistZ, -limZ, limZ);
	
				else 
					-- third person not blocked;
					properties.AimWaistZ = modMath.DeltaLerp(properties.AimWaistZ, 0, 10, delta);
	
				end
	
				characterProperties.Joints.WaistZ = modMath.DeltaLerp(characterProperties.Joints.WaistZ, properties.AimWaistZ, 10, delta);
			end
			if properties.AimDownViewModel then
				properties.CanAimDown = true;
				if properties.Reloading and configurations.CanUnfocusFire ~= false then
					properties.CanAimDown = false;
				end
				if toolAnimator:GetPlaying("Empty") then
					properties.CanAimDown = false;
				end
				
				if toolAnimator:GetPlaying("Inspect") then
					properties.CanAimDown = false;
				end
				if toolAnimator:GetPlaying("Inspect2") then
					properties.CanAimDown = false;
				end
				
				if properties.IsPrimaryFiring and configurations.UseScopeGui and configurations.KeepScopedWhenFiring ~= true then
					properties.CanAimDown = false;
				end
				
				if properties.CanAimDown and characterProperties.IsFocused then
					characterProperties.FieldOfView = rootPart:GetAttribute("FOV") or properties.AimDownFOV or 50;
					characterProperties.SwayYStrength=0.01;
					characterProperties.VelocitySrength=0.1;
					
					characterProperties.AimDownSights = true;
					
					if sightViewModel then
						if properties.LastAdsBool ~= characterProperties.AimDownSights then
							sightViewModel.CFrame = properties.AimDownViewModel;
						end
	
						properties.AimDownViewModel = sightViewModel.CFrame;
						UserInputService.MouseIconEnabled = true;
	
						weaponInterface.EditPanel.Visible = editPanelVisible;
						weaponInterface.EditPanel.AttachmentTag.Value = sightViewModel;
						weaponInterface.EditPanel.AttachmentTag:SetAttribute("ADS", characterProperties.AimDownSights);
	
						modCharacter.DevViewModel = properties.AimDownViewModel;
					end
					
					properties.LastAdsBool = true;
				else
					characterProperties.AimDownSights = false;
				end
			else
				characterProperties.AimDownSights = false;
			end

			if not characterProperties.AimDownSights then
				if properties.HipFireFOV then
					characterProperties.FieldOfView = rootPart:GetAttribute("FOV") or properties.HipFireFOV or nil;
					
				else
					characterProperties.FieldOfView = nil;
					
				end
				
				characterProperties.SwayYStrength=1;
				characterProperties.VelocitySrength=1;
				
				if sightViewModel then
					if properties.LastAdsBool ~= characterProperties.AimDownSights and properties.HipFireViewModel then
						sightViewModel.CFrame = properties.HipFireViewModel;
					end
					
					properties.HipFireViewModel = sightViewModel.CFrame;
					UserInputService.MouseIconEnabled = true;
					
					weaponInterface.EditPanel.Visible = editPanelVisible;
					weaponInterface.EditPanel.AttachmentTag.Value = sightViewModel;
					weaponInterface.EditPanel.AttachmentTag:SetAttribute("ADS", characterProperties.AimDownSights);
					
					modCharacter.DevViewModel = properties.HipFireViewModel;
				end
				
				properties.LastAdsBool = false;
			end

			statusDisplay.Reload.Hide = not (properties.Ammo <= 0 and getReserveAmmo() > 0);
		
			if characterProperties.IsFocused then
				
				if properties.InFocusDuration == nil then
					if properties.Ammo <= 0 or properties.Reloading then
					else
						properties.InFocusDuration = tick();
					end
				end;
				
				if configurations.WeaponType == modWeaponAttributes.WeaponType.SMG then
					characterProperties.AdsWalkSpeedMultiplier = nil;
				elseif configurations.WeaponType == modWeaponAttributes.WeaponType.HMG then
					characterProperties.AdsWalkSpeedMultiplier = 0.3;
				elseif configurations.FocusWalkSpeedReduction then
					characterProperties.AdsWalkSpeedMultiplier = configurations.FocusWalkSpeedReduction;
				else
					characterProperties.AdsWalkSpeedMultiplier = 0.5;
				end
				
			else
				if properties.InFocusDuration ~= nil then
					properties.InFocusDuration = nil;
					properties.FocusCharge = 0;
				end;
				characterProperties.AdsWalkSpeedMultiplier = nil;
				
			end
			
			local aimDownScope = false;
			local s = 1/15;
	
			local focusDuration = configurations.FocusDuration or 0;
			if characterProperties.FirstPersonCamera and properties.AimDownViewModel and characterProperties.IsFocused then
				
				if focusDuration > 0 then
					crosshairFrame.Visible = true;
					if properties.InFocusDuration then
						properties.FocusCharge	= focusDuration and math.clamp((tick()-properties.InFocusDuration)/focusDuration, 0, 1) or 0;
					else
						properties.FocusCharge = 0;
					end
					
					local uiSpread = properties.Reloading and 14 or (1-properties.FocusCharge)*20;
					crosshairFrame.CrosshairN:TweenPosition(UDim2.new(0.5, 0, 0.5, -uiSpread), Enum.EasingDirection.InOut, Enum.EasingStyle.Quad, s);
					crosshairFrame.CrosshairS:TweenPosition(UDim2.new(0.5, 0, 0.5, uiSpread), Enum.EasingDirection.InOut, Enum.EasingStyle.Quad, s);
					crosshairFrame.CrosshairW:TweenPosition(UDim2.new(0.5, -uiSpread, 0.5, 0), Enum.EasingDirection.InOut, Enum.EasingStyle.Quad, s);
					crosshairFrame.CrosshairE:TweenPosition(UDim2.new(0.5, uiSpread, 0.5, 0), Enum.EasingDirection.InOut, Enum.EasingStyle.Quad, s);
				else
					crosshairFrame.Visible = false;
				end
				if configurations.UseScopeGui and properties.CanAimDown then
					scopeFrame.Visible = true;
					aimDownScope = true;
				else
					scopeFrame.Visible = false;
					aimDownScope = false;
				end
				
			else
				if focusDuration > 0 and characterProperties.IsFocused then
					if properties.InFocusDuration then
						crosshairFrame.Visible = true;
	
						if properties.InFocusDuration then
							properties.FocusCharge	= focusDuration and math.clamp((tick()-properties.InFocusDuration)/focusDuration, 0, 1) or 0;
						else
							properties.FocusCharge = 0;
						end
						
						local uiSpread = properties.Reloading and 14 or (1-properties.FocusCharge)*20;
						crosshairFrame.CrosshairN:TweenPosition(UDim2.new(0.5, 0, 0.5, -uiSpread), Enum.EasingDirection.InOut, Enum.EasingStyle.Quad, s);
						crosshairFrame.CrosshairS:TweenPosition(UDim2.new(0.5, 0, 0.5, uiSpread), Enum.EasingDirection.InOut, Enum.EasingStyle.Quad, s);
						crosshairFrame.CrosshairW:TweenPosition(UDim2.new(0.5, -uiSpread, 0.5, 0), Enum.EasingDirection.InOut, Enum.EasingStyle.Quad, s);
						crosshairFrame.CrosshairE:TweenPosition(UDim2.new(0.5, uiSpread, 0.5, 0), Enum.EasingDirection.InOut, Enum.EasingStyle.Quad, s);
					end
					scopeFrame.Visible = false;
					aimDownScope = false;
				else
					scopeFrame.Visible = false;
					aimDownScope = false;
					crosshairFrame.Visible = true;
					local uiSpread = properties.Reloading and 14 or math.clamp(properties.Inaccuracy*configurations.UISpreadIntensity, 2, 999);
					crosshairFrame.CrosshairN:TweenPosition(UDim2.new(0.5, 0, 0.5, -uiSpread), Enum.EasingDirection.InOut, Enum.EasingStyle.Quint, s);
					crosshairFrame.CrosshairS:TweenPosition(UDim2.new(0.5, 0, 0.5, uiSpread), Enum.EasingDirection.InOut, Enum.EasingStyle.Quint, s);
					crosshairFrame.CrosshairW:TweenPosition(UDim2.new(0.5, -uiSpread, 0.5, 0), Enum.EasingDirection.InOut, Enum.EasingStyle.Quint, s);
					crosshairFrame.CrosshairE:TweenPosition(UDim2.new(0.5, uiSpread, 0.5, 0), Enum.EasingDirection.InOut, Enum.EasingStyle.Quint, s);
				end
			end
			
			if modData.Settings.CinematicMode == 1 then
				ammoCounter.Visible = false;
			else
				ammoCounter.Visible = true;
			end
			
			if properties.AdsPartsHidden ~= aimDownScope then
				for a=1, #toolModelObjsList do
					local obj = toolModelObjsList[a];
					if not obj:IsA("BasePart") or obj:GetAttribute("HideOnAds") ~= true then
						continue;
					end
					obj.Transparency = aimDownScope and 1 or 0.8;
				end
				
				properties.AdsPartsHidden = aimDownScope;
			end

			local sprintAnimationCanPlay = false;
			local animationTracksPlaying = toolAnimator:GetKeysPlaying{"Focus"; "Load"; "PrimaryFire"; "TacticalReload"; "Reload"; "SpecialReload"; "SpecialLoad"} or {};
			local isPlayingWeaponAnim = next(animationTracksPlaying) ~= nil;

			local function isAnimPlaying(keys)
				return modTables.ContainKeys(animationTracksPlaying, keys);
			end

			if animations["Focus"] then
				sprintAnimationCanPlay = false;
				local isPerformingAction = isAnimPlaying{"Load"; "TacticalReload"; "Reload"; "SpecialReload"; "SpecialLoad"};
				
				local focusTrack = toolAnimator:GetPlaying("Focus");
				if focusTrack then
					
					if not characterProperties.IsFocused 
						or (isPerformingAction and animations["Focus"].StopOnAction == true)
						or properties.InFocusDuration == nil then
						
						toolAnimator:Stop("Focus");
						if animations["FocusCore"] then
							toolAnimator:Stop("FocusCore");
						end
					else

						focusTrack.TimePosition = math.clamp(properties.FocusCharge, 0, 0.99);
						if animations["FocusCore"] and properties.FocusCharge > 0.99 then
							local focusCoreTrack = toolAnimator:GetPlaying("FocusCore");
							if focusCoreTrack == nil then
								toolAnimator:Play("FocusCore", {
									FadeTime=0;
								});
							end
						end
					end
					
				elseif isAnimPlaying{"FocusCore"} then
					
				else
					if isPerformingAction and animations["Focus"].StopOnAction == true then
					elseif characterProperties.IsFocused and properties.InFocusDuration then
						toolAnimator:Play("Focus", {
							PlaySpeed=0;
						})
					end
				end
				
				if properties.FlipPlayingWeaponAnim ~= isPlayingWeaponAnim then
					properties.FlipPlayingWeaponAnim = isPlayingWeaponAnim;
					properties.LastPlayingWeaponAnim = tick();
				end
				
				if isPlayingWeaponAnim then
					
					if properties.FocusWaistRotation then
						characterProperties.Joints.WaistY = properties.FocusWaistRotation;
					end
				else
					if properties.UnfocusWaistRotation then
						characterProperties.Joints.WaistY = properties.UnfocusWaistRotation;
					end
				end
				
			else
				--
				if characterProperties.IsMoving then
					if characterProperties.IsSprinting then
						sprintAnimationCanPlay = true;
						
					else
						sprintAnimationCanPlay = false;
					end
				else
					sprintAnimationCanPlay = false;
				end
			end

			if isAnimPlaying{"Empty";} then
				sprintAnimationCanPlay = false;
			end
			if isAnimPlaying{"Inspect"; "Inspect2"} then
				sprintAnimationCanPlay = false;
			end
			if isPlayingWeaponAnim then
				sprintAnimationCanPlay = false;
			end
			
			if characterProperties.IsFocused or properties.IsPrimaryFiring then
				sprintAnimationCanPlay = false;
			end
			
			if sprintAnimationCanPlay == true then
				if properties.LastSprintAnimationCanPlay == nil then
					properties.LastSprintAnimationCanPlay = tick();
				end
				if properties.LastSprintAnimationCanPlay == nil or tick()-properties.LastSprintAnimationCanPlay <= 1 then
					sprintAnimationCanPlay = false;
				end
			else
				properties.LastSprintAnimationCanPlay = nil;
			end
			
			if sprintAnimationCanPlay then
				if animations["Sprint"] then
					if properties.SprintWaistRotation then
						characterProperties.Joints.WaistY = properties.SprintWaistRotation
					end
					if not isAnimPlaying{"Sprint"} 
						and (properties.LastPlayingWeaponAnim == nil 
						or tick()-properties.LastPlayingWeaponAnim >=0.2) then
						toolAnimator:Play("Sprint", {
							FadeTime = 0.3;
						});
						
					end
				else
					toolAnimator:Stop("Core", {FadeTime=0.2;});
				end
			else
				if animations["Sprint"] then
					characterProperties.Joints.WaistY = properties.WaistRotation
					toolAnimator:Stop("Sprint", {FadeTime=0.2;});
					
				else
					local coreTrack = toolAnimator:GetPlaying("Core");
					if coreTrack == nil then
						toolAnimator:Play("Core", {FadeTime=0.1});
					end
					
				end
			end

			if properties.Reloading then
				reloadLabel.Visible = true;
				local reloadTime = configurations.ReloadTime;
				local alpha = math.clamp((tick() - properties.ReloadCooldown)/(reloadTime-0.1), 0, 1);
				reloadRadial:UpdateLabel(alpha);
			else
				reloadLabel.Visible = false;
			end
			
			
			if rootPart:GetAttribute("WaistRotation") then
				characterProperties.Joints.WaistY = math.rad(tonumber(rootPart:GetAttribute("WaistRotation")) or 0);
				
			elseif properties.WaistRotation then
				characterProperties.Joints.WaistY = properties.WaistRotation;
				
			end
			
			equipmentClass:ProcessModifiers("OnWeaponRender", statusDisplay);

			-- MARK: WeaponStatusDisplay;
			for k, statusInfo in pairs(statusDisplay) do
				local statusLabel = weaponStatusHud:FindFirstChild(k);
				if statusLabel == nil then
					statusLabel = weaponStatusTemplate:Clone();
					statusLabel.Name = k;
					statusLabel.LayoutOrder = statusInfo.Order;
					statusLabel.Parent = weaponStatusHud;

					if statusInfo.KeyString then
						local hotKeyFrame = statusLabel:WaitForChild("hotKey");
						hotKeyFrame.Visible = true;
						local hotKeyLabel = hotKeyFrame:WaitForChild("button");
						hotKeyLabel.Text = modKeyBindsHandler:ToString(statusInfo.KeyString);
					end

					if statusInfo.ModItemId then
						local modLib = modItemModsLibrary.Get(statusInfo.ModItemId);
						statusLabel:SetAttribute("Icon", modLib.Icon);
						if modLib.Color then
							statusLabel:SetAttribute("Color", modLib.Color:ToHex());
						end
					end
				end

				statusLabel.Visible = statusInfo.Hide ~= true;
				statusLabel.Text = statusInfo.Text;

				local statusIcon = statusLabel:WaitForChild("Icon");
				local iconStr = statusInfo.Icon or statusLabel:GetAttribute("Icon");
				if iconStr then
					statusIcon.Visible = true;
					statusIcon.Image = iconStr;
				else
					statusIcon.Visible = false;
				end

				local uiGradient = statusIcon:WaitForChild("UIGradient") :: UIGradient;
				local colorHex = statusInfo.Color or statusLabel:GetAttribute("Color");
				if colorHex then
					local newColor = Color3.fromHex(colorHex);
					local hue, sat, val = newColor:ToHSV();
					local baseColor = Color3.fromHSV(hue, sat*0.5, math.min(val*1.3, 1));

					uiGradient.Color = ColorSequence.new({
						ColorSequenceKeypoint.new(0, baseColor);
						ColorSequenceKeypoint.new(0.499, baseColor);
						ColorSequenceKeypoint.new(0.5, newColor);
						ColorSequenceKeypoint.new(1, newColor);
					});
				end

				if statusInfo.ColorPercent then
					if statusInfo.ColorPercent <= 0 then
						uiGradient.Offset = Vector2.new(0, 0.5);
					elseif statusInfo.ColorPercent >= 1 then
						uiGradient.Offset = Vector2.new(0, -0.5);
					else
						uiGradient.Offset = Vector2.new(0, modMath.MapNum(statusInfo.ColorPercent, 0, 1, 0.4, -0.4, true));
					end
				else
					uiGradient.Offset = Vector2.new(0, 0.5);
				end
			end

			local wStatusHudVisible = false;
			for _, obj in pairs(weaponStatusHud:GetChildren()) do
				if not obj:IsA("GuiObject") then continue end;
				if statusDisplay[obj.Name] == nil then
					obj.Visible = false;
					game.Debris:AddItem(obj, 0);
				else
					if obj.Visible then
						wStatusHudVisible = true;
					end
				end
			end
			weaponStatusHud.Visible = wStatusHudVisible;
		end

		local function getAudioSourcePoint()
			local soundOrigin = primaryHandle;

			if characterProperties.FirstPersonCamera then
				soundOrigin = camera;
			end

			return soundOrigin;
		end

		local function playWeaponSound(id, looped)
			local soundOrigin = getAudioSourcePoint();
			
			local sound = modAudio.PlayReplicated(id, soundOrigin, looped);
			if sound then
				sound:SetAttribute("WeaponAudio", true);
			end
			
			return sound;
		end

		local function ejectShell(ejectCFrame: CFrame) -- objectTable.CaseOutPoint.WorldCFrame
			local ejectBullet = configurations.BulletEject and prefabStorage:WaitForChild(configurations.BulletEject) or nil;
			local newEjectBullet = ejectBullet:Clone();
			game.Debris:AddItem(newEjectBullet, 5);
	
			newEjectBullet.CFrame = ejectCFrame * (configurations.BulletEjectOffset or CFrame.new()) 
				* CFrame.Angles(0, math.rad(math.random(0, 360)), math.rad(math.random(-35, 35)));
			newEjectBullet.Parent = workspace.Debris;
	
			newEjectBullet:ApplyImpulse(ejectCFrame.RightVector * 0.05);
	
			task.wait(0.1);
			for _, obj in pairs(newEjectBullet:GetDescendants()) do
				if obj:IsA("Motor6D") and obj:GetAttribute("BreakJoint") == true then
					obj:Destroy();
				end
			end
		end


		local function processPrimaryFire()
			local rawRpm: number = configurations.Rpm;

			local changeRef = {RawRpm=rawRpm};
			equipmentClass:ProcessModifiers("OnPrimaryFire", changeRef);
			rawRpm = changeRef.RawRpm;
	
			local baseFr = 60/rawRpm;
			local firerate = baseFr;
			
			if configurations.RapidFire then
				local f = math.clamp((tick()- properties.RapidFireStart)/configurations.RapidFire, 0, 1);
				firerate = baseFr + f*(DELTA - baseFr);
			end
	
			if configurations.RpmScaler and configurations.RpmScaler ~= 0 then
				firerate = firerate * (1-configurations.RpmScaler);
			end

			firerate = math.clamp(firerate, DELTA, 999);
			
			if properties.OnShotTick and tick()-properties.OnShotTick < firerate then return end;
			
			if properties.IsPrimaryFiring then return end
			properties.IsPrimaryFiring = true;
			
			local fireFocusCharge = properties.FocusCharge;
			properties.InFocusDuration = nil;
			
			-- apply recoil;
			local xRecoilV = configurations.XRecoil or 0.01;
			local yRecoilV = configurations.YRecoil or 0.02;
			local xR = math.random(0, xRecoilV*1000)/1000 * (math.random(0, 1) == 1 and 1 or -1);
			local yR = yRecoilV;
	
			if characterProperties.IsFocused and configurations.Deadeye then
				yR = yR * math.clamp(1-configurations.Deadeye, 0, 1);
			end
			
			if modCharacter.DizzyZAim or characterProperties.Ragdoll == true then
				mouseProperties.ZAngOffset = mouseProperties.ZAngOffset + (xR * 2);
				xR = xR *2.5;
				yR = yR *2.5;
			end
			
			mouseProperties.XAngOffset = mouseProperties.XAngOffset + (xR * modConfigurations.RecoilScaler);
			mouseProperties.YAngOffset = mouseProperties.YAngOffset + (yR * modConfigurations.RecoilScaler);

			properties.OnShotTick = tick();

			local primaryFireTrackKey = "PrimaryFire";
			local roll = math.random(1, 10);
			if animations["PrimaryFire2"] and roll >= 7 then
				primaryFireTrackKey = "PrimaryFire2";
			end
			if animations["LastFire"] and properties.Ammo == 1 then --properties.Ammo == 1 then
				primaryFireTrackKey = "LastFire";
			end
			if animations["Sprint"] then
				toolAnimator:Stop("Sprint", {FadeTime=0.05});
			end
			
			if characterProperties.FirstPersonCamera and properties.AimDownViewModel and characterProperties.IsFocused then
				toolAnimator:Play(primaryFireTrackKey, {
					PlayWeight=animations[primaryFireTrackKey].FocusWeight or 0.2;
				});
				
			else
				if animations[primaryFireTrackKey].LoopMarker == true then
					local primaryFireTrack = toolAnimator:GetPlaying(primaryFireTrackKey);
					if primaryFireTrack == nil then
						toolAnimator:Play(primaryFireTrackKey, {
							FadeTime = 0;
						});
					end
				else
					toolAnimator:Play(primaryFireTrackKey, {FadeTime=0;});
				end
			end

			task.spawn(function()
				for a=1, #handler.Prefabs do
					local toolModel = handler.Prefabs[a];

					if properties.Ammo <= 0 then
						playWeaponSound(audio.Empty.Id);
						mouseProperties.Mouse1Down = false;
						return 
					end;
					if properties.Reloading then return end;

					local shotTick = tick();
					local spreadRandom = Random.new(shotTick*10000);
					local shotData = {
						ToolModelIndex=a;
					};
					
					local ammoCost = math.min(configurations.AmmoCost or 1, properties.Ammo);
					
					if configurations.Triplethreat then
						ammoCost = properties.InfType == 2 and 3 or math.min(properties.Ammo, 3);
						
					end
					
					if configurations.Rocketman and characterProperties.GroundObject == nil and getReserveAmmo() > 0 and humanoid:GetState() ~= Enum.HumanoidStateType.Swimming then
						properties.MaxAmmo = properties.MaxAmmo -(properties.InfType == 2 and 0 or ammoCost);
						shotData.Rocketman = true;
						
					else
						properties.Ammo = properties.Ammo -(properties.InfType == 2 and 0 or ammoCost);
						
					end

					updateAmmoCounter();
					if toolPackage.OnAmmoUpdate then 
						toolPackage.OnAmmoUpdate(handler); 
					end
					
					if modData:GetSetting("DisableParticle3D") ~= 1 and configurations.BulletEject then
						local caseOutPoint: Attachment = toolModel:FindFirstChild("CaseOut", true);
						if caseOutPoint and caseOutPoint:IsA("Attachment") and configurations.BulletEjectDelayTime == nil then
							task.spawn(ejectShell, caseOutPoint.WorldCFrame);
						end
					end

					if audio["PrimaryFire"].Looped then
						if properties.LoopedPrimaryFireAudio == nil then
							properties.LoopedPrimaryFireAudio = playWeaponSound(audio.PrimaryFire.Id, true);
							if toolPackage.OnPrimaryFireAudio then
								toolPackage.OnPrimaryFireAudio(handler, properties.LoopedPrimaryFireAudio, 1);
							end
						else
							properties.LoopedPrimaryFireAudio.Parent = getAudioSourcePoint();
						end

					else
						local primaryFireSound = playWeaponSound(audio.PrimaryFire.Id, false);
						if primaryFireSound then
							if configurations.RapidFire and properties.RapidFireStart then
								local f = math.clamp((tick()-properties.RapidFireStart)/configurations.RapidFire, 0, 1);
								primaryFireSound.PlaybackSpeed = 1+(f/2);
	
							else
								local baseRpm = configurations:GetBase("Rpm");
								local rpmDif = 0;
								if rawRpm > baseRpm then
									rpmDif = (rawRpm/baseRpm)-1; -- > 0
								elseif rawRpm < baseRpm then
									rpmDif = (1/(baseRpm/rawRpm))-1; -- < 0
								end
								
								primaryFireSound.PlaybackSpeed = (audio.PrimaryFire.Pitch or 1) + rpmDif; --+ (math.noise(properties.Ammo+0.1, 0.1, 0.1)/5);
								 
							end
						end
						if configurations.PrimaryFireAudio ~= nil then configurations.PrimaryFireAudio(primaryFireSound, 1); end
					end

					local multishot = type(configurations.Multishot) == "table" 
						and spreadRandom:NextInteger(configurations.Multishot.Min, configurations.Multishot.Max) 
						or configurations.Multishot;
					
					if configurations.Triplethreat then
						multishot = ammoCost;
					end


					local spreadRollStart = spreadRandom:NextNumber()*2*math.pi;
					local spreadPitch = multishot <= 1 and spreadRandom:NextNumber()^2 or 1;
					
					local function spread(direction, maxSpreadAngle, multiIndex)
						maxSpreadAngle = math.clamp(maxSpreadAngle, 0, 90);
						local deflection = math.rad(maxSpreadAngle) * spreadPitch;
						if multishot > 1 then
							local mSpread = spreadRandom:NextNumber(-0.5, 0.5);
							
							if math.sign(mSpread) == -1 then
								mSpread = -1 * (math.abs(mSpread)^1.4) + 0.5;
							else
								mSpread = mSpread^1.4 + 0.5;
							end
							
							deflection = deflection * mSpread;
						end
						
						local cf = CFrame.new(Vector3.new(), direction);
						
						--multiIndex
						cf = cf*CFrame.Angles(0, 0, spreadRollStart + ((math.pi*2/multishot)*multiIndex) ); -- roll cframe
						cf = cf*CFrame.Angles(deflection, 0, 0); --pitch cframe;
						
						return cf.lookVector;
					end

					local bulletOrigin: Attachment = toolModel:FindFirstChild("BulletOrigin", true);
					if configurations.GenerateMuzzle ~= false then
						local muzzleOrigin = toolModel:FindFirstChild("MuzzleOrigin", true);
						if muzzleOrigin then
							modWeaponMechanics.CreateMuzzle(muzzleOrigin, bulletOrigin, multishot, configurations.GenerateMuzzle ~= false);
						end
					end
			
					shotData.ShotOrigin = bulletOrigin;
					if configurations.BulletMode == modWeaponAttributes.BulletModes.Hitscan then
						shotData.TargetPoints = {};
						shotData.Victims = {};
						shotData.HitInfoList = {};
						
					elseif configurations.BulletMode == modWeaponAttributes.BulletModes.Projectile then
						shotData.Projectiles = {};
						
					end
					
					shotData.ShotPoint = shotData.ShotOrigin.WorldPosition;
					shotData.Direction = mouseProperties.Direction;
					
					if characterProperties.IsSwimming or characterProperties.IsAntiGravity then
						rootPart:ApplyImpulse(-camera.CFrame.LookVector*Vector3.new(1, 0.5, 1) * configurations.RecoilStregth*150); -- recoil force;
						
					else
						rootPart:ApplyImpulse(-camera.CFrame.LookVector*Vector3.new(1, 0.5, 1) * configurations.RecoilStregth*40); -- recoil force;
					end

					-- MARK: multishot
					for multiIndex=1, multishot do
						local newInaccuracy = properties.Inaccuracy;
						if newInaccuracy == nil then return end;
						if multishot <= 1 or (multishot == 2 and multiIndex == 1) then
							local standInaccuracy = configurations.StandInaccuracy;
							local inaccuracyDecaySpeed = configurations.InaccDecaySpeed or 1;
							local inaccuracyDeduction = standInaccuracy * math.clamp( (shotTick - properties.LastFired)/inaccuracyDecaySpeed, 0, 1);
							newInaccuracy = newInaccuracy - inaccuracyDeduction;
						end
						local spreadedDirection = spread(shotData.Direction, math.max(newInaccuracy, 0), multiIndex);
						
						if configurations.BulletMode == modWeaponAttributes.BulletModes.Hitscan then

							local healthCompList = {};
							local firstImpactPoint = nil;
							local function onCast(basePart, position, normal, material, index, distance)
								if firstImpactPoint == nil then
									firstImpactPoint = position;
								end
								
								if basePart == nil then return end;
								local model = basePart.Parent;

								local healthComp: HealthComp? = modHealthComponent.getByModel(model);
								if healthComp then 
									if healthCompList[healthComp:GetModel()] then return end;
									healthCompList[healthComp:GetModel()] = true;
								end;

								local npcInstanceModule = model:FindFirstChild("NpcClassInstance");
								local isHeadshot = npcInstanceModule and basePart.Name == "Head" or basePart:GetAttribute("IsHead") == true or nil;
								
								equipmentClass:ProcessModifiers("OnBulletHit", {
									OriginPoint=shotData.ShotPoint;
									
									TargetPart=basePart;
									TargetPoint=position;
									TargetNormal=normal;
									TargetMaterial=material;
									TargetIndex=index;
									TargetDistance=distance;
	
									TargetModel=model;
									IsHeadshot=isHeadshot;
								});
								table.insert(shotData.HitInfoList, {
									Part=basePart;
									Position=position;
									Normal=normal;
									Index=index;
								});
	
								if healthComp then
									local weakPointGui = model:FindFirstChild("WeakpointTarget", true);
									if weakPointGui and weakPointGui.Parent ~= basePart then
										local wpRaycastParams = RaycastParams.new();
										wpRaycastParams.FilterType = Enum.RaycastFilterType.Include;
										wpRaycastParams.IgnoreWater = true
										wpRaycastParams.CollisionGroup = "Raycast";
										wpRaycastParams.FilterDescendantsInstances = {weakPointGui.Parent};
										
										local wpOrigin = position - spreadedDirection*3;
										
										local raycastResult = workspace:Raycast(wpOrigin, spreadedDirection*6, wpRaycastParams);
										
										if raycastResult and raycastResult.Instance then
											basePart = raycastResult.Instance;
										end
									end
									local weakpointTarget = basePart:FindFirstChild("WeakpointTarget");
									if weakpointTarget then
										modAudio.Play("WeakPointImpact", nil, false, 1/((index+1)*0.9));
										weakpointTarget:Destroy();
									end

									modWeaponMechanics.BulletHitSound{
										BasePart=basePart;
										Index=index;
									}

									if modData and modData:GetSetting("DisableParticle3D") ~= 1 and multiIndex == 1 and Debugger.ClientFps > 45 then
										modParticleSprinkler:Emit{
											Type=1;
											Origin=CFrame.new(position);
											Velocity=normal;
											MinSpawnCount=1;
											MaxSpawnCount=4;
										};
									else
										if (healthComp.CompOwner.ClassName == "NpcClass" or healthComp.CompOwner.ClassName == "PlayerClass") 
											and configurations.GenerateBloodEffect ~= false 
											and Debugger.ClientFps > 25 then
											modWeaponMechanics.CreateBlood(basePart, position, (mouseProperties.Focus.p-position).unit, camera);
										end;
									end
									table.insert(shotData.Victims, {Object=basePart; Index=index;});
									

								else


								end

								modWeaponMechanics.ImpactSound{
									BasePart = basePart;
									Point = position;
									Normal = normal;
								};

								if configurations.GeneratesBulletHoles ~= false and basePart.Transparency <= 0.9 then
									task.spawn(function() 
										modWeaponMechanics.CreateBulletHole(basePart, position, normal);
									end)
								end;
	
								return;
							end
							
							local rayWhitelist = CollectionService:GetTagged("TargetableEntities") or {};
							table.insert(rayWhitelist, workspace.Environment);
							table.insert(rayWhitelist, workspace.Characters);
							table.insert(rayWhitelist, workspace.Terrain);
	
							local scanPoint = modWeaponMechanics.CastHitscanRay{
								Origin = mouseProperties.Focus.p;
								Direction = spreadedDirection;
								IncludeList = rayWhitelist;
								Range = configurations.BulletRange;
							};
							
							local shotOrigin = playerHead.Position + -Vector3.yAxis*0.4 + playerHead.CFrame.RightVector *(characterProperties.LeftSideCamera and -1 or 1);
	
							local reDirection = (scanPoint-mouseProperties.Focus.Position).Unit;
							local newDirection = (scanPoint-shotOrigin).Unit;
	
							local bulletEnd = modWeaponMechanics.CastHitscanRay{
								Origin = shotOrigin;
								Direction = newDirection;
								IncludeList = rayWhitelist;
								Range = configurations.BulletRange;
								MaxPierce = properties.Piercing;
								PenTable = configurations.Penetration;
								PenReDirection = reDirection;
								
								OnCastFunc = onCast;
								OnPenFunc = function(packet)
									task.spawn(function()
										local basePart = packet.BasePart;
										local position = packet.Position;
										local normal = packet.Normal;
										
										if configurations.GeneratesBulletHoles ~= false and basePart.Transparency <= 0.9 then
											task.spawn(function() 
												modWeaponMechanics.CreateBulletHole(basePart, position, normal);
											end)
										end;
										modWeaponMechanics.ImpactSound{
											BasePart = basePart;
											Point = position;
											Normal = normal;
										}
									end)
								end
							};
							
							table.insert(shotData.TargetPoints, bulletEnd);
							
							shotData.TracerColor = Color3.fromRGB(255, 255, 255);

							if configurations.GenerateTracers ~= false then
								modWeaponMechanics.CreateTracer(bulletOrigin, firstImpactPoint, camera, shotData.TracerColor)
							end;
							
							
						elseif configurations.BulletMode == modWeaponAttributes.BulletModes.Projectile then
							
							local rayWhitelist = CollectionService:GetTagged("TargetableEntities") or {};
							table.insert(rayWhitelist, workspace.Environment);
							table.insert(rayWhitelist, workspace.Characters);
							table.insert(rayWhitelist, workspace.Terrain);
							projRaycast.FilterDescendantsInstances = rayWhitelist;

							-- Gets where player is aiming at;
							-- Get hitscan point from crosshair
							local scanPoint = modWeaponMechanics.CastHitscanRay{
								Origin = mouseProperties.Focus.p;
								Direction = mouseProperties.Direction;
								IncludeList = rayWhitelist;
								Range = configurations.BulletRange;
							};

							local newDirection = (scanPoint-playerHead.Position).Unit;
							
							local origin = playerHead.Position;
							spreadedDirection = newDirection * arcTracerConfig.Velocity;
							local bulletWorldPosition = bulletOrigin.WorldPosition;

							-- Gets where player can hit.
							-- Get hitscan point from head using direction provided by crosshair hitscan.
							local rayPoint = modWeaponMechanics.CastHitscanRay{
								Origin = origin;
								Direction = spreadedDirection;
								IncludeList = rayWhitelist;
								Range = arcTracerConfig.Velocity;
							};
							
							-- Turn projectile landing point to direction from projectile origin.
							local rayDisplacement, offsetDir;
							if rayPoint then
								rayDisplacement = (rayPoint-bulletWorldPosition);
								offsetDir = rayDisplacement.Unit;
								
							else
								rayDisplacement = ((shotData.ShotPoint + newDirection* arcTracerConfig.Velocity) - bulletWorldPosition);
								offsetDir = rayDisplacement.Unit;
								
							end
							
							local pdata = {};
							pdata.Origin = CFrame.new(bulletWorldPosition);
							pdata.Orientation = bulletOrigin.WorldOrientation;
							pdata.Direction = offsetDir;
							
							if multiIndex > 1 then
								local leftRight = multiIndex%2 == 0 and 1 or -1;
								local radMultiplier = math.floor(multiIndex/2);
								
								local deg = math.max(3/(rayDisplacement.Magnitude/32), 1);
								pdata.Direction = CFrame.Angles(0, math.rad(deg * leftRight * radMultiplier), 0):VectorToWorldSpace(offsetDir);
							end
							
							
							if modConfigurations.AutoAdjustProjectileAim == true then
								local raycastResult = workspace:Raycast(origin, mouseProperties.Direction*configurations.BulletRange, projRaycast);
								local rayEndPoint = raycastResult and raycastResult.Position or (origin + mouseProperties.Direction*configurations.BulletRange);
								local dist = (origin-rayEndPoint).Magnitude;
								
								pdata.RayEndPoint = rayEndPoint;
								pdata.Dist = dist;
								
							end
							
							table.insert(shotData.Projectiles, pdata);
						end
					end -- for multishot loop end


					if (configurations.FocusDuration or 0) > 0 then
						shotData.FocusCharge = fireFocusCharge;
						properties.InFocusDuration = nil;
	
					end
					
					if shotData.Victims and #shotData.Victims > 0 then
						hitmarker.Visible = true;
						hitmarker:SetAttribute("Timer", tick());
						
						task.delay(0.35, function()
							if tick()-hitmarker:GetAttribute("Timer") >= 0.35 then
								hitmarker.Visible = false;
							end; 
	
							for a=#shotData.Victims, 1, -1 do
								local victimPacket = shotData.Victims[a];
								
								if victimPacket.Humanoid then
									local window = modClientGuis.getWindow("EntityHealthHud");
									if window then
										window.Binds.TryHookEntity(victimPacket.Humanoid.Parent);
									end
								end
							end
						end)
					end
					
					shotData.ShotId = modData.ShotIdGen:NextInteger(1, 99);
					shotData.AmmoData = {
						Ammo = properties.Ammo;
						MaxAmmo = properties.MaxAmmo;
					};
					remoteToolInputHandler:FireServer(modRemotesManager.Compress{
						Action = "action";
						Siid = siid;
						
						ActionIndex = 1;
						ShotData = shotData
					});

					properties.LastFired = shotTick;
					
					if configurations.TriggerCycleDelay then
						task.wait(configurations.TriggerCycleDelay);
					end

				end -- for prefab loop end
			end)

			if configurations.RapidFire then
				local f = math.clamp((tick() - properties.RapidFireStart)/configurations.RapidFire, 0, 1);
				firerate = baseFr + f*(DELTA - baseFr);
				
				if properties.LoopedPrimaryFireAudio then
					properties.LoopedPrimaryFireAudio.PlaybackSpeed = 1+(f/2);
				end
			end
			
			firerate = math.clamp(firerate, DELTA, 999);

			if toolPackage.OnPrimaryFire then
				toolPackage.OnPrimaryFire(handler);
			end
	
			repeat
				RunService.RenderStepped:Wait();
			until (tick()- properties.OnShotTick) >= firerate;
			
			properties.IsPrimaryFiring = false;
			return true;
		end
		

		local function reload()
			if properties.Reloading then return end;
			
			if properties.InfAmmo == nil and getReserveAmmo(true) <= 0  then
				playWeaponSound(audio.Empty.Id);
				return 
			end;
			
			if properties.IsPrimaryFiring then 
				mouseProperties.Mouse1Down = false;
				
				repeat
					RunService.RenderStepped:Wait();
				until properties.IsPrimaryFiring ~= true;
			end;
			
			if properties.Ammo >= configurations.MagazineSize then return end;
			if (tick()- properties.ReloadCooldown) < 0.5+(0.25*properties.ReloadAttemptCount) then return end;
			properties.ReloadCooldown = tick();
			properties.ReloadAttemptCount = math.clamp(properties.ReloadAttemptCount +1, 0, 4);
			
			properties.Reloading = true;
			properties.LerpBody = false;

			toolAnimator:Stop("Inspect", {FadeTime=0});
			
			local reloadAnimKey = "Reload";
			local roll = math.random(1, 10);
			if animations["Reload2"] and roll >= 7 then
				reloadAnimKey = "Reload2";
			end

			if animations["TacticalReload"] and properties.Ammo > 0 then
				reloadAnimKey = "TacticalReload";
			end
			
			if toolPackage.DoSpecialReload then
				reloadAnimKey = toolPackage.DoSpecialReload(handler);
			end
			
			toolAnimator:Stop("Sprint", {FadeTime=0});
			
			if toolPackage.OnReload then
				toolPackage.OnReload(handler);
			end
	
			local reloadTime = configurations.ReloadTime;
			if configurations.ReloadMode == modWeaponAttributes.ReloadModes.Full then
				remoteToolInputHandler:FireServer(modRemotesManager.Compress{
					Action = "action";
					Siid = siid;
					
					ActionIndex = 3;
					ReloadStart = true;
				});

				local reloadAnimTrack = toolAnimator:Play(reloadAnimKey, {
					PlayLength=reloadTime;
				})
				
				if toolPackage.OnReloadAnimation and reloadAnimTrack then
					toolPackage.OnReloadAnimation(handler, reloadAnimTrack);
				end
				
				local reloadSound;
				if audio["Reload"] then
					reloadSound = playWeaponSound(audio.Reload.Id);
					reloadSound.PlaybackSpeed = reloadSound.TimeLength/reloadTime;
				end
				
				getReserveAmmo(true);
				
				local reloadDuration = math.clamp(reloadTime, 0.05, 20);
				local reloadComplete = 0;
				task.wait(reloadDuration);
				reloadComplete = 2;
				
				if reloadComplete == 2 and tick()-reloadDuration >= reloadDuration then
					reloadComplete = 1;
				end
				
				reloadAnimTrack:Stop();
				if reloadSound then reloadSound:Stop(); end;
				
				if reloadComplete == 1 and character:IsAncestorOf(primaryToolPrefab) then
					remoteToolInputHandler:FireServer(modRemotesManager.Compress{
						Action = "action";
						Siid = siid;
						
						ActionIndex = 3;
						ReloadStart = false;
					});
					properties.LastSuccessfulReload = tick();
					
					local currentAmmo = properties.Ammo;
					local ammoNeeded = configurations.MagazineSize - currentAmmo;
					local newAmmo = configurations.MagazineSize;
					local newMaxAmmo = properties.MaxAmmo - ammoNeeded;
					
					if properties.NoMaxAmmo then
						newMaxAmmo = configurations.AmmoCapacity;
					end
	
					if newMaxAmmo < 0 then
						newAmmo = properties.MaxAmmo + currentAmmo;
						newMaxAmmo = 0
					end;
					
					if newAmmo < configurations.MagazineSize and properties.AvailableInvAmmo > 0 then
						local ammoToAdd = math.min(configurations.MagazineSize-newAmmo, properties.AvailableInvAmmo);
						newAmmo = newAmmo + ammoToAdd;
						properties.AvailableInvAmmo = properties.AvailableInvAmmo - ammoToAdd;
					end
					
					properties.Ammo = newAmmo;
					if properties.NoMaxAmmo then
						properties.MaxAmmo = configurations.AmmoCapacity;
					else
						properties.MaxAmmo = properties.InfType == nil and newMaxAmmo or configurations.AmmoCapacity;
					end
					
					properties.ReloadAttemptCount = 0;
					updateAmmoCounter();
				end
				
			elseif configurations.ReloadMode == modWeaponAttributes.ReloadModes.Single and properties.Ammo < configurations.MagazineSize then
				repeat
					properties.Reloading = true;

					remoteToolInputHandler:FireServer(modRemotesManager.Compress{
						Action = "action";
						Siid = siid;
						
						ActionIndex = 3;
						ReloadStart = true;
					});
					properties.ReloadCooldown = tick();
					
					local reloadAnimTrack = toolAnimator:Play(reloadAnimKey, {
						PlayLength=reloadTime;
					});
					
					if audio["Reload"] then
						playWeaponSound(audio.Reload.Id);
					end
					
					local reloadComplete = 0;
					task.wait(math.clamp(reloadTime, 0.05, 20));
					reloadComplete = 1;
					
					local ammoCost = configurations.AmmoCost or 1;
					if properties.Ammo + ammoCost > configurations.MagazineSize and reloadAnimTrack then
						reloadAnimTrack:Stop();
						break;
					end
					
					if reloadComplete == 2 and reloadAnimTrack then
						reloadAnimTrack:Stop();
						break;
						
					elseif reloadComplete == 1 then
						local reserveAmmo = getReserveAmmo();
						if storageItem and storageItem.ID == siid and reloadComplete and reserveAmmo > 0 then
							
							local ammoDelta = ammoCost;
							if configurations.DualShell then ammoDelta = 2; end
							if properties.Ammo + ammoDelta > configurations.MagazineSize then -- cap to ammolimit
								ammoDelta = math.clamp(configurations.MagazineSize - properties.Ammo, 0, ammoDelta);
							end
							
							local ammoFromMA = math.min(ammoDelta, properties.MaxAmmo);
							local ammoFromInv = 0;
							
							if properties.NoMaxAmmo then
								ammoFromMA = configurations.AmmoCapacity;
							end
	
							if ammoDelta-ammoFromMA > 0 then
								ammoFromInv = math.min(ammoDelta-ammoFromMA, properties.AvailableInvAmmo);
							end
							
							ammoDelta = ammoFromMA + ammoFromInv;
							
							if ammoDelta > 0 then
								properties.Ammo = properties.Ammo + ammoDelta;
								
								if ammoFromMA > 0 then
									if properties.NoMaxAmmo then
										properties.MaxAmmo = configurations.AmmoCapacity;
									else
										properties.MaxAmmo = properties.InfType == nil and (properties.MaxAmmo - ammoFromMA) or configurations.AmmoCapacity;
										properties.MaxAmmo = math.max(properties.MaxAmmo, 0);
									end
								end
								if ammoFromInv > 0 then
									properties.AvailableInvAmmo = properties.AvailableInvAmmo - ammoFromInv;
								end
								
							end
							
							remoteToolInputHandler:FireServer(modRemotesManager.Compress{
								Action = "action";
								Siid = siid;
								
								ActionIndex = 3;
								ReloadStart = false;
							});
							properties.LastSuccessfulReload = tick();
							properties.ReloadAttemptCount = 0;
							
							if ammoDelta <= 0 then break end;
						else
							break;
						end
						
					end
					
					updateAmmoCounter();
					if toolPackage.OnAmmoUpdate then
						toolPackage.OnAmmoUpdate(handler)
					end
					if properties.IsPrimaryFiring then break; end;

				until configurations.MagazineSize == 1 or properties.Ammo >= configurations.MagazineSize or getReserveAmmo(true) <= 0 or primaryToolPrefab.Parent == nil;
			end
			
			if animations["Empty"] and properties.Ammo > 0 then
				toolAnimator:Stop("Empty");
			end
			
			properties.LerpBody = true;
			properties.Reloading = false;
			
			updateAmmoCounter();
		end


		local function tryAutoReload()
			if properties.Reloading or getReserveAmmo(true) <= 0 then return end;
			
			reload();
			if toolPackage.OnAmmoUpdate then
				toolPackage.OnAmmoUpdate(handler);
			end
		end


		local function primaryFire()
			if configurations.CanUnfocusFire == false and properties.InFocusDuration == nil then
				return;
			end
			
			if (tick()-properties.LastSuccessfulReload) <= 0.2 then
				return;
			end
			
			if humanoid.Health > 0 and properties.CanPrimaryFire then
				properties.CanPrimaryFire = false;
				
				if animations["Inspect"] then
					toolAnimator:Stop("Inspect", {FadeTime=0});
				end
				
				if properties.Ammo > 0 then
					if animations["Empty"] then
						toolAnimator:Stop("Empty", {FadeTime=0});
					end
					
					if configurations.CanUnfocusFire == false and not characterProperties.IsFocused then
						properties.CanPrimaryFire = true;
						return;
					end
					
					if configurations.TriggerMode == modWeaponAttributes.TriggerModes.Semi then
						processPrimaryFire();
						
					elseif configurations.TriggerMode == modWeaponAttributes.TriggerModes.Automatic
						or configurations.TriggerMode == modWeaponAttributes.TriggerModes.SpinUp then
						
						if configurations.TriggerMode == modWeaponAttributes.TriggerModes.SpinUp then
							if properties.SpinFloat == nil then properties.SpinFloat = 0 end;
							properties.IsSpinning = true;
							
							local function revFunc()
								while (properties.SpinFloat or 0) < 1 and mouseProperties.Mouse1Down do
									task.wait();
								end
							end
							
							if audio["SpinUp"] then properties.SpinUpSound = playWeaponSound(audio["SpinUp"].Id); end;
							if animations["SpinUp"] then 
								toolAnimator:Play("SpinUp", {FadeTime=configurations.SpinUpTime;});
							end;
							
							if configurations.SpinAndFire then
								coroutine.wrap(revFunc)();
							else
								revFunc();
							end
						end
						
						if configurations.TriggerMode == modWeaponAttributes.TriggerModes.Automatic or properties.SpinFloat >= 1 or configurations.SpinAndFire then
							if configurations.RapidFire then
								properties.RapidFireStart = tick();
							end

							local c = 0;
							repeat
								if processPrimaryFire() ~= true then
									task.wait(1/60);
								end
								if not characterProperties.CanAction then break; end;
								c = c +1;
								if math.fmod(c, 300) == 0 then
									break;
								end;
							until not mouseProperties.Mouse1Down;
							
							if properties.LoopedPrimaryFireAnim then
								local primaryFireAnim: AnimationTrack = properties.LoopedPrimaryFireAnim;
								if primaryFireAnim:GetAttribute("LoopEnd") then
									primaryFireAnim.TimePosition = primaryFireAnim:GetAttribute("LoopEnd") :: number;
								else
									primaryFireAnim:Stop();
								end
							end
						end
						
						if properties.SpinUpSound then properties.SpinUpSound:Stop(); end
						if configurations.TriggerMode == modWeaponAttributes.TriggerModes.SpinUp then
							if animations["SpinUp"] then
								toolAnimator:Stop("SpinUp", {FadeTime=configurations.SpinDownTime;});
							end
							if audio["SpinDown"] then properties.SpinDownSound = playWeaponSound(audio["SpinDown"].Id); end;
							properties.IsSpinning = nil;
							
						end
						if properties.LoopedPrimaryFireAudio ~= nil then
							local prevPrimaryFireAudio: Sound = properties.LoopedPrimaryFireAudio;
							properties.LoopedPrimaryFireAudio = nil;

							if toolPackage.OnPrimaryFireAudio ~= nil then
								toolPackage.OnPrimaryFireAudio(handler, prevPrimaryFireAudio, 2);
							else
								prevPrimaryFireAudio:Destroy();
							end
						end
					end
					
					if properties.Ammo <= 0 and configurations.AutoReload then
						tryAutoReload();
					end
					
				else
					if toolAnimator:GetPlaying("Empty") == nil then
						toolAnimator:Play("Empty");
					end
					playWeaponSound(audio.Empty.Id);
					if configurations.AutoReload then
						tryAutoReload();
					end
					
				end
				
				updateAmmoCounter();
				properties.CanPrimaryFire = true;
			end
		end


		local function PrimaryFireRequest()
			toolAnimator:Stop("Idle");
	
			task.spawn(function()
				if not characterProperties.CanAction then return end;

				if properties.Reloading then
					if configurations.ReloadMode == modWeaponAttributes.ReloadModes.Single and properties.Ammo > 0 then
						--MARK TODO: break reloading.
					else
						return;
					end 
				end

				if properties.CanPrimaryFire then primaryFire(); end;
			end)
		end;


		local function ReloadRequest()
			toolAnimator:Stop("Idle");
			
			if not properties.Reloading then 
				reload();
			else 
				playWeaponSound(audio.Empty.Id);
			end
			
			updateAmmoCounter();
			if toolPackage.OnAmmoUpdate then toolPackage.OnAmmoUpdate(handler); end
		end;


		local function InspectRequest() 
			toolAnimator:Stop("Idle");
				
			if not properties.Reloading and not properties.IsPrimaryFiring then
				properties.LerpBody = false;
				
				if sightViewModel then
					editPanelVisible = not editPanelVisible;
				end
				
				local roll = math.random(1,10)
				if animations["Inspect2"] and roll >= 7 then
					local inspectTrack = toolAnimator:Play("Inspect2");
					inspectTrack.Stopped:Wait();
				else
					local inspectTrack = toolAnimator:Play("Inspect");
					inspectTrack.Stopped:Wait();
				end
				
				properties.LerpBody = true;
			end
		end
		
		local function SpecialRequest()
			toolAnimator:Stop("Idle");
	
			Debugger:Warn("Special Request");
			return true;
		end


		if properties.UseViewModel == false then
			characterProperties.UseViewModel = false;
		end
		if configurations.CustomViewModel then
			characterProperties.CustomViewModel = configurations.CustomViewModel;
		end

		
		handler.Binds["KeyFire"] = PrimaryFireRequest;
		handler.Binds["KeyReload"] = ReloadRequest;
		handler.Binds["KeyInspect"] = InspectRequest;
		handler.Binds["KeyToggleSpecial"] = SpecialRequest;
		handler.Binds["KeyWalk"] = function()
			if animations["Idle"] == nil then return end;

			local idleTrack = toolAnimator:GetPlaying("Idle");
			if idleTrack then
				idleTrack:Stop();
			else
				toolAnimator:Play("Idle");
			end
		end

		if modWeaponMechanics.EquipUpdateExperience and not modConfigurations.DisableExperiencebar then
			local mechanicsBarElement: InterfaceElement = modClientGuis.getElement("MechanicsBar");
			if mechanicsBarElement then
				mechanicsBarElement.ProgressValue = (storageItem.Values.E or 0)/(storageItem.Values.EG or 100);
				mechanicsBarElement.ProgressType = "WeaponLevel";
				mechanicsBarElement.ProgressText = storageItem.Values.L;
			end
		end;
		
		if toolPackage.OnAmmoUpdate then
			toolPackage.OnAmmoUpdate(handler);
		end
		
		RunService:BindToRenderStep("WeaponRender", Enum.RenderPriority.Camera.Value, weaponRender);
		toolAnimator:LoadAnimations(animations, toolPackage.DefaultAnimatorState, handler.Prefabs);
		toolAnimator:Play("Core");

		-- Animation Markers
		toolAnimator:ConnectMarkerSignal("ShellEject", function(animKey, track, paramString)
			local toolModel = primaryToolPrefab;
			local prefabNamePrefix = paramString;
			for a=1, #handler.Prefabs do 
			if handler.Prefabs[a].Name:sub(1,#prefabNamePrefix) == prefabNamePrefix then 
				toolModel = handler.Prefabs[a]; 
				break; 
			end;
		end

		local caseOutPoint: Attachment = toolModel:FindFirstChild("CaseOut", true);
		if caseOutPoint == nil or not caseOutPoint:IsA("Attachment") then return end;
		
			task.spawn(ejectShell, caseOutPoint.WorldCFrame);
		end);
		

		-- Equip Load
		local cEquipTimeReduction = playerClass.Configurations.EquipTimeReduction;
		local cEquipTime = configurations.EquipLoadTime;
		if cEquipTimeReduction then
			cEquipTime = cEquipTime * math.clamp(1-cEquipTimeReduction, 0, 1);
		end

		local equipAnimKey = "Load";
		
		if animations["Load2"] and math.random(1, 10) <= 3 then
			equipAnimKey = "Load2";
		end

		if toolPackage.DoSpecialLoad then
			local animId = toolPackage.DoSpecialLoad(handler);
			if animId then
				equipAnimKey = animId;
			end
		end

		if equipAnimKey and animations[equipAnimKey]  then
			toolAnimator:Play("Load", {
				PlayLength=math.clamp(cEquipTime, 0.5, 2);
			});
		end
		if audio["Load"] then
			playWeaponSound(audio["Load"].Id);
		end
	
		task.delay(cEquipTime, function()
			properties.CanPrimaryFire = true;
			properties.CanAimDown = true;
		end);

		-- Unequip;
		handler.Garbage:Tag(function()
			if sightViewModel then
				properties.AimDownViewModel = sightViewModel.CFrame;
			end
			scopeFrame.Visible = false;
			
			characterProperties.Joints.WaistY = 0;
			characterProperties.Joints.WaistZ = 0;
			characterProperties.CustomViewModel = nil;
			characterProperties.AimDownSights = false;
			modCharacter.DevViewModel = nil;
			modCharacter.EquippedTool = nil;
			
			table.clear(statusDisplay);
		end)
	end
	

elseif RunService:IsServer() then -- MARK: Server
	function toolHandler.ActionEvent(handler: ToolHandlerInstance, packet)
		local characterClass: CharacterClass = handler.CharacterClass;

		local healthComp: HealthComp = characterClass.HealthComp;
		if healthComp.IsDead then return end;

		local equipmentClass = handler.EquipmentClass;
		local storageItem = handler.StorageItem;
		local primaryWeaponModel = handler.Prefabs[1];

		local actionIndex = packet.ActionIndex;

		if actionIndex == 1 then
			--MARK: Server Fire;
			local shotData = packet.ShotData;

			shotData.ShotBy = characterClass;
			shotData.ToolHandler = handler;

			shotData.ToolModel = handler.Prefabs[shotData.ToolModelIndex];
		
			modToolService.ProcessWeaponShot(shotData);


		elseif actionIndex == 3 then
			--MARK: Server Reload;
			local reloadStart = packet.ReloadStart == true;

			local configurations = equipmentClass.Configurations;
			local properties = equipmentClass.Properties;

			if characterClass.ClassName == "PlayerClass" then
				local profile = shared.modProfile:Get((characterClass :: PlayerClass):GetInstance());
				properties.InfiniteAmmo = profile.Cache.InfAmmo;

			elseif characterClass.HumanoidType == "Human" then
				
			end
			local infAmmo = properties.InfiniteAmmo;
			
			if reloadStart then
				properties.InitialReloadTick = tick();
				properties.ReloadDuration = configurations.ReloadTime;

				properties.ReloadModel = primaryWeaponModel;
				
				modOnGameEvents:Fire("OnToolReload", characterClass, storageItem, reloadStart);
				return;
			end
				
			if primaryWeaponModel ~= properties.ReloadModel then
				Debugger:Log("ReloadWeapon>> Reloading cancelled mismatch model.");
				return;
			end
			properties.ReloadModel = nil;

			local reloadTimeLapsed = tick()-properties.InitialReloadTick;
			local inValidTimeRange = (reloadTimeLapsed+0.3) >= configurations.ReloadTime;
			
			if not inValidTimeRange then
				Debugger:Warn("Invalid reload, out of valid time", configurations.ReloadTime)
				return;
			end;
			
			local defaultAmmoId = storageItem:GetValues("AmmoId") or configurations.AmmoType;

			local activeAmmoId;
			local availableInvAmmo = 0;

			local function loadAmmoId(ammoItemId)
				local ammoItemsList = {};

				if characterClass.ClassName == "PlayerClass" then
					local player: Player = (characterClass :: PlayerClass):GetInstance();
					ammoItemsList = shared.modStorage.ListItemIdFromStorages(ammoItemId, player, {
						"ammopouch"; "Inventory";
					});
				end

				local storageAmmoCount = 0;
				for a=1, #ammoItemsList do
					storageAmmoCount = storageAmmoCount + ammoItemsList[a].Item.Quantity;
				end

				if storageAmmoCount > 0 then
					activeAmmoId = ammoItemId;
					availableInvAmmo = storageAmmoCount;

				end
			end
			
			if configurations.ReloadMode == modWeaponAttributes.ReloadModes.Full then
				local ammo = storageItem:GetValues("A") or configurations.MagazineSize;
				ammo = math.min(ammo, configurations.MagazineSize);
				
				local maxAmmo = infAmmo == nil and storageItem:GetValues("MA") or configurations.AmmoCapacity;
				
				if properties.NoMaxAmmo then
					maxAmmo = configurations.AmmoCapacity;
				end

				local ammoNeeded = configurations.MagazineSize - ammo;
				local newMaxAmmo = infAmmo == nil and (maxAmmo - ammoNeeded) or maxAmmo;
				local newAmmo = configurations.MagazineSize;
				if newMaxAmmo < 0 then
					newAmmo = maxAmmo+ammo;
					newMaxAmmo = 0;
				end;
				
				if configurations.AmmoIds and newAmmo < configurations.MagazineSize then
					Debugger:Log("Searching for ammo in inventory");
					
					if newAmmo <= 0 then
						for a=1, #configurations.AmmoIds do
							loadAmmoId(configurations.AmmoIds[a]);
							if activeAmmoId then break; end;
						end
						
					else
						loadAmmoId(defaultAmmoId);
					end
					
					
					if activeAmmoId then
						newMaxAmmo = 0;
						
						local addAmmo = math.clamp(availableInvAmmo, 0, configurations.MagazineSize-ammo);
						newAmmo = ammo+addAmmo;
						
						if characterClass.ClassName == "PlayerClass" then
							local player: Player = (characterClass :: PlayerClass):GetInstance();
							shared.modStorage.RemoveItemIdFromStorages(activeAmmoId, player, addAmmo, {"ammopouch"; "Inventory";});
						end
						
						storageItem:SetValues("A", newAmmo);
						storageItem:SetValues("MA", newMaxAmmo);
						storageItem:SetValues("AmmoId", activeAmmoId);
					end
				end
				
				storageItem:SetValues("A", newAmmo);
				storageItem:SetValues("MA", newMaxAmmo);
				
				
			elseif configurations.ReloadMode == modWeaponAttributes.ReloadModes.Single then
				local ammo = storageItem:GetValues("A") or configurations.MagazineSize;
				local maxAmmo = infAmmo == nil and storageItem:GetValues("MA") or configurations.AmmoCapacity;
				
				if properties.NoMaxAmmo then
					maxAmmo = configurations.AmmoCapacity;
				end

				if maxAmmo > 1607317596421 then -- fix a bug;
					maxAmmo = math.min(maxAmmo, configurations.AmmoCapacity);
				end
				
				local ammoCost = configurations.AmmoCost or 1;
				if configurations.DualShell then
					ammoCost = 2;
				end
				if ammo + ammoCost > configurations.MagazineSize then
					ammoCost = math.clamp(configurations.MagazineSize-ammo, 1, ammoCost);
				end
				
				local ammoFromMA = 0;
				if maxAmmo > 0 and ammo < configurations.MagazineSize then
					ammoFromMA = math.min(ammoCost, maxAmmo);

					ammo = ammo +ammoFromMA;
					maxAmmo = infAmmo == nil and (maxAmmo - ammoFromMA) or maxAmmo;
					
					storageItem:SetValues("MA", maxAmmo);
				end
				
				if configurations.AmmoIds and ammoCost-ammoFromMA > 0 then
					Debugger:Log("Searching for ammo in inventory");

					loadAmmoId(defaultAmmoId);
				end
				
				local ammoFromInv = 0;
				if ammoCost-ammoFromMA > 0 then
					ammoFromInv = math.min(ammoCost-ammoFromMA, availableInvAmmo);
				end
				if ammoFromInv > 0 then
					ammo = ammo + ammoFromInv;

					if characterClass.ClassName == "PlayerClass" then
						local player: Player = (characterClass :: PlayerClass):GetInstance();
						shared.modStorage.RemoveItemIdFromStorages(activeAmmoId, player, ammoFromInv, {"ammopouch"; "Inventory";});
					end
				end

				storageItem:SetValues("A", ammo);
				storageItem:SetValues("UnixTime", DateTime.now().UnixTimestampMillis);
			end

			properties.Ammo = storageItem:GetValues("A");
			properties.MaxAmmo = storageItem:GetValues("MA");

			modOnGameEvents:Fire("OnToolReload", characterClass, storageItem, reloadStart);
		end
	end


	-- MARK: ServerEquip
	function toolHandler.ServerEquip(handler: ToolHandlerInstance)
		local healthComp: HealthComp = handler.CharacterClass.HealthComp;
		if healthComp.IsDead then return end;
		
		local storageItem: StorageItem = handler.StorageItem;
		storageItem:Sync({"L"; "E"; "EG"; "Tweak"});
        
		if handler.CharacterClass.ClassName ~= "NpcClass" then return end;

		local npcClass: NpcClass = handler.CharacterClass :: NpcClass;
		local wieldComp: WieldComp = handler.WieldComp;

		local itemId: string? = wieldComp.ItemId;
		if itemId == nil then return end;

		local equipmentClass: EquipmentClass = handler.EquipmentClass;
		local toolAnimator: ToolAnimator = handler.ToolAnimator;

		local toolPackage = handler.ToolPackage;

		local animations = toolPackage.Animations;
		local audio = toolPackage.Audio;

		local configurations = equipmentClass.Configurations;
		local properties = equipmentClass.Properties;

		local mainWeaponModel = handler.MainToolModel;

		local destroyed = false;
		local function weaponDestroyed()
			if destroyed then return end;
			destroyed = true;
		end

		for _, toolModel in pairs(handler.Prefabs) do
			local parentChangeSignal;
			parentChangeSignal = toolModel:GetPropertyChangedSignal("Parent"):Connect(function()
				if toolModel.Parent == game.Debris then
					destroyed = true;
				
					task.wait();
					toolModel:Destroy();
					handler:Destroy();

					npcClass.JointRotations.WaistRot:Remove("tool");
					npcClass.JointRotations.NeckRot:Remove("tool");
					
					
				elseif toolModel.Parent == nil or not toolModel:IsDescendantOf(npcClass.Character) then
					weaponDestroyed();
					
				end
				if destroyed then
					parentChangeSignal:Disconnect();
				end
			end);
		end

		local function playWeaponSound(id, looped)
			local soundOrigin = npcClass.RootPart;
			
			local sound = modAudio.Play(id, soundOrigin, looped);
			if sound then
				sound:SetAttribute("WeaponAudio", true);
			end
			
			return sound;
		end


		local cToolWaist = properties.WaistRotation or 0;
		
		npcClass.JointRotations.WaistRot:Set("tool", cToolWaist, 1);
		npcClass.JointRotations.NeckRot:Set("tool", cToolWaist, 1);

		toolAnimator:LoadAnimations(animations, toolPackage.DefaultAnimatorState, handler.Prefabs);
		toolAnimator:Play("Core");

		local cEquipTime = configurations.EquipLoadTime;
		local loadAnimKey = "Load";
		
		if animations["Load2"] and math.random(1, 10) <= 3 then
			loadAnimKey = "Load2";
		end

		if toolPackage.DoSpecialLoad then
			local animId = toolPackage.DoSpecialLoad(handler);
			if animId then
				loadAnimKey = animId;
			end
		end

		local function playLoad(loadAnimKey: string)
			loadAnimKey = loadAnimKey or "Load";
			if animations[loadAnimKey] then
				toolAnimator:Play(loadAnimKey, {
					PlayLength=math.clamp(cEquipTime, 0.5, 2);
				});
				
				if audio.Load then
					modAudio.Play(audio.Load.Id, mainWeaponModel.PrimaryPart);
				end
			end
		end
		playLoad(loadAnimKey);	

		-- MARK: BINDS
		handler.Binds["FuncPlayLoad"] = playLoad;
		handler.Binds["FuncWeaponDestroyed"] = weaponDestroyed;

		task.delay(cEquipTime, function()
			properties.CanPrimaryFire = true;
		end);
	end
	
	function toolHandler.ServerUnequip(handler: ToolHandlerInstance)
	end

	function toolHandler.ReloadRequest(handler: GunToolHandlerInstance)
		local properties = handler.EquipmentClass.Properties;
		local configurations = handler.EquipmentClass.Configurations;
		
		if properties.IsPrimaryFiring then return; end;
		if properties.Reloading then return end;
		if properties.Ammo == configurations.MagazineSize then return end;
		if properties.MaxAmmo <= 0 and properties.InfiniteAmmo ~= true then return end;
		if properties.Ammo ~= 0 and tick()-properties.ReloadCoolDown <= 12 then return end;
		properties.ReloadCoolDown = tick();

		local activeWeaponModel: Model = handler.Prefabs[properties.ToolModelIndex];
		if activeWeaponModel.PrimaryPart == nil then return end;

		if properties.InfiniteAmmo == nil and properties.MaxAmmo <= 0 then 
			handler:PlayAudio("Empty", activeWeaponModel.PrimaryPart);
			return
		end;

		properties.Reloading = true;

		handler.ToolAnimator:Stop("Inspect");
		handler.ToolAnimator:Stop("Empty");

		if configurations.ReloadMode == modWeaponAttributes.ReloadModes.Full
		or configurations.ReloadMode == modWeaponAttributes.ReloadModes.Single then
			
			local reloadTime = configurations.ReloadTime;

			handler.ToolAnimator:Play("Reload", {Length = reloadTime});
			handler:PlayAudio("Reload", activeWeaponModel.PrimaryPart, function(sound)
				sound.PlaybackSpeed = sound.TimeLength / reloadTime;
			end)

			toolHandler.ActionEvent(handler, {
				ActionIndex = 3;
				ReloadStart = true;
			});
			task.wait(reloadTime);
			toolHandler.ActionEvent(handler, {
				ActionIndex = 3;
				ReloadStart = false;
			});

			handler.ToolAnimator:Stop("Reload");
		end
		properties.CancelReload = false;
		properties.Reloading = false;
		
	end


	function toolHandler.FireWeapon(handler: GunToolHandlerInstance, direction: Vector3, enemyHumanoid: Humanoid?)
		local characterClass: CharacterClass = handler.CharacterClass;
		local equipmentClass: EquipmentClass = handler.EquipmentClass;
		local configurations = equipmentClass.Configurations;
		local properties = equipmentClass.Properties;

		if properties.Ammo <= 0 then
			characterClass.WieldComp.Controls.Mouse1Down = false;
			return 
		end


		handler:PullTrigger();


		local activeWeaponModel = handler.Prefabs[properties.ToolModelIndex];
		local bulletOriginAtt: Attachment = activeWeaponModel.PrimaryPart:WaitForChild("BulletOrigin") :: Attachment;
		local origin = bulletOriginAtt.WorldPosition;
		local maistPercent = 1;
		
		if enemyHumanoid and workspace:IsAncestorOf(enemyHumanoid.RootPart) then 
			local pick = math.random(1, 100);
			local targetPart = enemyHumanoid.RootPart;
			if pick <= 10 and enemyHumanoid.RootPart then
				targetPart = enemyHumanoid.RootPart;
			elseif pick <= 85 and enemyHumanoid.Parent then
				targetPart = enemyHumanoid.Parent:FindFirstChild("UpperTorso")
						or enemyHumanoid.Parent:FindFirstChild("Head")
						or enemyHumanoid.Parent:FindFirstChildWhichIsA("BasePart")
			end

			direction = (targetPart.Position-origin).Unit;
		end;

		local firePacket = {
			StorageItem = handler.StorageItem;
			ToolModel = activeWeaponModel;
			ToolHandler = handler;

			ShotBy = characterClass;

			ShotOrigin = origin;
			ShotDirection = direction;

			ReplicateToShotOwner = true;

			Targetable = handler.WieldComp.TargetableHumanoidType;

			IsPat = true;
			MaistPercent = maistPercent;
		};
		
		if configurations.FocusDuration and configurations.FocusDuration > 0 then
			task.wait(configurations.FocusDuration);
			firePacket.FocusCharge = 1;
		end
		
		modToolService.PrimaryFireWeapon(firePacket);
		properties.LastFired = tick();
	end

	local function primaryFire(handler: GunToolHandlerInstance, direction: Vector3, enemyHumanoid: Humanoid?)
		local toolAnimator: ToolAnimator = handler.ToolAnimator;
		local characterClass: CharacterClass = handler.CharacterClass;
		local wieldComp: WieldComp = handler.WieldComp;
		local equipmentClass: EquipmentClass = handler.EquipmentClass;
		local configurations: ConfigVariable = equipmentClass.Configurations;
		local properties = equipmentClass.Properties;
		local animations = handler.ToolPackage.Animations;

		wieldComp.Controls.Mouse1Down = true;

		if characterClass.HealthComp.IsDead then return end;
		if not properties.CanPrimaryFire then return end;
		properties.CanPrimaryFire = false;
		
		if properties.ToolModelIndex == nil or properties.ToolModelIndex >= #handler.Prefabs then
			properties.ToolModelIndex = 1;
		else
			properties.ToolModelIndex = properties.ToolModelIndex +1;
		end

		local activeWeaponModel = handler.Prefabs[properties.ToolModelIndex];

		toolAnimator:Stop("Inspect");

		if properties.Ammo <= 0 then
			toolAnimator:Play("Empty");
			handler:ToggleIdle(false);
			handler:PlayAudio("Empty", activeWeaponModel.PrimaryPart);

			wieldComp.Controls.Mouse1Down = false;
			properties.CanPrimaryFire = true;
			return;
		end

		toolAnimator:Stop("Empty");

		if configurations.TriggerMode == modWeaponAttributes.TriggerModes.Semi then
			handler:FireWeapon(direction, enemyHumanoid);

		elseif configurations.TriggerMode == modWeaponAttributes.TriggerModes.Automatic 
			or configurations.TriggerMode == modWeaponAttributes.TriggerModes.SpinUp then

			if configurations.TriggerMode == modWeaponAttributes.TriggerModes.SpinUp then
				if properties.SpinFloat == nil then
					properties.SpinFloat = 0;
					properties.SpinStartTick = tick();
				end;
				properties.IsSpinning = true;

				local function revFunc()
					while (properties.SpinFloat or 0) < 1 and wieldComp.Controls.Mouse1Down do
						local tickLapsed = tick()-properties.SpinStartTick;
						properties.SpinFloat = math.clamp(tickLapsed/configurations.SpinUpTime, 0, 1);
						task.wait();
					end
				end

				handler:PlayAudio("SpinUp", activeWeaponModel.PrimaryPart, function(sound)
					sound.Volume = 2;
				end)
				if animations["SpinUp"] then 
					toolAnimator:Play("SpinUp", {FadeTime=configurations.SpinUpTime;});
					handler:ToggleIdle(false);
				end;

				if configurations.SpinAndFire then
					task.spawn(revFunc);
				else
					revFunc();
				end
				-- if self.Npc.Movement then
				-- 	self.Npc.Movement:SetWalkSpeed("hmg", 6, 5);
				-- end
			end
			
			if configurations.TriggerMode == modWeaponAttributes.TriggerModes.Automatic 
			or properties.SpinFloat >= 1 or configurations.SpinAndFire then

				if configurations.RapidFire then
					properties.RapidFireStart = tick();
				end

				repeat
					if properties.IsPrimaryFiring == false then
						handler:FireWeapon(direction, enemyHumanoid);
					end
					if properties.Ammo <= 0 then
						wieldComp.Controls.Mouse1Down = false;
					end
					task.wait();
				until not wieldComp.Controls.Mouse1Down;
				
				if properties.LoopedPrimaryFireAudio then
					game.Debris:AddItem(properties.LoopedPrimaryFireAudio, 0);
					properties.LoopedPrimaryFireAudio = nil;
				end
			end

			properties.SpinFloat = nil;
			-- if self.Npc.Movement then
			-- 	self.Npc.Movement:SetWalkSpeed("hmg", nil);
			-- end

			if configurations.TriggerMode == modWeaponAttributes.TriggerModes.SpinUp then
				toolAnimator:Stop("SpinUp", {FadeTime=configurations.SpinDownTime;});
				handler:PlayAudio("SpinDown", activeWeaponModel.PrimaryPart, function(sound)
					sound.Volume = 2;
				end)
				properties.IsSpinning = nil;

			end

			if properties.LoopedPrimaryFireAudio ~= nil then
				local prevPrimaryFire = properties.LoopedPrimaryFireAudio;
				properties.LoopedPrimaryFireAudio = nil;
				
				prevPrimaryFire:Destroy();
			end

		end

		properties.CanPrimaryFire = true;
	end

	-- MARK: PrimaryFireRequest
	function toolHandler.PrimaryFireRequest(handler: GunToolHandlerInstance, direction: Vector3, enemyHumanoid: Humanoid?)
		local configurations = handler.EquipmentClass.Configurations;
		local properties = handler.EquipmentClass.Properties;
		if properties.CanPrimaryFire == false then return end;

		task.spawn(function()
			
			if properties.CanPrimaryFire then 
				primaryFire(handler, direction, enemyHumanoid);
			end;
		end);
	end

	-- MARK: ToggleIdle
	function toolHandler.ToggleIdle(handler: GunToolHandlerInstance, value: boolean)
		local characterClass = handler.CharacterClass;
		if characterClass == nil or characterClass.ClassName ~= "NpcClass" then return end

		local toolAnimator = handler.ToolAnimator;
		local animations = handler.ToolPackage.Animations;

		if animations["Idle"] then		
			if value ~= false then
				toolAnimator:Play("Idle");
				
				characterClass.JointRotations.WaistRot:Set("toolIdle", 0, 2);
				characterClass.JointRotations.NeckRot:Set("toolIdle", 0, 2);
	
			else
				toolAnimator:Stop("Idle");
				
				characterClass.JointRotations.WaistRot:Remove("toolIdle");
				characterClass.JointRotations.NeckRot:Remove("toolIdle");
				
			end
		end
	end
end

return toolHandler;