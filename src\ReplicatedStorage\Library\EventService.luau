local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");

local modBitFlags = shared.require(game.ReplicatedStorage.Library.BitFlags);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modSyncTime = shared.require(game.ReplicatedStorage.Library.SyncTime);

local EventHandlerPermissions = modBitFlags.new();
EventHandlerPermissions:AddFlag("CanInvoke", "Client can invoke");
EventHandlerPermissions:AddFlag("CanListen", "Client can listen to event")
EventHandlerPermissions:AddFlag("CanCancel", "Client can cancel event");

local EventSource = {
    Server = "S";
    Client = "C";
};

local localPlayer = game.Players.LocalPlayer;

local EventPacket = {};
local EventHandler = {};
local EventService = {};

local remoteEventService;

--== MARK: EventPacket;
function EventPacket:__index(k)
    if k == "Player" then
        return self.Players and self.Players[1];
    end
    local v = rawget(self, k);
    if v ~= nil then
        return v;
    end

    return EventPacket[k]; 
end;
EventPacket.__metatable = "The metatable is locked";
EventPacket.__newindex = function(t, k, v)
    if k == "Players" then
        rawset(t, k, v);

        return;
    end

    error(`Attempt modify locked values by setting k({k}) = v({v}).`);
end;

function EventPacket.new(key: GAME_EVENT_KEY<string>, eventSource: number)
    local self = {
        Key = key;
        Source = eventSource;

        Cancelled = false;
        Completed = false;

        Players = nil;

        Returns = {};
    };

    setmetatable(self, EventPacket);
    return self;
end

function EventPacket:SetCancelled(value: boolean)
    if self.Completed then
        Debugger:Warn(`Failed to set cancel on completed event ({self.Key}).`);
        return;
    end

    if RunService:IsClient() then
        if self.Source == EventSource.Server then
            Debugger:Warn(`Client has no authority to cancel event from received server ({self.Key}).`);

        else
            local eventHandler = EventService:GetOrNewHandler(self.Key);
        
            if eventHandler == nil or eventHandler:HasPermissions("CanCancel", localPlayer) == false then
                Debugger:Warn(`Player ({localPlayer}) does not have permission:CanCancel to listen to event ({self.Key}).`);
                return;
            end

        end
    end

    rawset(self, "Cancelled", value);
end


--== MARK: EventHandler;
EventHandler.__index = EventHandler;

function EventHandler.new(key: GAME_EVENT_KEY<string>)
    local self = {
        Key = key;
        Listeners = {};

        ReplicateToClients = true;
        RelayToServer = true;

        HandlerBitString = 0;
        ClientsBitString = {};
    };

    setmetatable(self, EventHandler);
    return self;
end

function EventHandler:AddListener(func, priority)
    priority = priority or 9;
    priority = priority or math.max(#self.Listeners+1, 1);

    local listener = {
        StackTrace = debug.traceback();
        Function = func;
        Priority = priority;
    };
    table.insert(self.Listeners, listener);
    
    table.sort(self.Listeners, function(a, b)
        return a.Priority < b.Priority;
    end);

    return function()
        for a=#self.Listeners, 1, -1 do
            if self.Listeners[a] ~= listener then continue end;
            self.Listeners[a] = nil;
        end
    end;
end

function EventHandler:SetPermissions(flagTag: string, value: boolean)
    self.HandlerBitString = EventHandlerPermissions:Set(self.HandlerBitString, flagTag, value);
end

function EventHandler:HasPermissions(flagTag: string, player: Player?): boolean
    local eventFlag = EventHandlerPermissions:Test(flagTag, self.HandlerBitString);

    local clientBitString = player and self.ClientsBitString[player] or nil;
    if clientBitString == nil then return eventFlag; end

    local clientFlag = EventHandlerPermissions:Test(flagTag, clientBitString);
    return clientFlag;
end

function EventHandler:SetPlayerPermissions(player: Player, flagTag: string, value: boolean)
    local clientBitString = self.ClientsBitString[player] or 0;
    clientBitString = EventHandlerPermissions:Set(clientBitString, flagTag, value);
    self.ClientsBitString[player] = clientBitString;
end


--== MARK: EventService
EventService.__index = EventService;
EventService.EventSource = EventSource;
EventService.Handlers = {};


function EventService:GetOrNewHandler(key: GAME_EVENT_KEY<string>, newIfNil: boolean?)
    if key == nil then
        Debugger:Warn(`Failed to get event handler. Key is nil.`, debug.traceback());
        return;
    end
    if self.Handlers[key] == nil and newIfNil == true then
        self.Handlers[key] = EventHandler.new(key);
    end
    return self.Handlers[key];
end

--MARK: ServerInvoke
function EventService:ServerInvoke(key: GAME_EVENT_KEY<string>, invokeParam : EventInvokeParam, ...): EventPacket
    assert(RunService:IsServer(), `Can not call ServerInvoke ({key}) from client.`);

    local eventSource = EventSource.Server;

    if typeof(invokeParam.SendBy) == "Instance" and invokeParam.SendBy:IsA("Player") then
        eventSource = EventSource.Client;
    end

    local eventHandler = EventService:GetOrNewHandler(key, true);
    local event = EventPacket.new(key, eventSource);

    if typeof(invokeParam.ReplicateTo) == "table" then 
        event.Players = invokeParam.ReplicateTo;
    elseif typeof(invokeParam.SendBy) == "Instance" and invokeParam.SendBy:IsA("Player") then
        event.Players = {invokeParam.SendBy};
    end

    for pos, listener in pairs(eventHandler.Listeners) do
        if listener == nil then continue end;
        listener.Function(event, ...);

        if event.Cancelled then
            break;
        end
    end
    rawset(event, "Completed", true);

    if not event.Cancelled then
        local packet = {
            [modRemotesManager.Ref("Key")] = key;
            [modRemotesManager.Ref("Arguments")] = {...};
        };

        if eventSource == EventSource.Server 
        and eventHandler.ReplicateToClients == true 
        and invokeParam.ReplicateTo then
            for _, player in pairs(invokeParam.ReplicateTo) do
                if not eventHandler:HasPermissions("CanListen", player) then continue end
                
                task.spawn(function()
                    local s, e = pcall(function()
                        remoteEventService:InvokeClient(player, packet)
                    end)
                    if not s then
                        Debugger:Warn(`InvokeClient error {player}: {packet}\n`, e);
                    end
                end)
            end
        end
    end

    return event;
end

--MARK: ClientInvoke
function EventService:ClientInvoke(key: GAME_EVENT_KEY<string>, invokeParam: EventInvokeParam, ...): EventPacket
    assert(RunService:IsClient(), `Can not call ClientInvoke ({key}) from server.`);

    local eventSource = EventSource.Client;
    if typeof(invokeParam.SendBy) == "nil" then
        eventSource = EventSource.Server;
    end

    local eventHandler = EventService:GetOrNewHandler(key, true);
    local event = EventPacket.new(key, eventSource);

    for pos, listener in pairs(eventHandler.Listeners) do
        if listener == nil then continue end;
        listener.Function(event, ...);

        if event.Cancelled then
            break;
        end
    end
    
    if not event.Cancelled and eventHandler.RelayToServer == true then
        local packet = {
            [modRemotesManager.Ref("Key")] = key;
            [modRemotesManager.Ref("Arguments")] = {...};
        };
        if eventSource == EventSource.Client then
            local e = remoteEventService:InvokeServer(packet);
            rawset(event, "Returns", e.Returns or {});
            rawset(event, "Cancelled", e.Cancelled);
        end
       
    end
    rawset(event, "Completed", true);

    return event;
end

--MARK: OnInvoked
function EventService:OnInvoked(
    key: GAME_EVENT_KEY<string>, 
    func: (event: EventPacket, ...any) -> nil, 
    position: number?
): () -> nil
    local eventHandler = EventService:GetOrNewHandler(key, true);
    return eventHandler:AddListener(func, position);
end

function EventService:GetHandlerKeys()
    local list = {};

    for key, _ in pairs(self.Handlers) do
        table.insert(list, key);
    end

    return list;
end

function EventService.onRequire()
    remoteEventService = modRemotesManager:Get("EventService") :: RemoteFunction;

    if RunService:IsServer() then
        game.Players.PlayerRemoving:Connect(function(player)
            for key, eventHandler in pairs(EventService.Handlers) do
                eventHandler.ClientsBitString[player] = nil;
            end
        end)

        function remoteEventService.OnServerInvoke(player, packet)
            local key = packet[modRemotesManager.Ref("Key")];
            local args = packet[modRemotesManager.Ref("Arguments")] or {};

            if key == nil or key ~= key then Debugger:Warn(`Invalid event key ({key}).`); return end;

            local eventHandler = EventService:GetOrNewHandler(key);
            if eventHandler == nil then Debugger:Warn(`Invalid event handler ({key}).`); return end;

            if eventHandler:HasPermissions("CanInvoke", player) == false then
                Debugger:Warn(`Player ({player}) does not have permission:CanInvoke to invoke event ({key})`);
                return;
            end

            local event = EventService:ServerInvoke(key, {SendBy=player}, unpack(args));
            return event;
        end
        

        local onClockTickEventHandler = EventService:GetOrNewHandler("Generic_BindClockTick", true);
        onClockTickEventHandler:SetPermissions("CanListen", true);

        modSyncTime.GetClock():GetPropertyChangedSignal("Value"):Connect(function()
            EventService:ServerInvoke("Generic_BindClockTick", {ReplicateTo=game.Players:GetPlayers()}, modSyncTime.GetTime());
        end)

    else
        function remoteEventService.OnClientInvoke(packet)
            local key = packet[modRemotesManager.Ref("Key")];
            local args = packet[modRemotesManager.Ref("Arguments")] or {};
            if key == nil or args == nil then return end;
            
            return EventService:ClientInvoke(key, {}, unpack(args));
        end
        
    end
end

shared.modEventService = EventService :: any;
return EventService;

