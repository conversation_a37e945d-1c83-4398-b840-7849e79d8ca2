local modTables = shared.require(game.ReplicatedStorage.Library.Util.Tables);

local PropertiesVariable = {};

local VALUES_KEY = "Values";
local ONCHANGED_KEY = "OnChanged";
local LOOP_KEY = "Loop";
local DESTROY_KEY = "Destroy";
local ONDESTROY_KEY = "OnDestroy";

function PropertiesVariable.new(v)
    v = modTables.DeepClone(v) or {};
    
    local self = {
        [VALUES_KEY] = v;
        [ONCHANGED_KEY] = shared.EventSignal.new("OnPropertyChanged");
        [ONDESTROY_KEY] = shared.EventSignal.new("OnDestroy");
    }
    
    setmetatable(self, PropertiesVariable);
    return self;
end

function PropertiesVariable:__index(k)
    if k == LOOP_KEY then
        return PropertiesVariable.Loop;
    elseif k == DESTROY_KEY then
        return PropertiesVariable.Destroy;
    elseif k == VALUES_KEY then 
        return rawget(self, VALUES_KEY);
    elseif k == ONDESTROY_KEY then
        return rawget(self, ONDESTROY_KEY);
    end;
    
    local t = rawget(self, VALUES_KEY);
    if t == nil then return end;
    return t[k];
end

function PropertiesVariable:__newindex(k, v)
    if k == VALUES_KEY then
        error(`Setting PropertiesVariable.Values is not allowed`);
    elseif k == ONCHANGED_KEY then
        error(`Setting PropertiesVariable.OnChanged is not allowed`);
    elseif k == LOOP_KEY then
        error(`Setting PropertiesVariable.Loop is not allowed`);
    elseif k == DESTROY_KEY then
        error(`Setting PropertiesVariable.Destroy is not allowed`);
    elseif k == ONDESTROY_KEY then
        error(`Setting PropertiesVariable.OnDestroy is not allowed`);
    end;

    local t = rawget(self, VALUES_KEY);
    if t == nil then return end;

    local ov = t[k];
    if ov == v then return end;
    t[k] = v;

    rawget(self, ONCHANGED_KEY):Fire(k, v, ov);

    return;
end

function PropertiesVariable:Loop(func)
    for k, v in pairs(rawget(self, VALUES_KEY)) do
        if func(k, v) == true then break end;
    end
end

function PropertiesVariable:Destroy()
    rawget(self, ONDESTROY_KEY):Fire();
    rawset(self, VALUES_KEY, nil);
    rawget(self, ONCHANGED_KEY):Destroy();
    rawget(self, ONDESTROY_KEY):Destroy();
end

return PropertiesVariable;