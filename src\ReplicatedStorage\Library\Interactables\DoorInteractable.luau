local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");

local localPlayer = game.Players.LocalPlayer;

local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modDoors = shared.require(game.ReplicatedStorage.Entity.Doors);

local interactablePackage = {};
--==
function interactablePackage.init(super) -- Server/Client

    local DoorInteractable = {
		Name = "Door";
        Type = "Door";

        InteractableRange = 14;
        Animation = "OpenDoor";

        EventServiceKey = "Interactables_BindDoorInteract";
    };
    
    function DoorInteractable.new(interactable: InteractableInstance, npcName: string)
        local interactPart = interactable.Part;
        local config = interactable.Config;
        local locked = config:GetAttribute("Locked") :: boolean;
        local lockedLabel = config:GetAttribute("LockedLabel") :: string;

        interactable.CanInteract = false;
        interactable.Label = `Door's locked`;

        interactable.Values.Locked = locked;
        interactable.Values.LockedLabel = lockedLabel;

        local doorConfig = interactPart.Parent:FindFirstChild("Door");
        if doorConfig and doorConfig:IsA("Configuration") then
            local door: DoorInstance = modDoors.getOrNew(doorConfig);
            
            local function checkBlockade()
                if door.Model:FindFirstChild("Blockade") then
                    config:SetAttribute("Barricaded", true);
                    interactable.Values.Barricaded = true;
                else
                    config:SetAttribute("Barricaded", false);
                    interactable.Values.Barricaded = false;
                end
            end
            checkBlockade()
            door.Model.ChildAdded:Connect(checkBlockade);
            door.Model.ChildRemoved:Connect(checkBlockade);
        end

        interactable:SetPermissions("CanInteract", true);
    end

    -- When interacting with interactable.
    function DoorInteractable.BindInteract(interactable: InteractableInstance, info: InteractInfo)
        local userName = nil;
        if info.Player then
            userName = info.Player.Name;
        elseif info.NpcClass then
            userName = info.NpcClass.Name;
        end
        if interactable:HasPermissions("CanInteract", userName) == false then
            return;
        end

        if info.Action == "Client" then 
            if info.Player == nil then return end;

            local modCharacter = info.CharacterVars;
            modCharacter.CharacterProperties.IsCrouching = false;
            modCharacter.StopSliding();

            local event: EventPacket = shared.modEventService:ClientInvoke(
                DoorInteractable.EventServiceKey, 
                {SendBy = localPlayer}, 
                interactable
            );
            if event.Cancelled then return end;

            interactable:InteractServer(info.Values);
            return;
        end

        if RunService:IsClient() then return end;
        --MARK: Server

        local event: EventPacket = shared.modEventService:ServerInvoke(
            DoorInteractable.EventServiceKey, 
            {ReplicateTo={info.Player}}, 
            interactable
        );
        if event.Cancelled then return end;

        local interactPart = interactable.Part;

        local doorConfig = interactPart.Parent:FindFirstChild("Door");
        if doorConfig and doorConfig:IsA("Configuration") then
            local door: DoorInstance = modDoors.getOrNew(doorConfig);
            door:Toggle();

		else
			local destination = interactPart and interactPart:FindFirstChild("Destination");
			if destination == nil then
				warn("Door missing destination.");
                return;
			end
			
            local tpCframe = CFrame.new(destination.WorldPosition) 
                            * CFrame.Angles(0, math.rad(destination.WorldOrientation.Y-90), 0);

            if info.Player then
                local player = info.Player;
                local playerClass: PlayerClass = shared.modPlayers.get(player);
                if playerClass == nil then return end;

                local distanceFromDoor = player:DistanceFromCharacter(interactPart.Position);
                if distanceFromDoor > (interactable.Values.MaxEnterDistance or 20) then 
                    Debugger:Warn(player.Name,"Too far from door. ("..distanceFromDoor..")");
                    return;
                end;
                    
                playerClass:SetCFrame(tpCframe * CFrame.new(0, playerClass.Humanoid.HipHeight, 0));

            elseif info.NpcClass then
                local npcClass: NpcClass = info.NpcClass;
                
                local seatWeld = npcClass.Humanoid.SeatPart and npcClass.Humanoid.SeatPart:FindFirstChild("SeatWeld");
                if seatWeld then 
                    seatWeld:Destroy();
                end
		
                npcClass:SetCFrame(tpCframe * CFrame.new(0, npcClass.Humanoid.HipHeight, 0));
                npcClass.PlayAnimation("OpenDoor");
            end

            local soundId = interactable.Values.EnterSound or interactable.Package.EnterSound;
            if soundId and #soundId > 0 then
                local sound = modAudio.Play(soundId, destination); 
                if sound then sound.PlaybackSpeed = math.random(7, 12)/10; end;
            end
		end
    end
    
    -- When interactable pops up on screen.
    function DoorInteractable.BindPrompt(interactable: InteractableInstance, info: InteractInfo)
        local interactPart = interactable.Part;

        if interactable.Values.Barricaded then
            interactable.CanInteract = false;
            interactable.Label = "Door's barricaded";
            return;
        end

        if interactable.Values.Locked then
            interactable.CanInteract = false;
            interactable.Label = interactable.Values.LockedLabel or `Door's locked`;
            return;
        end

        if info.Player then
            if interactable:HasPermissions("CanInteract", info.Player.Name) == false then
                interactable.CanInteract = false;
                interactable.Label = interactable.Values.LockedLabel or `Door's locked`;
                return;
            end
        end

		local door: DoorInstance = modDoors.getOrNew(interactPart.Parent:FindFirstChild("Door"));
        if door == nil then
            interactable.CanInteract = true;
            interactable.Label = `Enter Door`;
            return;
        end

        local isOpen = door.Config:GetAttribute("DoorOpen");
        interactable.CanInteract = true;
        interactable.Label = `{isOpen and "Close" or "Open"} Door`;
        
        if interactable.Label:match("Door") then
            if door.DoorType == "Sliding" then
                interactable.Animation = "Press";
                return;
            end
            
            if isOpen then
                interactable.Animation = "CloseDoor";
            else
                interactable.Animation = "OpenDoor";
            end
        end
    end

    super.registerPackage(DoorInteractable);

end

return interactablePackage;

