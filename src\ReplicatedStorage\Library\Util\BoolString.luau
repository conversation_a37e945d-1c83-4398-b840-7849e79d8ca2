local BoolString = {};
BoolString.__index = BoolString;

local function extractGroups(boolString)
    local groups = {};
    
    local toDoGroups = {
        boolString;
    };

    repeat
        local subject = table.remove(toDoGroups, 1);
        local c = 0;
        for match in subject:gmatch("%b()") do
            table.insert(toDoGroups, match:sub(2, #match-1));
            c = c+1;
        end
        table.insert(groups, subject);
    until #toDoGroups <= 0;

    return groups;
end

local function newTestFunction(boolString: string)
    if boolString == nil then
        error(`Missing boolString.`);
    end

    boolString = boolString:gsub(" ", ""); -- removal all spaces;
    local groups = extractGroups(boolString);

    return function(testFlags: {string}, printDebug: boolean)
        if printDebug then
            print(`Testing`, testFlags);
        end

        local flags = setmetatable({
            [""] = true;
            ["1"] = true;
            ["0"] = false;
        }, {
            __index = function()
                return false;
            end;
        });

        for a=1, #testFlags do
            flags[testFlags[a]] = true;
        end
    
        local groupResult = false;
        for a=#groups, 1, -1 do
            local group = groups[a];
            local test = group;
            groupResult = false;
            
            local orList = string.split(test, "|");
            for a=1, #orList do
                local andList = string.split(orList[a], "&");
                local andResult = true;
                for b=1, #andList do
                    local item = andList[b];
                    local key = item;
                    local bool = true;
                    
                    if item:sub(1,1) == "!" then
                        key = item:sub(2);
                        bool = false;
                    end
                    
                    local value = flags[key] == bool;
                    if value == false then
                        andResult = false;
                        break;
                    end
                end
                
                if andResult then
                    groupResult = true;
                    break;
                end
            end

            for z=#groups, 1, -1 do
                groups[z] = groups[z]:gsub(`%({group}%)`, groupResult and "1" or "0");
            end
            if printDebug then
                print(`Testing`, a, group, groupResult);
            end
        end

        return groupResult;
    end
end

--print(newTestFunction("A&B&((C&!D|E)&!F&(G|H))")({"A", "G", "C"}));

BoolString.newTestFunction = newTestFunction;
BoolString.extractGroups = extractGroups;

return BoolString;