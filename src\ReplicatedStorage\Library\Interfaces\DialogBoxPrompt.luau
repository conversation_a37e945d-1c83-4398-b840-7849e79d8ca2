local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");
local TweenService = game:GetService("TweenService");

local modComponents = shared.require(game.ReplicatedStorage.Library.UI.Components);

local interfacePackage = {
    Type = "Player";
};
--==


function interfacePackage.newInstance(interface: InterfaceInstance)
    local frame = script:WaitForChild("DialogBox"):Clone();
    frame.Parent = interface.ScreenGui;

	local templatePromptButton = frame:WaitForChild("Button") :: TextButton; 
	local promptDialogWindow = frame:WaitForChild("Window") :: TextButton;
	local promptDialogFrame = promptDialogWindow:WaitForChild("Frame") :: Frame;

    local window: InterfaceWindow = interface:NewWindow("DialogBoxPrompt", frame);
    window.IgnoreHideAll = true;
    window.UseTween = false;

    local binds = window.Binds;
    binds.StatusLabel = promptDialogFrame.statusLabel;

	frame.MouseButton1Click:Connect(function()
        window:Close();
    end);

    local promptDialogActive = false;
    window.OnToggle:Connect(function(visible, ...)
        if visible then
            window:Update(...);

            promptDialogWindow.Position = UDim2.new(0.5, 0, -1.5, 0);
            TweenService:Create(frame, TweenInfo.new(0.3), {
                BackgroundTransparency = 0.3;
            }):Play();
            TweenService:Create(promptDialogWindow, TweenInfo.new(0.5), {
                Position = UDim2.new(0.5, 0, 0.5, 0);
            }):Play();
            task.delay(0.3, function() 
                promptDialogActive = true;
            end)
            frame.Visible = true;

        else
            if not promptDialogActive then 
                frame.Visible = false;
                return 
            end;
            promptDialogActive = false;

            TweenService:Create(frame, TweenInfo.new(0.3), {
                BackgroundTransparency = 1;
            }):Play();
            TweenService:Create(promptDialogWindow, TweenInfo.new(0.5), {
                Position = UDim2.new(0.5, 0, -1.5, 0);
            }):Play();
            task.delay(0.3, function()
                frame.Visible = false;
            end)
        end
    end)

    window.OnUpdate:Connect(function(params)
        local titleLabel = promptDialogFrame.titleLabel;
        local descLabel = promptDialogFrame.descLabel;
        local iconLabel = promptDialogWindow.iconLabel;

        local buttonsFrame = promptDialogFrame.Buttons;
        local statusLabel = promptDialogFrame.statusLabel;
        
        titleLabel.Text = params.Title or "Are you sure?";
        descLabel.Text = params.Desc or "?"
        iconLabel.Image = params.Icon or "";
        iconLabel.Visible = iconLabel.Image ~= "";

        buttonsFrame.Visible = true;
        statusLabel.Visible = false;

        for _, obj in pairs(buttonsFrame:GetChildren()) do
            if obj:IsA("GuiObject") then
                obj:Destroy();
            end
        end

        local optionButtons = params.Buttons or {};

        for a=1, #optionButtons do
            local optionButtonInfo = optionButtons[a];

            if optionButtonInfo.Check and (not optionButtonInfo.Check()) then
                continue;
            end
            
            local newButton = templatePromptButton:Clone();
            newButton.Text = optionButtonInfo.Text or "Button"..a;

            local onClick1 = optionButtonInfo.OnPrimaryClick;
            local onClick2 = optionButtonInfo.OnSecondaryClick;

            if optionButtonInfo.Style then
                if optionButtonInfo.Style == "Confirm" then
                    newButton.BackgroundColor3 = Color3.fromRGB(54, 107, 51);
                elseif optionButtonInfo.Style == "Cancel" then
                    newButton.BackgroundColor3 = Color3.fromRGB(102, 38, 38);
                    if optionButtonInfo.Text == nil then
                        newButton.Text = "Cancel";
                    end
                elseif optionButtonInfo.Style == "Perks" then
                    newButton.BackgroundColor3 = Color3.fromRGB(53, 66, 100);
                    newButton.UIPadding.PaddingRight = UDim.new(0, 20);
                    local newLabel = modComponents.TemplatePerksImageLabel:Clone();
                    newLabel.ZIndex = 7;
                    newLabel.Size = UDim2.new(1, -5, 1, -5);
                    newLabel.Parent = newButton;

                elseif optionButtonInfo.Style == "Gold" then
                    newButton.BackgroundColor3 = Color3.fromRGB(120, 103, 53);
                    newButton.UIPadding.PaddingRight = UDim.new(0, 20);
                    local newLabel = modComponents.TemplateGoldImageLabel:Clone();
                    newLabel.ZIndex = 7;
                    newLabel.Size = UDim2.new(1, -5, 1, -5);
                    newLabel.Parent = newButton;

                end
            end

            if optionButtonInfo.Color then
                newButton.BackgroundColor3 = optionButtonInfo.Color;
            end
            if optionButtonInfo.TextStrokeTransparency then
                newButton.TextStrokeTransparency = optionButtonInfo.TextStrokeTransparency;
            end

            local debouceTick = tick();
            newButton.MouseButton1Click:Connect(function()
                if not promptDialogActive then return end;

                if tick()-debouceTick <= 0.2 then return end;
                debouceTick = tick();

                interface:PlayButtonClick();
                
                buttonsFrame.Visible = false;
                statusLabel.Visible = true;

                local skipClose = false;
                if onClick1 then
                    skipClose = onClick1(window, newButton);
                end

                if optionButtonInfo.HideButtonsOnClick then
                    return;
                elseif optionButtonInfo.SkipClosePrompt or skipClose == true then 
                    buttonsFrame.Visible = true;
                    statusLabel.Visible = false;
                    return;
                end;

                window:Close();
            end)

            local function onSecondaryClick()
                if not promptDialogActive then return end;
                
                if tick()-debouceTick <= 0.2 then return end;
                debouceTick = tick();

                interface:PlayButtonClick();

                buttonsFrame.Visible = false;
                statusLabel.Visible = true;
                
                local skipClose = false;
                if onClick2 then
                    skipClose = onClick2(window, newButton);
                end

                if optionButtonInfo.SkipClosePrompt or skipClose == true then 
                    buttonsFrame.Visible = true;
                    statusLabel.Visible = false;
                    return;
                end;
                
                window:Close();
            end
            newButton.MouseButton2Click:Connect(onSecondaryClick);
            newButton.TouchLongPress:Connect(onSecondaryClick);

            newButton.Visible = true;
            newButton.Parent = buttonsFrame;
        end
    end)
end

function interfacePackage.onRequire()
    if not RunService:IsStudio() then return end;

    shared.modCommandsLibrary.bind({
        ["testdialogbox"]={
            Permission = shared.modCommandsLibrary.PermissionLevel.DevBranch;
            Description = [[Test dialog box
            /testdialogbox test1
            ]];

            RequiredArgs = 0;
            UsageInfo = "/testdialogbox test1";
            Function = function() end;
            ClientFunction = function(player, args)
                local modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);
                
                local testNum = args[1] or "test1";

                if testNum == "test1" then
                    
                    modClientGuis.promptDialogBox({
						Title=`Unlock Event Pass for 1'234?`;
						Desc=`Are you sure you want to unlock Event Pass: No More BattlePass for 1'234?`;
						Icon="";
						Buttons={
							{
								Text="Purchase";
								Style="Confirm";
								OnPrimaryClick=function(promptDialogFrame, textButton)
                                    Debugger:Warn(`Purchase`);
								end;
							};
							{
								Text="Cancel";
								Style="Cancel";
							};
						}
					});
                end

                return;
            end;
        }
    });
end

return interfacePackage;

