local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");

local modConfigurations = shared.require(game.ReplicatedStorage.Library.Configurations);
local modGarbageHandler = shared.require(game.ReplicatedStorage.Library.GarbageHandler);
local modEventSignal = shared.require(game.ReplicatedStorage.Library.EventSignal);
local modLayeredVariable = shared.require(game.ReplicatedStorage.Library.LayeredVariable);
local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modInfoBubbles = shared.require(game.ReplicatedStorage.Library.InfoBubbles);
local modNpcClass = shared.require(game.ReplicatedStorage.Entity.NpcClass);
		
local modHealthComponent = shared.require(game.ReplicatedStorage.Components.HealthComponent);
local modStatusComponent = shared.require(game.ReplicatedStorage.Components.StatusComponent);
local modWieldComponent = shared.require(game.ReplicatedStorage.Components.WieldComponent);

local modAnalytics = shared.require(game.ServerScriptService.ServerLibrary.GameAnalytics);
local modParticleSprinkler = shared.require(game.ReplicatedStorage.Particles.ParticleSprinkler);
local modOnGameEvents = shared.require(game.ServerScriptService.ServerLibrary.OnGameEvents);

local DamageData = shared.require(game.ReplicatedStorage.Data.DamageData);

local npcHidden = game.ServerStorage.Prefabs:WaitForChild("Objects"):WaitForChild("NpcHidden"); -- for :TeleportHide();
npcHidden.Parent = workspace;

local remotes = game.ReplicatedStorage.Remotes;
local bindOnTalkedTo = remotes.Dialogue.OnTalkedTo;

local SafeKeys = {
	IsDestroyed = true;
	Id = true;
	IsDead = true;
	Name = true;
};
--==

local OldNpcClass = {};
OldNpcClass.__index = OldNpcClass;

-- properties;
OldNpcClass.ClassName = "NpcClass";
OldNpcClass.BindOnTalkedTo = bindOnTalkedTo;

-- methods;
OldNpcClass.AddComponent = modNpcClass.AddComponent;
OldNpcClass.LoadClientScript = modNpcClass.LoadClientScript;
OldNpcClass.IsRagdolling = modNpcClass.IsRagdolling;
OldNpcClass.RefreshRagdollJoints = modNpcClass.RefreshRagdollJoints;

function OldNpcClass:SetClientScript(localScript)
	self.ClientScript = localScript;
end

OldNpcClass.Kill = modNpcClass.Kill;
-- function NpcClass:Kill()
-- 	local rootPart: BasePart = self.RootPart;
-- 	local prefab: Model = self.Prefab;
-- 	local humanoid: Humanoid = self.Humanoid;

-- 	humanoid.PlatformStand = true;
-- 	humanoid.EvaluateStateMachine = false;
-- 	humanoid.HealthDisplayDistance = 0;
-- 	humanoid:SetAttribute("IsDead", true);
-- 	self.IsDead = true;

-- 	if humanoid.SeatPart then
-- 		for _, weld in pairs(humanoid.SeatPart:GetChildren()) do
-- 			if weld:IsA("Weld") and weld.Part1 == rootPart then
-- 				game.Debris:AddItem(weld, 0);
-- 			end
-- 		end
-- 	end

-- 	if self.Animator then
-- 		for _, track: AnimationTrack in pairs(self.Animator:GetPlayingAnimationTracks()) do
-- 			track:Stop();
-- 		end
-- 	end

-- 	if self.BehaviorTree then
-- 		self.BehaviorTree.Disabled = true;
-- 		self.BehaviorTree:StopTree();
-- 	end
-- 	if self.InitializeThread and coroutine.status(self.InitializeThread) == "suspended" then
-- 		task.spawn(function()
-- 			local killS, killE = coroutine.resume(self.InitializeThread);
-- 			if not killS then
-- 				modAnalytics:ReportError("Kill Npc", self.Name..": "..(killE or "No error"), "warning");
-- 			end
-- 		end)
-- 	end

-- 	self:SetNetworkOwner(nil, true);

-- 	task.spawn(function()
-- 		if rootPart then
-- 			rootPart:RemoveTag("Enemies");
-- 			for _, tag in pairs(rootPart:GetTags()) do
-- 				rootPart:RemoveTag(tag);
-- 			end
-- 			local physicSleepDisabler = Instance.new("BodyForce");
-- 			physicSleepDisabler.Parent = rootPart;
-- 		end

-- 		if prefab then
-- 			for _, tag in pairs(prefab:GetTags()) do
-- 				prefab:RemoveTag(tag);
-- 			end
-- 			prefab:SetAttribute("DeadbodyTick", tick());
-- 			prefab:AddTag("Deadbody");
-- 			task.delay(0.5, function()
-- 				if not workspace.Entity:IsAncestorOf(prefab) then return end;
-- 				prefab.Parent = workspace.Entities;
-- 			end)
-- 			task.delay(5, function()
-- 				rootPart.Anchored = true;
-- 				for _, obj in pairs(prefab:GetChildren()) do
-- 					if not obj:IsA("BasePart") or obj.AssemblyRootPart == nil then continue end;
-- 					obj.AssemblyRootPart.Anchored = true;
-- 					obj.CanCollide = false;
-- 				end
-- 			end)
-- 		end
-- 		self:RefreshRagdollJoints();

-- 		if self.Wield then
-- 			for _, obj in pairs(self.Wield.Instances) do
-- 				if obj:IsA("Model") then
-- 					if obj.PrimaryPart then
-- 						local handle: BasePart = obj.PrimaryPart;
-- 						handle.Massless = false;
-- 						handle.CustomPhysicalProperties = PhysicalProperties.new(4, 0.5, 1, 0.3, 1);
-- 					end
-- 					for _, weaponPart in pairs(obj:GetChildren()) do
-- 						if weaponPart:IsA("BasePart") then
-- 							weaponPart.CanCollide = true;
-- 						end
-- 					end
-- 					if game:IsAncestorOf(obj) then
-- 						obj.Parent = workspace.Debris;
-- 					end
-- 					game.Debris:AddItem(obj, 10);

-- 				elseif obj:IsA("Motor6D") then
-- 					obj:Destroy();

-- 				end
-- 			end
-- 		end
-- 	end)

-- end

function OldNpcClass:BreakJoint(motor: Motor6D)
	if motor == nil then return end;

	local part0 :BasePart, part1 :BasePart = motor.Part0 :: BasePart, motor.Part1 :: BasePart;
	motor:Destroy();

	local ragdollJoint = motor.Parent and motor.Parent:FindFirstAncestorWhichIsA("BallSocketConstraint");
	if ragdollJoint then
		ragdollJoint:Destroy();
	end

	if self.JointsDestroyed == nil then
		self.JointsDestroyed = {};
	end;
	self.JointsDestroyed[motor.Name] = true;

	local assemblyRoots = {};
	if part0 then
		table.insert(assemblyRoots, part0.AssemblyRootPart);
	end
	if part1 then
		table.insert(assemblyRoots, part1.AssemblyRootPart);
	end

	for a=1, #assemblyRoots do
		if assemblyRoots[a] == self.RootPart then continue end;

		local assemblyRoot = assemblyRoots[a];
		local connParts = assemblyRoot:GetConnectedParts(true);

		for b=1, #connParts do
			if self.Prefab:IsAncestorOf(connParts[b]) then continue end;
			table.remove(connParts, b);
		end

		task.spawn(function()
			table.insert(connParts, assemblyRoot);

			for _, bodyPart in pairs(connParts) do
				bodyPart.CanCollide = true;

				if bodyPart:FindFirstChild("WeakpointTarget") then
					bodyPart.WeakpointTarget:Destroy();
				end
			end

			local physicSleepDisabler = Instance.new("BodyForce");
			physicSleepDisabler.Parent = assemblyRoot;

			task.wait(5);

			if workspace:IsAncestorOf(assemblyRoot) then
				for _, bodyPart :BasePart in pairs(connParts) do
					bodyPart.Anchored = true;
					bodyPart.CanCollide = false;
					bodyPart.CanQuery = false;
					bodyPart:SetAttribute("IgnoreWeaponRay", true);
				end
			end
		end)

		break;
	end

	return assemblyRoots;
end

function OldNpcClass:Destroy()
	if self.IsDestroyed then return end;
	self.IsDestroyed = true;

	task.delay(10, function()
		for key, _ in pairs(self) do
			if SafeKeys[key] == nil then
				if typeof(self[key]) == "table" then
					if self[key].Destroy then
						self[key]:Destroy();
					end
				end

				self[key] = nil;
			end
		end
	end)

	self:Kill();

	if self.Garbage then
		local garbage = self.Garbage;
		self.Garbage = nil;
		garbage:Destruct();
	end
	if self.Logic then
		self.Logic:ClearAll();
		for k,_ in pairs(self.Logic) do
			self.Logic.Actions[k] = nil;
		end
	end

	self.Think:Destroy();
	self.Status = nil;
	setmetatable(self, {__mode="kv"; __index=getmetatable(self)});

end

function OldNpcClass:AddDialogueInteractable()
	if self.Prefab:FindFirstChild("Interactable") == nil then
		local new = script.Interactable:Clone();
		new.Parent = self.Prefab;
	end
end

OldNpcClass.DistanceFrom = modNpcClass.DistanceFrom;
OldNpcClass.ToggleInteractable = modNpcClass.ToggleInteractable;
OldNpcClass.SetNetworkOwner = modNpcClass.SetNetworkOwner;
OldNpcClass.IsInVision = modNpcClass.IsInVision;
OldNpcClass.SendActorMessage = modNpcClass.SendActorMessage;
OldNpcClass.ListComponents = modNpcClass.ListComponents;

function OldNpcClass:DamageTarget(model, damage, character, dmgData: DamageData, dmgCate, dmgAfterDeath)
	local canDmgTarget = self.Humanoid and self.Humanoid.Health > 0 and (self.IsDead ~= true);
	if dmgAfterDeath then
		canDmgTarget = true;
	end

	if not canDmgTarget then return end;
	local player = game.Players:GetPlayerFromCharacter(character);

	if self.NetworkOwners then
		if player then
			if table.find(self.NetworkOwners, player) == nil then
				return;
			end
		end
	end

	local visibleParts = {};
	for _, obj in pairs(model:GetChildren()) do
		if obj:IsA("BasePart") and obj.Transparency <= 0.1 then
			table.insert(visibleParts, obj);
		end
	end

	local health: HealthComp? = modHealthComponent.getByModel(model);
	if health then
		dmgData = dmgData or DamageData.new();

		dmgData.DamageBy = self;
		dmgData.Damage = damage;
		dmgData.DamageCate = dmgCate;

		dmgData.TargetModel = model;
		dmgData.TargetPart = (#visibleParts > 0 and visibleParts[math.random(1, #visibleParts)] or nil);

		health:TakeDamage(dmgData);
	end
end

function OldNpcClass:Teleport(cframe: CFrame, cfAngle: CFrame)
	if self.Humanoid and self.Humanoid.SeatPart and self.Humanoid.SeatPart:FindFirstChild("SeatWeld") then
		self.Humanoid.SeatPart.SeatWeld:Destroy();
	end
	if cframe then
		local cfAng = cframe.Rotation;
		if cfAng == CFrame.Angles(0, 0, 0) then
			cfAng = self.RootPart.CFrame.Rotation;
		end
		if cfAngle then
			cfAng = cfAngle;
		end
		self.RootPart.CFrame = CFrame.new(cframe.Position) * cfAng;

	else
		if self.Player and self.Player.Character and self.Player.Character.PrimaryPart and self.Player.Character.PrimaryPart:IsDescendantOf(workspace) then
			self.RootPart.CFrame = self.Player.Character.PrimaryPart.CFrame;
		end
	end
end

function OldNpcClass:TeleportHide()
	Debugger:StudioWarn("TeleportHide> "..self.Name, debug.traceback());
	if self.Humanoid and self.Humanoid.SeatPart and self.Humanoid.SeatPart:FindFirstChild("SeatWeld") then
		self.Humanoid.SeatPart.SeatWeld:Destroy();
	end
	if self.RootPart and self.RootPart:IsDescendantOf(workspace) then
		self.RootPart.CFrame = CFrame.new(10000, 270, 10000);
	end
	if self.Follow then
		self.Follow();
	end
end

function OldNpcClass:Attach(att: Attachment)
	local rootRigAtt = self.RootPart.RootRigAttachment;

	if att == nil then
		for _, obj in pairs(self.RootPart:GetChildren()) do
			if obj.Name == "RigAttach" then
				obj:Destroy();
			end
		end
		self.Humanoid:SetAttribute("RigAttached", nil);

	else
		local alignPosition = self.RootPart:FindFirstChild("RigAttach");
		if alignPosition == nil then
			alignPosition = Instance.new("AlignPosition");
			alignPosition.Name = "RigAttach";
			alignPosition.RigidityEnabled = true;
			alignPosition.Parent = self.RootPart;
			alignPosition.Attachment0 = rootRigAtt;

		end

		alignPosition.Attachment1 = att;
		self.Humanoid:SetAttribute("RigAttached", true);
	end
end

function OldNpcClass.GetHealthComp(self: NpcClass, bodyPart: BasePart): HealthComp
	if bodyPart and self.CustomHealthbar then
		local healthComp: HealthComp = self.CustomHealthbar:GetFromPart(bodyPart); --MARK: TODO;
		if healthComp then
			return healthComp;
		end
	end

	return self.HealthComp;
end

function OldNpcClass:GetHealth(valueType: string, bodyPart: BasePart)
	if bodyPart and self.CustomHealthbar then
		local healthObj = self.CustomHealthbar:GetFromPart(bodyPart);
		if healthObj then
			if valueType == "MaxHealth" then
				return healthObj.MaxHealth;
			end
			return healthObj.Health;
		end
	end

	if valueType == "MaxHealth" then
		return self.Humanoid.MaxHealth;
	end
	return self.Humanoid.Health
end

function OldNpcClass:GetComponent()
end

function OldNpcClass:Died(func)
	if self.Humanoid == nil then return end;
	return self.Humanoid:GetAttributeChangedSignal("IsDead"):Connect(function()
		if self.Humanoid:GetAttribute("IsDead") ~= true then return end;
		func();
	end)
end

local ShirtParts = {
	"UpperTorso"; "LowerTorso";
	"LeftUpperArm"; "RightUpperArm";
	"LeftLowerArm"; "RightLowerArm";
	"LeftHand"; "RightHand";
};
local PantsParts = {
	"LeftUpperLeg"; "RightUpperLeg";
	"LeftLowerLeg"; "RightLowerLeg";
	"LeftFoot"; "RightFoot";
};
function OldNpcClass:UpdateClothing()
	local oldShirt = self.Prefab:FindFirstChildWhichIsA("Shirt");
	local oldPants = self.Prefab:FindFirstChildWhichIsA("Pants");


	if self.PresetSkinColor then
		self.Head.Color = self.PresetSkinColor;
	end

	for a=1, #ShirtParts do
		local part = self.Prefab:FindFirstChild(ShirtParts[a]);

		if self.PresetSkinColor then
			part.Color = self.PresetSkinColor;
		end

		if self.PresetShirt == nil then continue end;
		if oldShirt then
			oldShirt.ShirtTemplate = self.PresetShirt.Id;
		else
			part.TextureID = self.PresetShirt.Id;
		end
	end
	for a=1, #PantsParts do
		local part = self.Prefab:FindFirstChild(PantsParts[a]);
		if self.PresetSkinColor then
			part.Color = self.PresetSkinColor;
		end

		if self.PresetPants == nil then continue end;
		if oldPants then
			oldPants.PantsTemplate = self.PresetPants.Id;
		else
			part.TextureID = self.PresetPants.Id;
		end
	end

	if self.PresetSkinColor then
		if self.Prefab:FindFirstChild("RightPoint") then
			self.Prefab.RightPoint.Color = self.PresetSkinColor;
		end
		if self.Prefab:FindFirstChild("RightMiddle") then
			self.Prefab.RightMiddle.Color = self.PresetSkinColor;
		end
		if self.Prefab:FindFirstChild("RightPinky") then
			self.Prefab.RightPinky.Color = self.PresetSkinColor;
		end
		if self.Prefab:FindFirstChild("LeftPoint") then
			self.Prefab.LeftPoint.Color = self.PresetSkinColor;
		end
		if self.Prefab:FindFirstChild("LeftMiddle") then
			self.Prefab.LeftMiddle.Color = self.PresetSkinColor;
		end
		if self.Prefab:FindFirstChild("LeftPinky") then
			self.Prefab.LeftPinky.Color = self.PresetSkinColor;
		end
	end
end

function OldNpcClass:GetAttackers()
	local players = {};
	if self.Weapons then
		for name, _ in pairs(self.Weapons) do
			table.insert(players, game.Players:FindFirstChild(name));
		end;
	end

	if self.NetworkOwners then
		for a=1, #self.NetworkOwners do
			local found = table.find(players, self.NetworkOwners[a]);
			if found == nil then
				table.insert(players, self.NetworkOwners[a]);
			end
		end
	end

	if self.GetAttackers then
		self.GetAttackers(players);
	end

	return players;
end

OldNpcClass.GetImmunity = modNpcClass.GetImmunity;
--==

return function(self)
	self.Character = self.Prefab;
	self.Name = self.Character.Name;
	self.Head = self.Character:FindFirstChild("Head") :: BasePart;
	self.Humanoid = self.Character:FindFirstChildWhichIsA("Humanoid") :: Humanoid;
	self.RootPart = self.Humanoid.RootPart :: BasePart;
	self.HumanoidType = self.Humanoid.Name;
	if RunService:IsStudio() then
		Debugger:Warn(`{self.Name} is using deprecated NpcClass.`);
	end

	self.Actor = self.Character:GetActor();
	self.OnThink = shared.EventSignal.new(self.Name.."Think");
	self.Think = self.OnThink;
	
	self.NpcPackage = {};
	self.NpcComponentsList = {};

	local actorEvent: BindableEvent = Instance.new("BindableEvent");
	actorEvent.Name = "ActorEvent";
	actorEvent.Parent = self.Character;
	self.ActorEvent = actorEvent;

	local isHuman = self.Humanoid.Name == "Human"

	self.PathAgent = {
		AgentRadius=1;
		AgentHeight=6;

		AgentCanClimb=true;

		WaypointSpacing = math.huge;--isHuman and 4 or 10;

		Costs={
			Climb = 4;
			Water = 10;
			Avoid = 10;
			Barricade = 100;
			DefinePath = isHuman and 0.25 or 0.5;
			Walkway = isHuman and 0.5 or 0.75;
			Slowpath = isHuman and 2 or nil;
			DoorPortal = isHuman and 1 or nil;
		};
	};

	self.Garbage = modGarbageHandler.new();

	if self.Properties == nil then
		self.Properties = {};
	end

	local healthComp: HealthComp;
	local statusComp: StatusComp;
	--MARK: init HealthComp
	healthComp = modHealthComponent.new(self :: ComponentOwner);
	modNpcClass.initHealthComp(self, healthComp);
	self.HealthComp = healthComp;
	--

	--MARK: init StatusComp
	statusComp = modStatusComponent.new(self :: ComponentOwner);
	statusComp.OnProcess:Connect(function()
		if self.HealthComp.IsDead then return end;
		self.Think:Fire();
	end)

	self.StatusComp = statusComp;
	
	--MARK: init WieldComp
	local wieldComp: WieldComp = modWieldComponent.new(self :: ComponentOwner);
	self.WieldComp = wieldComp;
	--

	function self:ApplyImpulseForce(direction, force, impactPosition)
		local humanoid = self.Humanoid;
		local rootPart: BasePart = self.RootPart;
		if rootPart == nil then return end;
		
		local knockbackResistant = self.KnockbackResistant or 0;
		knockbackResistant = 1-math.clamp(knockbackResistant, 0, 1);

		local damageForce = direction * (force or knockbackResistant);
		rootPart:ApplyImpulseAtPosition(damageForce * humanoid.RootPart.AssemblyMass, impactPosition or rootPart.Position);
	end
	--

	self.LastDamageTaken = tick();

	self.JointRotations = {
		WaistRot = modLayeredVariable.new(0);
		NeckRot = modLayeredVariable.new(0);
	};

	self.JointRotations.WaistRot.Changed:Connect(function()
		local value = self.JointRotations.WaistRot:Get();
		local NpcWaist = self.Prefab and self.Prefab:FindFirstChild("Waist", true);
		if NpcWaist then
			NpcWaist.C1 = CFrame.new(NpcWaist.C1.p) * CFrame.Angles(0, value, 0);
			NpcWaist.Parent:SetAttribute("WaistRot", value);
		end
	end)
	self.JointRotations.NeckRot.Changed:Connect(function()
		local value = self.JointRotations.NeckRot:Get();
		local NpcNeck = self.Prefab and self.Prefab:FindFirstChild("Neck", true);
		if NpcNeck then
			NpcNeck.C0 = CFrame.new(NpcNeck.C0.p) * CFrame.Angles(0, value, 0);
		end
	end)

	setmetatable(self, OldNpcClass);
	--== Initialize;

	self:AddComponent("Move");
	self:AddComponent("BehaviorTree");
	self:AddComponent("StatusLogic");
	self:AddComponent("ThreatSense");

	if modConfigurations.SpecialEvent.Christmas then
		self:AddComponent("Christmas")();
	end

	if self.Character:GetAttribute("HasRagdoll") == true then
		self:AddComponent("Ragdoll")();
	end

	--== Signals;
	self.Garbage:Tag(function()
		for _, jointLV in pairs(self.JointRotations) do
			jointLV:Destroy();
		end
	end)

	local waterCheckTick, lastInWaterTick = nil, nil;
	self.OnThink:Connect(function()
		if waterCheckTick and waterCheckTick > tick() then return end;
		waterCheckTick = tick()+ (self.Target and 1 or 5);

		local min = self.RootPart.Position - (4 * Vector3.one)
		local max = self.RootPart.Position + (4 * Vector3.one)
		local region = Region3.new(min,max):ExpandToGrid(4);
		local material = workspace.Terrain:ReadVoxels(region,4)[1][1][1];

		if material == Enum.Material.Water then
			lastInWaterTick = tick();

			if self.IsSwimming then return end;
			self.IsSwimming = true;
			self.Humanoid:SetAttribute("IsSwimming", self.IsSwimming);

			task.spawn(function()
				if self.WaterBodyVelocity == nil then
					self.WaterBodyVelocity = Instance.new("BodyVelocity");
				end
				self.WaterBodyVelocity.Name = "FloatVelocity";
				self.WaterBodyVelocity.MaxForce = Vector3.new(0, 500 * self.RootPart:GetMass(), 0);
				self.WaterBodyVelocity.Parent = self.RootPart;

				while self.IsSwimming and self.IsDead ~= true and tick()-lastInWaterTick <= 1 do
					self.WaterBodyVelocity.Velocity = Vector3.new(0, self.Humanoid.WalkSpeed, 0);

					task.wait(1);
				end
				self.IsSwimming = false;
				self.Humanoid:SetAttribute("IsSwimming", self.IsSwimming);

				if self.WaterBodyVelocity then
					game.Debris:AddItem(self.WaterBodyVelocity, 0);
					self.WaterBodyVelocity = nil;
				end;
			end)
		end
	end)

	self.Garbage:Tag(self.Humanoid.StateChanged:Connect(function(new, old)
		self:RefreshRagdollJoints();
	end))


	return self;
end