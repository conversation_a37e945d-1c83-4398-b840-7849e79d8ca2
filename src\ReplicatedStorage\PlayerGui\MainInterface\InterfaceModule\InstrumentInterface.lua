local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--== Configuration;

--== Variables;
local UserInputService = game:GetService("UserInputService");

local localPlayer = game.Players.LocalPlayer;
local modData = shared.require(localPlayer:WaitForChild("DataModule"));
local modBranchConfigs = shared.require(game.ReplicatedStorage.Library.BranchConfigurations);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library:WaitForChild("RemotesManager"));
local modGlobalVars = shared.require(game.ReplicatedStorage:WaitForChild("GlobalVariables"));
local modInstrument = shared.require(game.ReplicatedStorage.Library.InstrumentModule);
local modConfigurations = shared.require(game.ReplicatedStorage.Library:WaitForChild("Configurations"));

local templateButton = script:WaitForChild("TextButton");

local instrFrame = script.Parent.Parent:WaitForChild("InstrumentFrame");
local inputKeyBox = instrFrame:WaitForChild("input");
local notesList = instrFrame:WaitForChild("noteButtons");
local buttonsFrame = instrFrame:WaitForChild("Buttons");
local octiveLabel = instrFrame:WaitForChild("octiveLabel");

local isInstrumentFocus = false;
local isShiftDown = false;

local interfacePackage = {
    Type = "Character";
};

--== Script;
function interfacePackage.newInstance(interface: InterfaceInstance)
	local window: InterfaceWindow = interface:NewWindow("InstrumentWindow", instrFrame);

	if modConfigurations.CompactInterface then
		instrFrame.Size = UDim2.new(1, 0, 0, 100);
		window:SetClosePosition(UDim2.new(0.5, 0, -1.5, 0), UDim2.new(0.5, 0, 1, 0));

	else
		instrFrame.Size = UDim2.new(0, 784, 0, 90);
		window:SetClosePosition(UDim2.new(0.5, 0, -1.5, 0), UDim2.new(0.5, 0, 1, -75));

	end

	window:AddCloseButton(instrFrame);
	instrFrame:WaitForChild("touchCloseButton"):WaitForChild("closeButton").MouseButton1Click:Connect(function()
		window:Close();
	end)

	-- Initialize binds
	local binds = window.Binds;
	binds.ToggleAdv = false;
	binds.activeInstrument = nil;
	binds.toolHandler = nil;

	window.OnToggle:Connect(function(visible, toolHandler: ToolHandlerInstance)
		if visible then
			interface:HideAll{[window.Name]=true;};
			binds.Update(toolHandler);
			binds.Refresh();
		end
	end)
	
	buttonsFrame.ToggleAdv.MouseButton1Click:Connect(function()
		binds.ToggleAdv = not binds.ToggleAdv;
		binds.Refresh();
		binds.Update(binds.toolHandler);
	end)

	function binds.Refresh()
		if not binds.activeInstrument then return end

		local octiveVal = 5;
		if binds.activeInstrument.IsShiftDown then octiveVal = 6; end
		if binds.activeInstrument.IsCtrlDown then octiveVal = 4; end

		octiveLabel.UphotKey.BackgroundColor3 = not binds.activeInstrument.IsShiftDown and Color3.fromRGB(255,255,255) or Color3.fromRGB(153, 185, 255);
		octiveLabel.DownhotKey.BackgroundColor3 = not binds.activeInstrument.IsCtrlDown and Color3.fromRGB(255,255,255) or Color3.fromRGB(153, 185, 255);
		octiveLabel.Text = "Octave: "..octiveVal;

		if binds.ToggleAdv then
			instrFrame.Size = modConfigurations.CompactInterface and UDim2.new(1, 0, 0, 200) or UDim2.new(0, 784, 0, 190);

		else
			instrFrame.Size = modConfigurations.CompactInterface and UDim2.new(1, 0, 0, 100) or UDim2.new(0, 784, 0, 90);

		end
	end
	
	interface.Garbage:Tag(UserInputService.InputBegan:Connect(function(inputObject)
		if binds.activeInstrument == nil then return end;
		binds.activeInstrument.AdvanceMode = binds.ToggleAdv;

		if inputObject.KeyCode == Enum.KeyCode.LeftShift or inputObject.KeyCode == Enum.KeyCode.RightShift then
			binds.activeInstrument.IsShiftDown = true;
		end
		if inputObject.KeyCode == Enum.KeyCode.LeftControl or inputObject.KeyCode == Enum.KeyCode.RightControl then
			binds.activeInstrument.IsCtrlDown = true;
		end
		binds.Refresh();
	end))


	interface.Garbage:Tag(UserInputService.InputEnded:Connect(function(inputObject)
		if binds.activeInstrument == nil then return end;
		binds.activeInstrument.AdvanceMode = binds.ToggleAdv;

		if inputObject.KeyCode == Enum.KeyCode.LeftShift or inputObject.KeyCode == Enum.KeyCode.RightShift then
			binds.activeInstrument.IsShiftDown = false;
		end
		if inputObject.KeyCode == Enum.KeyCode.LeftControl or inputObject.KeyCode == Enum.KeyCode.RightControl then
			binds.activeInstrument.IsCtrlDown = false;
		end
		binds.Refresh();
	end))

	local inputBeganConn = UserInputService.InputBegan:Connect(function(inputObject)
		if inputObject.KeyCode == Enum.KeyCode.LeftShift or inputObject.KeyCode == Enum.KeyCode.RightShift then
			isShiftDown = true;
		end
		if inputKeyBox:IsFocused() and binds.activeInstrument then
			binds.activeInstrument:ProcessInputBegan(inputObject);
		end
	end)

	local inputEndedConn = UserInputService.InputEnded:Connect(function(inputObject)
		if inputObject.KeyCode == Enum.KeyCode.LeftShift or inputObject.KeyCode == Enum.KeyCode.RightShift then
			isShiftDown = false;
		end
		if binds.activeInstrument then
			binds.activeInstrument:ProcessInputEnded(inputObject);
		end
	end)

	interface.Garbage:Tag(function()
		if binds.activeInstrument then
			binds.activeInstrument:Destroy();
		end
		if inputBeganConn then
			inputBeganConn:Disconnect();
		end
		if inputEndedConn then
			inputEndedConn:Disconnect();
		end
	end)

inputKeyBox:GetPropertyChangedSignal("Text"):Connect(function()
	local lastNoteIndex = #inputKeyBox.Text;
	local latestNote = inputKeyBox.Text:sub(lastNoteIndex, lastNoteIndex);
	inputKeyBox.Text = latestNote;
	
end)

	-- ▶ play ■ stop
	local lastAdvMode = false;
	function binds.Update(toolH)
		binds.toolHandler = toolH;
		local storageItem = binds.toolHandler.StorageItem;

		for _, obj in pairs(notesList:GetChildren()) do
			if obj:IsA("GuiObject") then
				obj:Destroy();
			end
		end

		if storageItem == nil then Debugger:Log("Missing instrument item") return end

		local toolModel = binds.toolHandler.Prefabs[1];
		local configurations = binds.toolHandler.ToolPackage.Configurations;

		if configurations.Instrument then
			if binds.activeInstrument then
				binds.activeInstrument:Destroy();
			end
			binds.activeInstrument = modInstrument.new(configurations.Instrument, toolModel.Handle, toolModel.Handle);
			binds.activeInstrument.Player = game.Players.LocalPlayer;
			binds.activeInstrument.StorageItem = storageItem;

			if lastAdvMode ~= binds.ToggleAdv then
				lastAdvMode = binds.ToggleAdv;

				for _, obj in pairs(notesList:GetChildren()) do
					if obj:IsA("GuiObject") then
						obj:Destroy();
					end
				end
			end
		
			local baseOctave = 5;
			for _, note in pairs(modInstrument.Notes) do
				if binds.ToggleAdv then

					local advKeyList = note.AdvKey;

					for octive=-1, 1 do
						local advKeyInfo = advKeyList[octive+2];

						local new = templateButton:Clone();
						new.Text = note.Name..(baseOctave+octive);

						new.LayoutOrder = (-octive * 12) + note.Index;
						new.Parent = notesList;

						local hotKey = new:WaitForChild("hotKey");
						local hotKeyLabel = hotKey:WaitForChild("button");
						hotKeyLabel.Text = advKeyInfo.Key;

						new.MouseButton1Down:Connect(function()
							if binds.activeInstrument == nil then return end;
							binds.activeInstrument.AdvanceMode = binds.ToggleAdv;

							binds.activeInstrument:ProcessInputBegan({KeyCode=advKeyInfo.KeyCode});
						end)
						new.MouseButton1Up:Connect(function()
							if binds.activeInstrument == nil then return end;
							binds.activeInstrument.AdvanceMode = binds.ToggleAdv;

							binds.activeInstrument:ProcessInputEnded({KeyCode=advKeyInfo.KeyCode});
						end)
					
				end

				else

					local new = templateButton:Clone();
					new.Text = note.Name;
					new.Parent = notesList;

					local hotKey = new:WaitForChild("hotKey");
					local hotKeyLabel = hotKey:WaitForChild("button");
					hotKeyLabel.Text = note.Key;

					new.MouseButton1Down:Connect(function()
						if binds.activeInstrument == nil then return end;
						binds.activeInstrument.AdvanceMode = binds.ToggleAdv;

						binds.activeInstrument:ProcessInputBegan({KeyCode=note.KeyCode});
					end)
					new.MouseButton1Up:Connect(function()
						if binds.activeInstrument == nil then return end;
						binds.activeInstrument.AdvanceMode = binds.ToggleAdv;

						binds.activeInstrument:ProcessInputEnded({KeyCode=note.KeyCode});
					end)

				end
			end
		end
	end

	return interfacePackage;
end

return interfacePackage;