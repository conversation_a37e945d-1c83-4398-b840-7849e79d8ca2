local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local CollectionService = game:GetService("CollectionService");
local SoundService = game:GetService("SoundService");

local localPlayer = game.Players.LocalPlayer;
local camera = workspace.CurrentCamera;

local ClientSounds = {};

ClientSounds.AudioListener = nil;
ClientSounds.AudioDeviceOutput = nil;
--==

function ClientSounds.onRequire()
    local audioListener = Instance.new("AudioListener");
    audioListener.Parent = camera;
    ClientSounds.AudioListener = audioListener;
    
    local audioDeviceOutput = Instance.new("AudioDeviceOutput");
    audioDeviceOutput.Parent = SoundService;
    ClientSounds.AudioDeviceOutput = audioDeviceOutput;

    local wire = Instance.new("Wire");
    wire.SourceInstance = audioListener;
    wire.TargetInstance = audioDeviceOutput;
    wire.Parent = audioListener.Parent;
end

return ClientSounds;