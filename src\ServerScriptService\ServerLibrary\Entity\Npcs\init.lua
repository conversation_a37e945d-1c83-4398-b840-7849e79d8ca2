local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
local Npcs = {};

--== Script;
local CollectionService = game:GetService("CollectionService");
local RunService = game:GetService("RunService");

local modGlobalVars = shared.require(game.ReplicatedStorage.GlobalVariables);
local modConfigurations = shared.require(game.ReplicatedStorage.Library.Configurations);
local modEventSignal = shared.require(game.ReplicatedStorage.Library.EventSignal);
local modBranchConfigs = shared.require(game.ReplicatedStorage.Library.BranchConfigurations);
local modInteractables = shared.require(game.ReplicatedStorage.Library.Interactables);
local modReplicationManager = shared.require(game.ReplicatedStorage.Library.ReplicationManager);
local modNpcClass = shared.require(game.ReplicatedStorage.Entity.NpcClass);
local modNpcConstructor = shared.require(game.ServerScriptService.ServerLibrary.Entity.NpcClass);
local modNpcAnimator = shared.require(game.ServerScriptService.ServerLibrary.Entity.NpcAnimator);

local modHealthComponent = shared.require(game.ReplicatedStorage.Components.HealthComponent);

local npcScanOverlapParam = OverlapParams.new();
npcScanOverlapParam.FilterType = Enum.RaycastFilterType.Include;

local npcClassConstructors = {};

local templateNpcInstance, parallelNpcTemplate, npcPrefabs;
--==

function Npcs.onRequire()
	for _, npcMod in pairs(game.ServerScriptService.ServerLibrary.Entity.Npcs:GetChildren()) do
		if npcMod:IsA("ModuleScript") then
			npcClassConstructors[npcMod.Name] = shared.require(npcMod);
		end;
	end;

	local function searchCustom(t, k)
		local npcMod = game.ServerStorage.Prefabs.CustomNpcModules:FindFirstChild(k); 
		if npcMod then
			npcClassConstructors[npcMod.Name] = shared.require(npcMod);
			return npcClassConstructors[npcMod.Name];
		end
		return;
	end
	setmetatable(npcClassConstructors, {__index=searchCustom});

	templateNpcInstance = game.ServerScriptService.ServerLibrary.Entity.NpcClassInstance;

	parallelNpcTemplate = script.Parent:WaitForChild("ParallelNpc");
	npcPrefabs = game.ServerStorage.Prefabs.Npc;
	
	local npcPrefabsList = npcPrefabs:GetChildren();
	for a=1, #npcPrefabsList do
		local function updatePrefab(prefab)
			local humanoidInstance = prefab:FindFirstChildWhichIsA("Humanoid");

			if modBranchConfigs.IsWorld("TheInvestigation") then
				if prefab.Name == "Robert" then
					humanoidInstance.Name = "Zombie";
				end
			end

			if humanoidInstance.Name == "Human" then
				for _, obj in next, prefab:GetDescendants() do

					if obj:IsA("BasePart") then
						obj.CollisionGroup = "Characters";

					end
				end

			elseif humanoidInstance.Name == "Zombie" or humanoidInstance.Name == "Bandit" or humanoidInstance.Name == "Cultist" then
				for _, obj in next, prefab:GetDescendants() do
					if obj:IsA("BasePart") then
						obj.CollisionGroup = "EnemiesSpawn";
					end
				end

			end
		end
		
		if npcPrefabsList[a]:IsA("Folder") then 
			local variants = npcPrefabsList[a]:GetChildren();
			
			for _, prefab in pairs(variants) do
				updatePrefab(prefab);
				prefab:SetAttribute("Variant",prefab.Name);
				prefab.Name = npcPrefabsList[a].Name;
			end
			
			continue;
		end;
		
		updatePrefab(npcPrefabsList[a]);
	end
	npcPrefabsList = nil;

	
	local function onEntityRootPartChanged()
		npcScanOverlapParam.FilterDescendantsInstances = CollectionService:GetTagged("EntityRootPart");
	end
	CollectionService:GetInstanceAddedSignal("EntityRootPart"):Connect(onEntityRootPartChanged);

	local workspaceEntity = workspace:WaitForChild("Entity");

	workspaceEntity.ChildAdded:Connect(function(child)
		if not child:IsA("Model") or not child:IsA("Actor") then return end;

		for _, obj in pairs(child:GetDescendants()) do
			if not obj:IsA("BasePart") then continue end;
			if obj.Name == "HumanoidRootPart" then continue end;

			if obj.CollisionGroup == "EnemiesSpawn" then
				obj.CollisionGroup = "Enemies";
			end
		end
	end)

	workspaceEntity.ChildRemoved:Connect(function(child)
		for a=#Npcs.ActiveNpcClasses, 1, -1 do
			if Npcs.ActiveNpcClasses[a] and Npcs.ActiveNpcClasses[a].Character == child and not child:IsDescendantOf(workspaceEntity) then
				table.remove(Npcs.ActiveNpcClasses, a);
				break;
			end;
		end
	end)


	task.spawn(function()
		while true do
			for a=#Npcs.ActiveNpcClasses, 1, -1 do
				local npcClass: NpcClass = Npcs.ActiveNpcClasses[a];
				if npcClass == nil then continue end;

				local fireThinkS, _fireThinkE = pcall(function()
					npcClass.OnThink:Fire();
				end);
				if not fireThinkS then
					Debugger:Warn("Failed to think", Npcs.ActiveNpcClasses[a].Name);
				end;
			end

			task.wait(modConfigurations.NpcThinkCycle or 15);
		end
	end)
end

local bindSpawn = Instance.new("BindableFunction");
bindSpawn.Name = "SpawnNpc";
bindSpawn.Parent = script;

Npcs.NpcBaseConstructors = npcClassConstructors;
Npcs.NpcPackages = {};
Npcs.ActiveNpcClasses = {} :: {NpcClass};
Npcs.OnNpcSpawn = shared.EventSignal.new("OnNpcSpawn");
Npcs.GarbageModules = {};


function Npcs.getNpcPackage(name: string)
	local package = Npcs.NpcPackages[name];
	if package then return package; end;

	local packageModule = script:FindFirstChild(name);
	if packageModule == nil then return end;
	
	local npcPackage = shared.require(packageModule);
	if typeof(npcPackage) == "function" then return end;
	npcPackage.Script = packageModule;
	Npcs.NpcPackages[name] = npcPackage;

	return npcPackage;
end


local prefabCache = {};
function Npcs.getNpcPrefab(name)
	if prefabCache[name] then return prefabCache[name] end;
	if npcPrefabs:FindFirstChild(name) == nil then return; end;

	local prefab = npcPrefabs[name];
	if prefab:IsA("Folder") then
		local refPrefabName = prefab:GetAttribute("Prefab");
		if refPrefabName == nil then
			local prefabsList = prefab:GetChildren();
			return prefabsList[math.random(1, #prefabsList)];
			
		elseif npcPrefabs:FindFirstChild(refPrefabName) == nil then
			Debugger:Warn("Npc prefab ref: "..refPrefabName.." does not exist."); 
			
		else
			prefab = npcPrefabs[refPrefabName];
			if prefab:IsA("Folder") then
				local prefabsList = prefab:GetChildren();
				return prefabsList[math.random(1, #prefabsList)];
			end
			
		end;
	end
	
	prefabCache[name] = prefab;
	
	return prefabCache[name];
end

function Npcs.getByModel(characterModel)
	local npcInstance = characterModel:FindFirstChild("NpcClassInstance") and shared.require(characterModel.NpcClassInstance);
	if npcInstance == nil then return end;

	if npcInstance.NpcClass then
		return npcInstance.NpcClass;
	end
	
	for a=#Npcs.ActiveNpcClasses, 1, -1 do
		local npcClass = Npcs.ActiveNpcClasses[a];
		if npcClass == nil or npcClass.Character ~= characterModel then continue end;

		return npcClass;
	end

	return;
end

function Npcs.getById(id)
	for a=#Npcs.ActiveNpcClasses, 1, -1 do
		local npcClass = Npcs.ActiveNpcClasses[a];
		if npcClass == nil or npcClass.Id ~= id then continue end;

		return npcClass;
	end

	return;
end

function Npcs.getPlayerNpc(player: Player, npcName: string)
	for a=#Npcs.ActiveNpcClasses, 1, -1 do
		local npcClass = Npcs.ActiveNpcClasses[a];
		if npcClass == nil or npcClass.Name ~= npcName or npcClass.Player ~= player then continue end;

		return npcClass;
	end

	return;
end

function Npcs.listNpcClasses(matchingFunc: (npcClass: NpcClass) -> boolean) : {NpcClass}
	local list = {};

	for a=1, #Npcs.ActiveNpcClasses do
		local npcClass = Npcs.ActiveNpcClasses[a];
		if matchingFunc == nil or matchingFunc(npcClass) == true then
			table.insert(list, npcClass);
		end
	end

	return list;
end

function Npcs.listInRange(origin, radius, maxRootpart)
	local scannedTargets = {};
	if radius <= 0 then return scannedTargets; end;
	
	npcScanOverlapParam.MaxParts = maxRootpart or 32;
	
	local targets = workspace:GetPartBoundsInRadius(origin, radius, npcScanOverlapParam);

	for a=1, #targets do
		local targetModel = targets[a].Parent;
		if targetModel == nil then continue end;
		
		local npcClass = Npcs.getByModel(targetModel);
		if npcClass == nil or npcClass.IgnoreScan == true then continue end;

		table.insert(scannedTargets, npcClass);
	end
	
	return scannedTargets;
end

function Npcs.attractNpcs(attracterModel: Model, range: number, func: ((npcModule: NpcClass)-> boolean)? )
	local returnNpcModels = {};
	if attracterModel == nil then return returnNpcModels; end;

	local healthComp: HealthComp? = modHealthComponent.getByModel(attracterModel);
	if healthComp then
		attracterModel = healthComp:GetModel() :: Model;
	end
	if attracterModel == nil then return returnNpcModels; end;

	local player = game.Players:GetPlayerFromCharacter(attracterModel);
	local forcefield = attracterModel:FindFirstChildWhichIsA("ForceField") or nil;
	local scanEntities = Npcs.listInRange(attracterModel:GetPivot().Position, range);
	
	for a=1, #scanEntities do
		local npcClass = scanEntities[a];
		if npcClass == nil or npcClass.Character == attracterModel then continue end;
		if func and func(npcClass) == false then continue end;
		
		if player then
			if npcClass.NetworkOwners then
				if table.find(npcClass.NetworkOwners, player) == nil then
					continue;
				end
			end
			
			npcClass:LoadClientScript(player);
		end
		
		if forcefield == nil or forcefield.Name == "ForcefieldStatus" then -- ForcefieldStatus is power up;
			-- Old ontarget
			if npcClass.Target ~= attracterModel then
				if npcClass.OnTarget then
					npcClass.OnTarget(attracterModel);
				end
			end

			if npcClass.TargetHandler then
				npcClass.TargetHandler:AddTarget(attracterModel, healthComp);
			end
		end

		npcClass.OnThink:Fire();

		table.insert(returnNpcModels, npcClass.Character);
	end
	
	return returnNpcModels;
end

-- npcModule.Humanoid:SetStateEnabled(Enum.HumanoidStateType.FallingDown, false);
-- npcModule.Humanoid:SetStateEnabled(Enum.HumanoidStateType.Ragdoll, false);
-- npcModule.Humanoid:SetStateEnabled(Enum.HumanoidStateType.Physics, false);
-- npcModule.Humanoid:SetStateEnabled(Enum.HumanoidStateType.Swimming, false);
--
local unneededHumanoidStates = {
	--Enum.HumanoidStateType.Climbing;
	Enum.HumanoidStateType.Dead;
	Enum.HumanoidStateType.FallingDown; --
	Enum.HumanoidStateType.Flying;
	--Enum.HumanoidStateType.Freefall;
	--Enum.HumanoidStateType.GettingUp;
	--Enum.HumanoidStateType.Jumping;
	--Enum.HumanoidStateType.Landed;
	--Enum.HumanoidStateType.None;
	Enum.HumanoidStateType.Physics; --
	--Enum.HumanoidStateType.PlatformStanding;
	Enum.HumanoidStateType.Ragdoll; --
	--Enum.HumanoidStateType.Running;
	Enum.HumanoidStateType.RunningNoPhysics;
	--Enum.HumanoidStateType.Seated;
	Enum.HumanoidStateType.StrafingNoPhysics;
	Enum.HumanoidStateType.Swimming; --
};


--MARK: DoSpawn
Npcs.DoSpawn = function (name, cframe, preloadCallback, customNpcModule, customNpcPrefab)	
	local npcName = name;
	
	local npcPackage = Npcs.getNpcPackage(npcName);
	if npcPackage then
		local npcClass = Npcs.spawn2({
			Name = npcName;
			CFrame = cframe;
		});

		if preloadCallback then
			preloadCallback(npcClass.Character, npcClass);
		end

		return npcClass;
	end;

	-- TODO; deprecate
	local basePrefab = customNpcPrefab or Npcs.getNpcPrefab(name);
	if basePrefab == nil then return end;
	
	local npcPrefab: Actor = basePrefab:Clone();
	npcPrefab.ModelStreamingMode = Enum.ModelStreamingMode.PersistentPerPlayer;
	npcPrefab.Name = name;
	
	local hasInteractable = basePrefab:FindFirstChild("Interactable");

	local rootPart = npcPrefab:WaitForChild("HumanoidRootPart");
	rootPart:SetAttribute("IgnoreWeaponRay", true);
	
	cframe = cframe or rootPart.CFrame;
	npcPrefab:PivotTo(cframe);
	
	
	local npcClass;
	if customNpcModule then
		npcClass = customNpcModule(npcPrefab, cframe);
	elseif npcClassConstructors[name] then
		npcClass = npcClassConstructors[name](npcPrefab, cframe);
	else
		npcClass = Npcs.NpcBaseConstructors.BasicNpcModule(npcPrefab, cframe);
	end

	local humanoid: Humanoid = npcClass.Humanoid;
	
	table.insert(Npcs.ActiveNpcClasses, npcClass);
	script:SetAttribute("ActiveNpcs", #Npcs.ActiveNpcClasses);
	
	modNpcClass.IdCount = modNpcClass.IdCount +1;
	npcClass.Id = modNpcClass.IdCount;
	npcPrefab:SetAttribute("EntityId", npcClass.Id);
	
	for a=1, #unneededHumanoidStates do
		humanoid:SetStateEnabled(unneededHumanoidStates[a], false);
	end
	
	npcClass.SpawnTime = tick();
	npcClass.SpawnPoint = cframe;

	if npcClass.Properties then
		if npcClass.HumanoidType == "Zombie" then
			npcClass.Properties.IsHostile = true;
		end
	end

	local newNpcInstance = templateNpcInstance:Clone();
	newNpcInstance.Parent = npcPrefab;

	local npcInstance = shared.require(newNpcInstance);
	npcInstance.NpcClass = npcClass;
	
	-- if basePrefab:FindFirstChild("Interactable") then
	-- 	npcClass.Interactable = npcPrefab:WaitForChild("Interactable");
		
	-- else
	if basePrefab:GetAttribute("DialogueInteractable") == true then
		local newInteractable = modInteractables.createInteractable("Dialogue");
        newInteractable:SetAttribute("NpcName", npcClass.Name);
        newInteractable.Parent = npcPrefab;
		npcClass.Interactable = newInteractable;
		
	end

	if npcPrefab:IsA("Actor") then
		local newParallelHandler = parallelNpcTemplate:Clone();
		newParallelHandler.Parent = npcPrefab;
		
		npcClass.Remote = newParallelHandler:WaitForChild("NpcRemote");
		
		npcClass.ActorEvent.Event:Connect(function(action, ...)
			if action == "init" then 
				local pNpc = ...;
				npcClass.ParallelNpc = pNpc;
				return;
			end;

			if npcClass == nil or npcClass.IsDead then return end;

			--if action == "moveToEnded" then
			--	npcModule.Move.IsMoving = false;
			--	npcModule.Move.OnMoveToEnded:Fire(...);
				
			--end
		end)
		
		local bindActor: BindableFunction = Instance.new("BindableFunction");
		bindActor.Name = "ActorBind";
		bindActor.Parent = npcPrefab;
		npcClass.Bind = bindActor;

		newParallelHandler.Enabled = true;
		
	else
		npcClass.Remote = script.Parent:WaitForChild("OldNpcRemote");
		
	end
	
	task.spawn(function()
		task.wait(0.1);
		
		local rightArm: BasePart = npcPrefab:FindFirstChild("RightUpperArm") :: BasePart;
		local rightHand: BasePart = npcPrefab:FindFirstChild("RightHand") :: BasePart;
		local rightPoint: BasePart = npcPrefab:FindFirstChild("RightPoint") :: BasePart;
		if rightArm and rightHand and rightPoint then
			local middle: BasePart = npcPrefab:FindFirstChild("RightMiddle") :: BasePart;
			local pinky: BasePart = npcPrefab:FindFirstChild("RightPinky") :: BasePart;

			local function updateHand()
				rightPoint.Color = rightHand.Color;
				middle.Color = rightHand.Color;
				pinky.Color = rightHand.Color;

				rightPoint.Transparency = rightHand.Transparency;
				middle.Transparency = rightHand.Transparency;
				pinky.Transparency = rightHand.Transparency;
			end
			rightHand:GetPropertyChangedSignal("Color"):Connect(updateHand);
			rightHand:GetPropertyChangedSignal("Transparency"):Connect(updateHand);
			updateHand()
		end

		local leftArm: BasePart = npcPrefab:FindFirstChild("LeftUpperArm") :: BasePart;
		local leftHand: BasePart = npcPrefab:FindFirstChild("LeftHand") :: BasePart;
		local leftPoint: BasePart = npcPrefab:FindFirstChild("LeftPoint") :: BasePart;
		if leftArm and leftHand and leftPoint then
			local middle: BasePart = npcPrefab:FindFirstChild("LeftMiddle") :: BasePart;
			local pinky: BasePart = npcPrefab:FindFirstChild("LeftPinky") :: BasePart;

			local function updateHand()
				leftPoint.Color = leftHand.Color;
				middle.Color = leftHand.Color;
				pinky.Color = leftHand.Color;

				leftPoint.Transparency = leftHand.Transparency;
				middle.Transparency = leftHand.Transparency;
				pinky.Transparency = leftHand.Transparency;
			end
			leftHand:GetPropertyChangedSignal("Color"):Connect(updateHand);
			leftHand:GetPropertyChangedSignal("Transparency"):Connect(updateHand);
			updateHand()
		end
	end)
	
	
	if preloadCallback then 
		preloadCallback(npcPrefab, npcClass :: NpcClass);
		task.spawn(function()
			Npcs.OnNpcSpawn:Fire(npcClass);

			if npcClass.SetShirt then
				local shirt = npcPrefab:FindFirstChildWhichIsA("Shirt");
				if shirt then
					shirt.ShirtTemplate = npcClass.SetShirt;
				end
			end
			if npcClass.SetPants then
				local pants = npcPrefab:FindFirstChildWhichIsA("Pants");
				if pants then
					pants.PantsTemplate = npcClass.SetPants;
				end
			end
		end)
	end;
	
	if npcClass.Interactable == nil then
		if basePrefab:FindFirstChild("Interactable") then
			npcClass.Interactable = npcPrefab:WaitForChild("Interactable");
		end
	end
	
	if npcClass.Player then
		npcPrefab:AddPersistentPlayer(npcClass.Player);
	end
	npcPrefab.Parent = workspace.Entity;
	
	npcClass:SetNetworkOwner(nil);
	
	-- npcClass.Garbage:Tag(humanoid.Died:Connect(function()
	-- 	if npcClass == nil then return end;
	-- 	npcClass:Kill();
	-- end));
	npcClass.Garbage:Tag(npcPrefab.ChildRemoved:Connect(function(child)
		if child.Name ~= "HumanoidRootPart" then return end;
		if npcClass == nil then return end;
		npcClass:Kill();
	end))
	npcClass.Garbage:Tag(npcPrefab.Destroying:Connect(function()
		if npcClass == nil then return end;
		npcClass:Kill();
	end));
	if RunService:IsStudio() then
		npcClass.Garbage:Tag(npcPrefab.AncestryChanged:Connect(function()
			if npcPrefab.Parent ~= nil then return end;
			if npcClass == nil then return end;
			
			npcClass:Kill();
		end));
	end

	if modConfigurations.TargetableEntities[humanoid.Name] then
		CollectionService:AddTag(npcPrefab, "TargetableEntities");
		CollectionService:AddTag(npcPrefab:WaitForChild("HumanoidRootPart"), "Enemies");
	end
	if humanoid.Name == "Zombie" then
		CollectionService:AddTag(npcPrefab, "Zombies");
		
	elseif humanoid.Name == "Human" then
		CollectionService:AddTag(npcPrefab, "Humans");
		
	elseif humanoid.Name == "Bandit" then
		CollectionService:AddTag(npcPrefab, "Bandits");
		
	elseif humanoid.Name == "Cultist" then
		CollectionService:AddTag(npcPrefab, "Cultists");

	elseif humanoid.Name == "Rat" then
		CollectionService:AddTag(npcPrefab, "Rats");
		
	end
	
	if humanoid.Name == "Zombie" or humanoid.Name == "Bandit" or humanoid.Name == "Cultist" or humanoid.Name == "Rats" then
		task.delay(2, function()
			for _, obj in pairs(npcPrefab:GetDescendants()) do
				if obj:IsA("BasePart") and obj:IsDescendantOf(workspace) and obj.CollisionGroup == "EnemiesSpawn" then
					obj.CollisionGroup = "Enemies";
				end
			end
		end)
	end
	
	CollectionService:AddTag(rootPart, "EntityRootPart");

	modNpcAnimator(npcClass);
	if npcClass.BaseArmor then
		npcClass:AddComponent("ArmorSystem");
	end
	
	if npcClass.Initialize then
		task.defer(function()
			local key = string.gsub(name, " ", "");
			key = string.gsub(key, "%.", "");

			Npcs.GarbageModules[name] = (Npcs.GarbageModules[name] or 0) +1;
			script:SetAttribute(key, Npcs.GarbageModules[name]);
			
			npcClass.InitializeThread = coroutine.running();
			
			npcClass:LoadClientScript(game.Players:GetPlayers());
			npcClass.Initialize();
			
			local despawnPrefab = npcClass.DespawnPrefab;
			if despawnPrefab then
				game.Debris:AddItem(npcPrefab, despawnPrefab);
			end
			
			local autoRespawn = npcClass.AutoRespawn;
			
			task.spawn(function()
				local t=setmetatable({npcClass},{__mode='v'});
				local garbageTick = tick();
				
				repeat
					task.wait(10) 
					if tick()-garbageTick >= 300 then
						break;
					end
				until t[1] == nil;
				
				if t[1] == nil then
					Npcs.GarbageModules[name] = (Npcs.GarbageModules[name] or 0) -1;
					script:SetAttribute(key, Npcs.GarbageModules[name]);
					
				else
					local memSize = #game:GetService("HttpService"):JSONEncode(t[1]);
					if memSize >= 1024 then
						Debugger:Warn("Npc Memory Leak", name);
						task.spawn(function()
							modGlobalVars.DeepClearTable(t[1]);
						end)
					end
				end
				
				--for k,v in pairs(game.ServerScriptService.ServerLibrary.Entity.Npcs:GetAttributes()) do
				--	print(k,v);
				--end
			end)
			
			CollectionService:RemoveTag(rootPart, "EntityRootPart");
			npcClass:Destroy();
			script:SetAttribute("ActiveNpcs", #Npcs.ActiveNpcClasses);
			
			for a=#Npcs.ActiveNpcClasses, 1, -1 do
				if Npcs.ActiveNpcClasses[a] == npcClass then
					table.remove(Npcs.ActiveNpcClasses, a);
				end
			end
			
			npcClass = nil; -- IMPORTANT Need to clear strong ref;
			
			if autoRespawn then task.spawn(autoRespawn, name) end;
		end)
		
	end;
	
	if hasInteractable then
		npcClass.Interactable = npcPrefab:FindFirstChild("Interactable");
	end

	return npcPrefab;
end

--local templateSpawnSrc = game.ServerScriptService.ServerScripts:WaitForChild("NpcEngine");
Npcs.spawn = function(
	name: string,
	cframe: CFrame?, 
	preloadCallback: ((prefab: Model, npcClass: NpcClass) -> Model)?, 
	customNpcModule,
	customNpcPrefab: (Model)?
)
	return Npcs.DoSpawn(name, cframe, preloadCallback, customNpcModule, customNpcPrefab);
end

--MARK: spawn2
function Npcs.spawn2(args: {
	Name: string;
	CFrame: CFrame?;
	Player: Player?;
	AddComponents: {string}?;
	AddBehaviorTrees: {string}?;
	
	CustomNpcPrefab: Instance?;
	ReplicateOut: boolean?;
}): NpcClass?
	local npcName = args.Name;
	
	local npcPackage = Npcs.getNpcPackage(npcName);
	if npcPackage == nil then 
		Debugger:Warn("Npc Package not found", npcName);
		return; 
	end;

	local npcPrefabName = npcName;
	if npcPackage.CharacterPrefabName then
		npcPrefabName = npcPackage.CharacterPrefabName;
	end

	local baseNpcModel = args.CustomNpcPrefab or Npcs.getNpcPrefab(npcPrefabName);
	if baseNpcModel == nil then return end;
	
	local npcModel: Actor = baseNpcModel:Clone();
	npcModel.ModelStreamingMode = Enum.ModelStreamingMode.PersistentPerPlayer;
	npcModel.Name = npcName;
	
	local rootPart = npcModel:WaitForChild("HumanoidRootPart");
	rootPart:SetAttribute("IgnoreWeaponRay", true);
	
	local cframe = args.CFrame or rootPart.CFrame;
	npcModel:PivotTo(cframe);

	local npcClass: NpcClass = modNpcClass.new(npcPackage);
	npcClass.Player = args.Player;

	if args.AddComponents then
		for a=1, #args.AddComponents do
			local compName = args.AddComponents[a];
			npcClass:AddComponent(compName);
		end
	end
	if args.AddBehaviorTrees then
		for a=1, #args.AddBehaviorTrees do
			local treeName = args.AddBehaviorTrees[a];
			npcClass.BehaviorTree:LoadTree(treeName);
		end
	end

	npcClass:Setup(baseNpcModel, npcModel);
	npcClass.initHealthComp(npcClass, npcClass.HealthComp);

	npcClass.SpawnTime = tick();
	npcClass.SpawnPoint = cframe;
	
	table.insert(Npcs.ActiveNpcClasses, npcClass);
	script:SetAttribute("ActiveNpcs", #Npcs.ActiveNpcClasses);
	
	if args.ReplicateOut and args.Player then
		modReplicationManager.ReplicateOut(args.Player, npcClass.Character);
	end
	npcClass.OnThink:Fire();

	local humanoid: Humanoid = npcClass.Humanoid;

	if modConfigurations.TargetableEntities[humanoid.Name] then
		CollectionService:AddTag(npcModel, "TargetableEntities");
		CollectionService:AddTag(npcClass.RootPart, "Enemies");
	end
	CollectionService:AddTag(npcModel, `{npcClass.HumanoidType}s`);

	return npcClass;
end

function Npcs.GetCFrameFromPlatform(platform)
	local worldSpaceSize = platform.CFrame:vectorToWorldSpace(platform.Size);
	worldSpaceSize = Vector3.new(math.abs(worldSpaceSize.X), math.abs(worldSpaceSize.Y), math.abs(worldSpaceSize.Z));
		
	local pointMin = Vector3.new(platform.Position.X-worldSpaceSize.X/2, platform.Position.Y+worldSpaceSize.Y/2, platform.Position.Z-worldSpaceSize.Z/2);
	local pointMax = Vector3.new(platform.Position.X+worldSpaceSize.X/2, platform.Position.Y+worldSpaceSize.Y/2, platform.Position.Z+worldSpaceSize.Z/2);
	
	return CFrame.new(
		Vector3.new(
			math.random(pointMin.X *100, pointMax.X *100)/100, 
			pointMin.Y+2.1, 
			math.random(pointMin.Z *100, pointMax.Z *100)/100
		)
	) * CFrame.Angles(0, math.rad(math.random(0, 360)), 0);	
end

function Npcs.init(entity)
	setmetatable(Npcs, entity);
end

Npcs.Script = script;
shared.modNpcs = Npcs;
return Npcs;