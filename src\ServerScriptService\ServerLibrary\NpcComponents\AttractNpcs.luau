local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--== Script;
local Component = {};
Component.__index = Component;

function Component.new(npcClass: NpcClass)
	local self = {
        IsActive = false;

        AttractRange = 32;
        AttractHumanoidType = {};

        SelfAttractAlert = false;
        SelfAttractAlertDelay = {Min=0; Max=0.2};
    };
	
    function self:Activate()
        if self.IsActive then return end;
        self.IsActive = true;

        task.spawn(function()
            while self.IsActive do
                if npcClass.HealthComp.IsDead then break; end;
                if #self.AttractHumanoidType <= 0 then 
                    task.wait(2); 
                    continue; 
                end;
                
				local attractedModels = shared.modNpcs.attractNpcs(
                    npcClass.Character, 
                    self.AttractRange, 
                    function(npcClass: NpcClass)
                        local shouldAttract = false;

                        for a=1, #self.AttractHumanoidType do
                            if self.AttractHumanoidType[a] == npcClass.HumanoidType then
                                shouldAttract = true;
                            end
                        end

                        return shouldAttract;
                    end
                );

                local targetHandlerComp = npcClass:GetComponent("TargetHandler");
                if self.SelfAttractAlert and targetHandlerComp then
                    task.wait(math.random(self.SelfAttractAlertDelay.Min*1000, self.SelfAttractAlertDelay.Max*1000)/1000);

                    for a=1, #attractedModels do
                        local targetModel = attractedModels[a];
                        if targetModel == nil then continue end;

                        targetHandlerComp:AddTarget(targetModel);
                    end
                end

                task.wait(1);
            end
            self.IsActive = false;
        end)
    end


	
	setmetatable(self, Component);
	return self;
end

return Component;