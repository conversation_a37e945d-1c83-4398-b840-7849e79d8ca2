local modStrings = shared.require(game.ReplicatedStorage.Library.Util.Strings);

local LogicString = {};
LogicString.__index = LogicString;
LogicString.ClassName = "LogicString";

local function extractGroups(boolString)
	local groups = {};

	local toDoGroups = {
		boolString;
	};

	repeat
		local subject = table.remove(toDoGroups, 1);
		local c = 0;
		for match in subject:gmatch("%b()") do
			table.insert(toDoGroups, match:sub(2, #match-1));
			c = c+1;
		end
		table.insert(groups, subject);
	until #toDoGroups <= 0;

	return groups;
end

function LogicString.newEvaluator(logicTable)
	local self = {
		String = "";
        Groups = {};
	};

	if logicTable then
		if logicTable.Default == nil then
			error("LogicTable missing Default tree root.");
		end
		local str: string = logicTable.Default;
		for k, v in pairs(logicTable) do
			if k == "Default" then continue end;

			for k2, _ in pairs(logicTable) do
				logicTable[k2] = logicTable[k2]:gsub(k, `({v})`);
			end

			v = logicTable[k];
			str = str:gsub(k, `({v})`);
		end

	    str = str:gsub(" ", "");
		self.String = str;
	end

	setmetatable(self, LogicString);
	self:Setup();
	return self;
end

function LogicString:Setup()
	local str: string = self.String;

    local groups = extractGroups(str);    

    local mapGroups = {};

    for a=#groups, 1, -1 do
        mapGroups[a] = {GroupId=`_G{a}_`; GroupString=groups[a]};
    end

    for a=1, #mapGroups do
        local data = mapGroups[a];
        for b=1, #mapGroups do
            local data2 = mapGroups[b];
            if data.GroupString == data2.GroupString then continue end;
            data2.GroupString = modStrings.Replace(data2.GroupString, `({data.GroupString})`, data.GroupId);
        end
    end

    table.clear(self.Groups);
    if #mapGroups > 0 then
        mapGroups[1].GroupId = "Default";

        for a=1, #mapGroups do
            local data = mapGroups[a];
            self.Groups[data.GroupId] = data.GroupString;
        end
    end
end

function LogicString:Evaluate(evaluators, ...)
	local groups = self.Groups;

    local args = {...};
	local evalCache = {};

    local startStr = groups["Default"];
    local function evaluate(s)
        local result = false;

        local orList = string.split(s, "|");
        for a=1, #orList do
            result = true;

            local andList = string.split(orList[a], "&");
            for b=1, #andList do
                local item = andList[b];
                local key = item;
                local bool = true;

                if item:sub(1,1) == "!" then
                    key = item:sub(2);
                    bool = false;
                end

                if typeof(key) == "string" and key:sub(1,1) == "[" and key:sub(#key, #key) == "]" then
                    return key:sub(2, #key-1);
                end

                local evalValue;
                if groups[key] then
                    local r = evaluate(groups[key]);
                    if typeof(r) == "string" then
                        return r;
                    elseif typeof(r) == "boolean" then
                        evalValue = r;
                    elseif typeof(r) == "nil" then
                        return;
                    else
                        error("returned unknown type from group"..key..": "..typeof(r));
                    end

                else
                    evalValue = evalCache[key];
                    if evalValue == nil then
                        if evaluators[key] == nil then
                            error(`Evaluator for {key} not found.`);
                        end
                        evalValue = evaluators[key](unpack(args));
                        if evalValue == nil then
                            return;
                        elseif typeof(evalValue) == "string" then
                            return evalValue;
                        end
                        evalCache[key] = evalValue;
                    end
                end

                local value = evalValue == bool;
                if value == false then
                    result = false;
                    break;
                end
            end

            if result == true then
                return true;
            end
        end

        return result;
    end

    local r = evaluate(startStr);
    return r, evalCache;
end

return LogicString;