local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--== Services;
local CollectionService = game:GetService("CollectionService");

local modWeaponAttributes = shared.require(game.ReplicatedStorage.Library.WeaponsLibrary.WeaponAttributes);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modConfigurations = shared.require(game.ReplicatedStorage.Library.Configurations);
local modWeaponsMechanics = shared.require(game.ReplicatedStorage.Library.WeaponsMechanics);
local modFormatNumber = shared.require(game.ReplicatedStorage.Library.FormatNumber);
local modProjectile = shared.require(game.ReplicatedStorage.Library.Projectile);
local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modDamageTag = shared.require(game.ReplicatedStorage.Library.DamageTag);
local modHealthComp = shared.require(game.ReplicatedStorage.Components.HealthComponent);

local modDamageData = shared.require(game.ReplicatedStorage.Data.DamageData);

local modMath = shared.require(game.ReplicatedStorage.Library.Util.Math);
local modTables = shared.require(game.ReplicatedStorage.Library.Util.Tables);

local remotePrimaryFire; 
--==
local ToolService = {};

function ToolService.onRequire()
	remotePrimaryFire = modRemotesManager:Get("PrimaryFire");

end

-- MARK: CancelPrimaryFire
function ToolService.CancelPrimaryFire(packet)
	packet.ToolModule = packet.ToolModule;
	--
	
	local toolModule = packet.ToolModule;
	
	if toolModule.Cache.AudioPrimaryFire then
		toolModule.Cache.AudioPrimaryFire:Destroy();
		toolModule.Cache.AudioPrimaryFire = nil;
	end
end

-- MARK: PriamryFireWeapon
function ToolService.PrimaryFireWeapon(firePacket)
	firePacket.ShotBy = firePacket.ShotBy;
	firePacket.StorageItem = firePacket.StorageItem;
	firePacket.ToolHandler = firePacket.ToolHandler;
	
	firePacket.ShotOrigin = firePacket.ShotOrigin; -- Vector3
	firePacket.ShotDirection = firePacket.ShotDirection; -- Vector3
	
	firePacket.Targetable = firePacket.Targetable;
	
	firePacket.ReplicateToShotOwner = firePacket.ReplicateToShotOwner;
	
	firePacket.FocusCharge = firePacket.FocusCharge;
	--
	local shotPacket = {
		StorageItem = firePacket.StorageItem;
		ToolHandler = firePacket.ToolHandler;
		
		ReplicateToShotOwner = firePacket.ReplicateToShotOwner;
		FocusCharge = firePacket.FocusCharge;

		ShotOrigin = firePacket.ShotOrigin;
		ShotBy = firePacket.ShotBy;

		IsPat = firePacket.IsPat;
		RicochetCount = firePacket.RicochetCount;
	};
	
	
	local storageItem = firePacket.StorageItem;
	local storageItemID = storageItem.ID;
	
	local toolHandler: ToolHandlerInstance = firePacket.ToolHandler;
	local equipmentClass: EquipmentClass = toolHandler.EquipmentClass;
	local toolPackage = toolHandler.ToolPackage;

	local toolModel = firePacket.ToolModel;
	local toolHandle: BasePart = toolModel.PrimaryPart;

	local isRicochet = firePacket.RicochetCount ~= nil;
	
	local configurations = equipmentClass.Configurations;
	local properties = equipmentClass.Properties;
	local audio = toolPackage.Audio;

	local ammo = storageItem:GetValues("A") or configurations.MagazineSize;
	local maxAmmo = storageItem:GetValues("MA") or configurations.AmmoCapacity;

	if isRicochet then
		shotPacket.RicochetCount = shotPacket.RicochetCount-1;
	end

	if isRicochet ~= true and ammo <= 0 then 
		modAudio.Play(audio.Empty.Id, toolHandle);
		return; 
	end
	
	if isRicochet ~= true and properties.IsPrimaryFiring then return end
	properties.IsPrimaryFiring = true;
	local onShotTick = tick();

	task.spawn(function()
		local shotTick = tick();
		local ammoCost = math.min(configurations.AmmoCost or 1, properties.Ammo);

		if configurations.Triplethreat then
			ammoCost = properties.InfiniteAmmo == 2 and 3 or math.min(properties.Ammo, 3);
		end
		
		if isRicochet ~= true then
			properties.Ammo = properties.Ammo - (properties.InfiniteAmmo == 2 and 0 or ammoCost);
			
			if audio.PrimaryFire.Looped then
				if properties.LoopedPrimaryFireAudio == nil or not workspace:IsAncestorOf(properties.LoopedPrimaryFireAudio) then
					local primaryFireSound = modAudio.Play(audio.PrimaryFire.Id, toolHandle);
					primaryFireSound.Name = "PrimaryFireSound";
					primaryFireSound.Looped = true;
					primaryFireSound.Volume = 2;
					properties.LoopedPrimaryFireAudio = primaryFireSound;
				end
				
			else
				local primaryFireSound = modAudio.Play(audio.PrimaryFire.Id, toolHandle, false);
				if configurations.PrimaryFireAudio ~= nil then configurations.PrimaryFireAudio(primaryFireSound, 1); end
				
			end
		end
		
		local multishot = type(configurations.Multishot) == "table" and math.random(configurations.Multishot.Min, configurations.Multishot.Max) or configurations.Multishot;

		if configurations.Triplethreat then
			multishot = ammoCost;
		end
		
		-- shotPacket.ShotOrigin = toolHandle:FindFirstChild("BulletOrigin");
		-- assert(shotPacket.ShotOrigin, `Missing bullet origin: {toolHandle:GetFullName()}`);
		if configurations.BulletMode == modWeaponAttributes.BulletModes.Hitscan then
			shotPacket.TargetPoints = {};
			shotPacket.Victims = {};
			shotPacket.HitInfoList = {};

		elseif configurations.BulletMode == modWeaponAttributes.BulletModes.Projectile then
			shotPacket.Projectiles = {};
		end
		
		local direction = firePacket.ShotDirection;
		shotPacket.Direction = direction;
		
		local accRatio, accRate;
		for multiIndex=1, multishot do

			local newInaccuracy = configurations.StandInaccuracy;
			
			if firePacket.IsPat then
				if accRatio == nil then
					accRatio = math.clamp(modMath.MapNum(newInaccuracy, 2, 20, 0, 1), 0, 1);
					accRate = 1-math.pow(accRatio, 1/2);
				end
				local imulti = (1.5+(2*accRate));
				
				imulti = imulti * firePacket.MaistPercent;
				
				local nI = newInaccuracy * imulti;
				
				newInaccuracy = nI;
			end
			
			local spreadedDirection = modMath.CFrameSpread(direction, math.max(newInaccuracy, 0));

			if configurations.BulletMode == modWeaponAttributes.BulletModes.Hitscan then
				
				local function onCast(basePart, position, normal, material, index, distance)
					if basePart == nil then return end;

					table.insert(shotPacket.HitInfoList, {
						Part=basePart;
						Position=position;
						Normal=normal;
						Index=index;
					});
					
					local humanoid = basePart.Parent:FindFirstChildWhichIsA("Humanoid");
					local targetRootPart = basePart.Parent:FindFirstChild("HumanoidRootPart");

					if humanoid then
						if humanoid.Health > 0 then
							if targetRootPart then
								if (basePart.Name == "Head" or basePart:GetAttribute("IsHead") == true) then
									local hitSoundRoll = math.random(0, 1) == 1 and "BulletHeadImpact" or "BulletHeadImpact2";
									modAudio.Play(hitSoundRoll, basePart);
								else
									local hitSoundRoll = math.random(0, 1) == 1 and "BulletBodyImpact" or "BulletBodyImpact2";
									modAudio.Play(hitSoundRoll, basePart);
								end
								table.insert(shotPacket.Victims, {Object=((basePart.Name == "Head" or basePart:GetAttribute("IsHead") == true) and basePart or targetRootPart); Index=index;});
							end
							return basePart.Parent;
						end
					else
						if basePart.Parent:FindFirstChild("Destructible") and basePart.Parent.Destructible.ClassName == "ModuleScript" then
							table.insert(shotPacket.Victims, {Object=basePart; Index=index;});
						end
					end

					return;
				end

				local whitelist = {workspace.Environment; workspace.Terrain};
				if firePacket.Targetable then
					if firePacket.Targetable.Zombie then
						whitelist = CollectionService:GetTagged("Zombies");
						table.insert(whitelist, workspace.Environment);
					end
					if firePacket.Targetable.Human then
						local humanoidList = CollectionService:GetTagged("Humans");
						for a=1, #humanoidList do
							table.insert(whitelist, humanoidList[a]);
						end
						table.insert(whitelist, workspace.Environment);
					end

					if firePacket.Targetable.Bandit then
						local humanoidList = CollectionService:GetTagged("Bandits");
						for a=1, #humanoidList do
							table.insert(whitelist, humanoidList[a]);
						end
						table.insert(whitelist, workspace.Environment);
					end

					if firePacket.Targetable.Cultist then
						local humanoidList = CollectionService:GetTagged("Cultists");
						for a=1, #humanoidList do
							table.insert(whitelist, humanoidList[a]);
						end
						table.insert(whitelist, workspace.Environment);
					end

					if firePacket.Targetable.Rat then
						local humanoidList = CollectionService:GetTagged("Rats");
						for a=1, #humanoidList do
							table.insert(whitelist, humanoidList[a]);
						end
						table.insert(whitelist, workspace.Environment);
					end

					if firePacket.Targetable.Humanoid then
						local humanoidList = CollectionService:GetTagged("PlayerCharacters");
						for a=1, #humanoidList do
							table.insert(whitelist, humanoidList[a]);
						end
						table.insert(whitelist, workspace.Environment);
					end
				end

				local bulletEnd = modWeaponsMechanics.CastHitscanRay{
					Origin = firePacket.ShotOrigin;
					Direction = spreadedDirection;
					IncludeList = whitelist;
					Range = 256;
					OnCastFunc = onCast;
					MaxPierce = properties.Piercing;
					PenTable = configurations.Penetration;
				};

				table.insert(shotPacket.TargetPoints, bulletEnd);
				
			elseif configurations.BulletMode == modWeaponAttributes.BulletModes.Projectile then

				local projData = {};
				projData.Origin = CFrame.new(firePacket.ShotOrigin);
				projData.Orientation = toolHandle.Orientation;
				projData.Direction = firePacket.ShotDirection;

				if multiIndex > 1 then
					local leftRight = multiIndex%2 == 0 and 1 or -1;
					local radMultiplier = math.floor(multiIndex/2);

					local deg = 3;
					projData.Direction = CFrame.Angles(0, math.rad(deg * leftRight * radMultiplier), 0):VectorToWorldSpace(firePacket.ShotDirection);
				end

				table.insert(shotPacket.Projectiles, projData);
				
			end
		end
		
		ToolService.ProcessWeaponShot(shotPacket);
		
		task.wait(properties.FireRate or 0.1);
		properties.IsPrimaryFiring = false;
	end)
	
	return true;
end

--MARK: ProcessWeaponShot
function ToolService.ProcessWeaponShot(shotPacket)
	shotPacket.ShotBy = shotPacket.ShotBy;
	shotPacket.ToolHandler = shotPacket.ToolHandler;
	shotPacket.ToolModel = shotPacket.ToolModel;

	-- BulletMode: Hitscan
	shotPacket.ShotOrigin = shotPacket.ShotOrigin;

	shotPacket.Victims = shotPacket.Victims;
	shotPacket.TargetPoints = shotPacket.TargetPoints;
	shotPacket.HitInfoList = shotPacket.HitInfoList;

	-- BulletMode: Projectile
	shotPacket.Projectiles = shotPacket.Projectiles;
	shotPacket.FocusCharge = shotPacket.FocusCharge;
	--
	shotPacket.RicochetCount = shotPacket.RicochetCount;

	local shotBy: CharacterClass = shotPacket.ShotBy;

	local toolHandler: ToolHandlerInstance = shotPacket.ToolHandler;
	local equipmentClass: EquipmentClass = toolHandler.EquipmentClass;
	local toolPackage = toolHandler.ToolPackage;
	
	local storageItem = toolHandler.StorageItem;
	local toolModel: Model = shotPacket.ToolModel or toolHandler.Prefabs[1];
	assert(toolModel.PrimaryPart, `Missing tool primary part for:{toolModel:GetFullName()}`);

	local toolHandle: BasePart = toolModel.PrimaryPart;
	
	local siid = storageItem.ID;
	local itemId = storageItem.ItemId;
	
	local realShotId = math.random(1, 99);
	
	local isRicochet = shotPacket.RicochetCount ~= nil;

	
	local configurations = equipmentClass.Configurations;
	local properties = equipmentClass.Properties;
	
	local baseFirerate = 60/configurations.Rpm;
	
	local ammo = storageItem:GetValues("A") or configurations.MagazineSize;
	local maxAmmo = storageItem:GetValues("MA") or configurations.AmmoCapacity;

	if isRicochet ~= true and ammo <= 0 then return; end
	ammo = math.min(ammo, configurations.MagazineSize);

	local ammoCost = math.min(configurations.AmmoCost or 1, ammo);
	if configurations.Triplethreat then
		ammoCost = math.min(ammo, 3);
	end
	
	if properties.InfiniteAmmo == 2 then
		ammoCost = 0;
	end

	if isRicochet ~= true then
		ammo = ammo -ammoCost;
		storageItem:SetValues("A", ammo);

	end
	
	local newDmgData: DamageData = modDamageData.new{
		DamageBy = shotBy;
		DamageId = realShotId;

		ToolHandler = toolHandler;
		StorageItem = storageItem; 
		IsPat = shotPacket.IsPat;
	};
	
	if configurations.BulletMode == modWeaponAttributes.BulletModes.Hitscan then
		local playersShot = {};
		local victims = (shotPacket.Victims or {});

		local targetsPierceable = (configurations.Piercing or 0);
		
		local hitInfoList = shotPacket.HitInfoList;
		for a=1, #hitInfoList do
			local hitInfo = hitInfoList[a];

			--MARK: OnBulletHit
			equipmentClass:ProcessModifiers("OnBulletHit", {
				Player=shotPacket.Player;
				HitInfo=hitInfo;
				WeaponModel=shotPacket.ToolModel;

				Index=a;
				EndIndex=#hitInfoList;
				
				ShotDirection=shotPacket.Direction;
				FocusCharge=(shotPacket.FocusCharge and math.clamp(shotPacket.FocusCharge, 0, 1) or nil);

				RicochetCount = shotPacket.RicochetCount;
			});
		end

		local maxVictims = math.clamp(
			#victims, 
			0, 
			(
				typeof(configurations.Multishot) == "table" 
				and (configurations.Multishot.Max + targetsPierceable) 
				or configurations.Multishot + targetsPierceable
			)
		);

		local function processTarget(subject: {Part: BasePart; Index: number;})
			local targetPart = subject.Part;
			local targetIndex = subject.Index;
			local targetModel = targetPart and targetPart.Parent;

			if targetModel and targetModel:IsA("Accessory") then
				targetModel = targetModel.Parent;
			end

			-- shot handler;
			local validDirectory = workspace:IsAncestorOf(targetPart) or game.ReplicatedStorage.Replicated:IsAncestorOf(targetPart)
			if targetModel == nil or not validDirectory then return; end;
			
			local distance = (targetPart.Position-toolHandle.Position).Magnitude;
			local direction = (targetPart.Position-toolHandle.Position).Unit;
			if distance >= 400 then return; end;

			local healthComp: HealthComp? = modHealthComp.getByModel(targetModel);
			if healthComp == nil or healthComp.IsDead or not healthComp:CanTakeDamageFrom(shotBy) then return; end;
			
			if shotPacket.ShotBy then
				modDamageTag.Tag(targetModel, shotPacket.ShotBy.Character, {
					WeaponItemId=itemId;
					IsHeadshot=((targetPart.Name == "Head" or targetPart:GetAttribute("IsHead") == true) and true or nil);
				});
			end

			local preModDamage = configurations.PreModDamage;
			local damage = configurations.Damage;
		
			if configurations.DamageScaler and configurations.DamageScaler ~= 0 then 
				damage = damage * (1+configurations.DamageScaler);
				Debugger:Warn("DamageScaler", damage, configurations.DamageScaler);
			end

			newDmgData.TargetModel = targetModel;
			newDmgData.TargetPart = targetPart;

			local dmgDataClone = newDmgData:Clone();
			dmgDataClone.Damage = damage;

			dmgDataClone.Index = targetIndex;
			dmgDataClone.EndIndex = maxVictims;

			local killImpulseForce = configurations.KillImpulseForce or 5;
			dmgDataClone.DamageForce = direction*killImpulseForce;
			dmgDataClone.DamagePosition = targetPart.Position;

			equipmentClass:ProcessModifiers("OnNewDamage", dmgDataClone);
			damage = dmgDataClone.Damage;

			if healthComp.CompOwner.ClassName == "Destructible" then
				healthComp:TakeDamage(dmgDataClone);

			else

				-- Shot verification
				--if weaponModel.PrimaryPart == nil then return end;
				--local shotOrigin = weaponModel.PrimaryPart.Position;
				--local ray = Ray.new(shotOrigin, shotdata.Direction);

				--local closestDistance = ray:Distance(targetObject.Position);
				--local maxDistance = math.max(targetObject.Size.X, targetObject.Size.Y, targetObject.Size.Z, 4)+5;

				--if humanoid and humanoid.RootPart then
				--	maxDistance = maxDistance * math.clamp(humanoid.RootPart.Velocity.Magnitude, 1, 100);
				--end

				--if closestDistance >= maxDistance then
				--	Debugger:Warn("Player ("..client.Name..") did an illegal shot. Distance:",closestDistance.."/"..maxDistance);
				--	return;
				--end;
				-- Shot verification

				-- Damage post processing ===================================================================================================
				if targetIndex and targetIndex >= 2 then
					local piercingDamageReduction = 0.5;
					damage = damage * piercingDamageReduction ^ targetIndex;
				end

				if configurations.DamageDropoff then
					damage = modWeaponsMechanics.DamageDropoff(equipmentClass, damage, distance);
				end
				
				
				-- Apply damage ===================================================================================================
				if healthComp.CompOwner.ClassName == "NpcClass" then
					local npcClass: NpcClass = (healthComp.CompOwner :: NpcClass);
					local humanoid: Humanoid = npcClass.Humanoid;

					if configurations.WeaponType == modWeaponAttributes.WeaponType.Pistol then
						---
					elseif configurations.WeaponType == modWeaponAttributes.WeaponType.Rifle then
						if humanoid.FloorMaterial and npcClass.StunFlag ~= true and npcClass.KnockbackResistant == nil then
							humanoid.RootPart.Velocity = humanoid.RootPart.Velocity + direction*50;
						end

					elseif configurations.WeaponType == modWeaponAttributes.WeaponType.Shotgun then
						dmgDataClone.BreakJoint = math.random(1, math.max(maxVictims, 3)) == 1;

					end

					if npcClass.WeakPoint then
						npcClass.WeakPoint(targetPart, function()
							if shotBy.ClassName == "PlayerClass" then
								local player: Player = (shotBy :: PlayerClass):GetInstance()
								local profile: Profile = shared.modProfile:Get(player);

								local skill = profile.SkillTree:GetSkill(player, "weapoi");
								
								local level, skillStats = profile.SkillTree:CalStats(skill.Library, skill.Points);
								local wpMulti = (skillStats.Percent.Default + skillStats.Percent.Value)/100;

								local add = preModDamage * wpMulti;

								damage = damage + add;
							end

							dmgDataClone.DamageType = "Crit";
						end);
					end
				end
			
				healthComp:TakeDamage(dmgDataClone);
			end
		end

		for a=1, maxVictims do
			processTarget(victims[a]);
		end
		for a=1, #hitInfoList do
			local part = hitInfoList[a].Part;
			if part.Parent == nil then continue end;
			
			local healthComp: HealthComp = modHealthComp.getByModel(part.Parent);
			if healthComp == nil or healthComp.CompOwner.ClassName ~= "Destructible" then continue end;
			processTarget(hitInfoList[a]);
		end
		
		
		local players = game.Players:GetPlayers();
		local weaponShotPacket = {
			ItemId=itemId;
			ToolModel=toolModel;
			TargetPoints=shotPacket.TargetPoints;
			
			ShotOrigin=shotPacket.ShotOrigin;
			IsRicochet=isRicochet;
		};

		local shotOwner: Player = nil;
		if shotBy and shotBy.ClassName == "PlayerClass" then
			shotOwner = (shotBy :: PlayerClass):GetInstance();
		end

		for a=1, #players do
			local playerA: Player = players[a];
			if playerA == nil then continue end;
			if modConfigurations.PvpMode then
				if playerA == shotOwner and shotPacket.ReplicateToShotOwner ~= true then
					continue;
				end
				
				remotePrimaryFire:FireClient(playerA, "fire", weaponShotPacket);

			else
				local pCharacter = playerA.Character;
				local pRootPart = pCharacter ~= nil and pCharacter:FindFirstChild("HumanoidRootPart") or nil;

				if playersShot[playerA.Name] then
					remotePrimaryFire:FireClient(playerA, "fire", weaponShotPacket);

				elseif pRootPart then
					
					if playerA == shotPacket.Player and shotPacket.ReplicateToShotOwner ~= true then
						continue;
					end
					
					if pRootPart and (pRootPart.Position-toolHandle.Position).Magnitude < 64 then
						remotePrimaryFire:FireClient(playerA, "fire", weaponShotPacket);
					end

				end
			end
		end
		
	elseif configurations.BulletMode == modWeaponAttributes.BulletModes.Projectile then
		local projectileId = storageItem:GetValues("CustomProj") or configurations.ProjectileId;

		local projectiles = shotPacket.Projectiles;
		local projectileCount = type(configurations.Multishot) == "table" and (configurations.Multishot.Max) or configurations.Multishot;

		if configurations.Triplethreat then
			projectileCount = 3;
		end

		for a=1, math.min(#projectiles, projectileCount) do
			local projectileData = projectiles[a];

			local rot = projectileData.Orientation;
			local projectile: ProjectileInstance = modProjectile.fire(projectileId, {
				OriginCFrame = projectileData.Origin * CFrame.fromOrientation(rot.X, rot.Y, rot.Z);
				ToolHandler = toolHandler;
			})
			local projProperties = projectile.Properties;

			projProperties.DamageSource = newDmgData;

			if configurations.FocusDuration then
				if shotPacket.FocusCharge and shotPacket.FocusCharge > 0 then
					local charge = math.clamp(shotPacket.FocusCharge, 0, 1);
					projProperties.Charge = charge;
				end
			end

			if projectile.Part:CanSetNetworkOwnership() then 
				projectile.Part:SetNetworkOwner(nil); 
			end

			local arcConfig = configurations.ArcTracerConfig;
			if arcConfig == nil or projectile.ArcTracerConfig then
				arcConfig = projectile.ArcTracerConfig;
			end

			projectileData.Direction = projectileData.Direction.Unit;
			local velocity = projectileData.Direction * arcConfig.Velocity;
			local projectileOrigin = projectileData.Origin.Position;

			local arcTracer = projectile.ArcTracer;

			if modConfigurations.AutoAdjustProjectileAim == true then
				local timelapse = projectileData.Dist/arcConfig.Velocity;
				velocity = arcTracer:GetVelocityByTime(
					projectileOrigin,
					projectileData.RayEndPoint, timelapse);
			end

			modProjectile.serverSimulate(projectile, {
				Velocity = velocity;
			});
		end
		
	end
	
end

-- !outline ToolService.ReloadWeapon(packet)
function ToolService.ReloadWeapon(packet)
	packet.StorageItem = packet.StorageItem;
	packet.ToolModel = packet.ToolModel;
	packet.ToolModule = packet.ToolModule;
	
	local storageItem = packet.StorageItem;
	local toolHandle = packet.ToolModel.PrimaryPart;
	
	local toolModule;
	
	if packet.ToolModule then
		toolModule = packet.ToolModule;
	end
	
	local configurations = toolModule.Configurations;
	local properties = toolModule.Properties;
	local audio = toolModule.Audio;

	local ammo = storageItem:GetValues("A") or configurations.MagazineSize;
	local maxAmmo = storageItem:GetValues("MA") or configurations.AmmoCapacity;
	
	properties.Ammo = ammo;
	properties.MaxAmmo = maxAmmo;
	
	if properties.Reloading then return false; end;
	if properties.Ammo == configurations.MagazineSize then return false; end;
	if maxAmmo <= 0 then return false; end;
	properties.Reloading = true;
	
	local magazinePart = packet.ToolModel:FindFirstChild("Magazine");
	
	local reloadTime = math.clamp(configurations.ReloadTime-0.2, 0.05, 40);
	if packet.IsPat then
		reloadTime = reloadTime * 3;
		
		if magazinePart then
			local oldPar = magazinePart.Parent;
			task.spawn(function()
				magazinePart.Parent = nil;
				task.wait(reloadTime);
				magazinePart.Parent = oldPar;
			end)
		end
	end
		
	if configurations.ReloadMode == modWeaponAttributes.ReloadModes.Full then

		local reloadSound;
		if audio.Reload then
			reloadSound = modAudio.Play(audio.Reload.Id, toolHandle);
			reloadSound.PlaybackSpeed = reloadSound.TimeLength/configurations.ReloadTime;
		end

		task.wait(reloadTime);

		if not workspace:IsAncestorOf(packet.ToolModel) then
			return false;
		end
		
		local ammoNeeded = configurations.MagazineSize - ammo;
		local newMaxAmmo = maxAmmo - ammoNeeded;
		local newAmmo = configurations.MagazineSize;
		if newMaxAmmo < 0 then newAmmo = maxAmmo+ammo; newMaxAmmo = 0 end;
		properties.Ammo = newAmmo;
		properties.MaxAmmo = properties.InfiniteAmmo == nil and newMaxAmmo or configurations.AmmoCapacity;
		
		storageItem:SetValues("A", properties.Ammo);
		storageItem:SetValues("MA", properties.MaxAmmo);
		
	elseif configurations.ReloadMode == modWeaponAttributes.ReloadModes.Single and ammo < configurations.MagazineSize then
		local ammoCost = configurations.AmmoCost or 1;

		if configurations.DualShell then
			ammoCost = 2;
		end
		if ammo + ammoCost > configurations.MagazineSize then
			ammoCost = math.clamp(configurations.MagazineSize-ammo, 1, ammoCost);
		end

		if audio.Reload then
			modAudio.Play(audio.Reload.Id, toolHandle);
		end
		task.wait(reloadTime);

		if not workspace:IsAncestorOf(packet.ToolModel) then
			return false;
		end

		local ammoFromMA = 0;
		if maxAmmo > 0 and ammo < configurations.MagazineSize then
			ammoFromMA = math.min(ammoCost, maxAmmo);

			ammo = ammo +ammoFromMA;
			maxAmmo = properties.InfiniteAmmo == nil and (maxAmmo - ammoFromMA) or configurations.AmmoCapacity;

			storageItem:SetValues("A", ammo);
			storageItem:SetValues("MA", maxAmmo);
		end
		
	end
	
	properties.Reloading = false;
	return true;
end

return ToolService;
