local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);

local RunService = game:GetService("RunService");
if RunService:IsServer() then return {}; end;

local localPlayer = game.Players.LocalPlayer;
local playerGui = localPlayer:WaitForChild("PlayerGui");

local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modPropertiesVariable = shared.require(game.ReplicatedStorage.Library.PropertiesVariable);
local modCameraGraphics = shared.require(game.ReplicatedStorage.PlayerScripts.CameraGraphics);

local playerGuiBase;

local ClientGuis = {};
ClientGuis.ActiveInterface = nil;
ClientGuis.ToggleChatWindow = nil;

--==
function ClientGuis.onRequire()
	playerGuiBase = game.ReplicatedStorage:WaitForChild("PlayerGui");
    local remoteGeneralUIRemote = modRemotesManager:Get("GeneralUIRemote");

	function remoteGeneralUIRemote.OnClientInvoke(action: string, ...)
		if action == "promptwarning" then
			local message = ...;
			ClientGuis.promptWarning(message);
		end
	end
end

function ClientGuis.loadGuiItem(newGuiItem)
	if newGuiItem.Archivable == false then return end;

	for _, obj in pairs(playerGui:GetChildren()) do
		if obj.Name == newGuiItem.Name then
			if obj:IsA("ScreenGui") then
				obj:Destroy();
			else
				return;
			end
		end
	end
	
	local newClone = newGuiItem:Clone();
	newClone.Parent = playerGui;

	if newClone.Name == "MainInterface" then
		local coreInterfaceName = `{shared.gameCore}Interface`;
		if playerGuiBase:FindFirstChild(coreInterfaceName) then
			Debugger:Warn(`Loading {shared.gameCore} interface..`);
			local interfaceModuleScript = playerGuiBase:WaitForChild(coreInterfaceName):Clone();
			interfaceModuleScript.Parent = newClone;

			task.defer(function()
				task.wait(0.1); -- To load other PlayerGui items later.
				local coreInterface = shared.require(interfaceModuleScript);
				ClientGuis.ActiveInterface = coreInterface;
				coreInterface.clientGuisLoad();

				newClone.Destroying:Once(function()
					Debugger:Log(`Cleaning up {newClone.Name} GUI garbage.`);
					if coreInterface.Destroy then
						coreInterface:Destroy();
					end
				end)
			end)
		end
	end
end

function ClientGuis.refreshInterfaces()
	if ClientGuis.ActiveInterface == nil then return end;
	ClientGuis.ActiveInterface:RefreshInterfaces();
end

function ClientGuis.toggleGameBlinds(...)
	if ClientGuis.ActiveInterface == nil then return end;

	ClientGuis.ActiveInterface:ToggleGameBlinds(...);
end

function ClientGuis.fireEvent(bindKey: string, ...)
	if ClientGuis.ActiveInterface == nil then return end;

	ClientGuis.ActiveInterface:FireEvent(bindKey, ...);
end

function ClientGuis.hintWarning(msg: string, func: ((element: InterfaceElement)->nil)?)
	if ClientGuis.ActiveInterface == nil then return end;

	local warningHintElement: InterfaceElement = ClientGuis.ActiveInterface:GetOrDefaultElement("WarningHint");
	if warningHintElement then
		if func then
			warningHintElement.Text = msg;
			func(warningHintElement);
			warningHintElement.Prompt();
		else
        	warningHintElement.Duration = nil;
        	warningHintElement.TextColor = nil;
       		warningHintElement.LabelPosition = nil;
			warningHintElement.Text = msg;
			warningHintElement.Prompt();
		end
	end
end

function ClientGuis.promptWarning(message: string)
	if ClientGuis.ActiveInterface == nil then return end;

	local warningPromptWindow: InterfaceWindow = ClientGuis.ActiveInterface:GetWindow("WarningPrompt");
	if warningPromptWindow then
		warningPromptWindow:Open(message);
	end
end

function ClientGuis.promptQuestion(title: string, desc: string, yesText: string, noText: string, imageId: string)
	if ClientGuis.ActiveInterface == nil then return end;

	local questionPromptWindow: InterfaceWindow = ClientGuis.ActiveInterface:GetWindow("QuestionPrompt");
	if questionPromptWindow then
		questionPromptWindow:Open(title, desc, yesText, noText, imageId);
	end

	return questionPromptWindow;
end

function ClientGuis.promptDialogBox(params)
	if ClientGuis.ActiveInterface == nil then return end;

	ClientGuis.toggleWindow("DialogBoxPrompt", true, params);
end

function ClientGuis.getWindow(name: string)
	if ClientGuis.ActiveInterface == nil then return end;

	return ClientGuis.ActiveInterface:GetWindow(name);
end

function ClientGuis.toggleWindow(name, visible, ...)
	if ClientGuis.ActiveInterface == nil then return end;

	return ClientGuis.ActiveInterface:ToggleWindow(name, visible, ...);
end

function ClientGuis.getElement(name: string)
	if ClientGuis.ActiveInterface == nil then return end;

	return ClientGuis.ActiveInterface:GetOrDefaultElement(name);
end

function ClientGuis.toggleChatWindow()
	if ClientGuis.ToggleChatWindow == nil then return end;

	ClientGuis.ToggleChatWindow();
end

function ClientGuis.setPositionWithPadding(guiObject, position, padding)
	if ClientGuis.ActiveInterface == nil then return end;

	ClientGuis.ActiveInterface.setPositionWithPadding(guiObject, position, padding);
end


local isReloading = false;
function ClientGuis.reload()
	if isReloading then return end;
	isReloading = true;

	local c = 0;
	while localPlayer:FindFirstChild("DataModule") == nil do
		c = c+1;
		Debugger:Warn(`Waiting for DataModule`, c);
		task.wait();
	end

    for _, child in pairs(playerGuiBase:GetChildren()) do
		ClientGuis.loadGuiItem(child);
	end
	isReloading = false;
end

return ClientGuis;