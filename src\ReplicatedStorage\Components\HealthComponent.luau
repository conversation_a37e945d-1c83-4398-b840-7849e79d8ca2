local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");

local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modBoolString = shared.require(game.ReplicatedStorage.Library.Util.BoolString);

local EMPTY_TABLE = table.freeze({});

local HealthComponent = {} :: Class;
HealthComponent.__index = HealthComponent;
HealthComponent.base = HealthComponent;
HealthComponent.Script = script;

HealthComponent.ClientIdCounter = 0;
--
function HealthComponent.onRequire()
	remoteHealthComponent = modRemotesManager:Get("HealthComponent");

	if RunService:IsClient() then
		remoteHealthComponent.OnClientEvent:Connect(function(action, ...)
			local playerClass: PlayerClass = shared.modPlayers.get(game.Players.LocalPlayer);
			local healthComp: HealthComp = playerClass.HealthComp;

			if action == "func" then
				local funcName, new, packet = ...;
				if healthComp[funcName] then
					healthComp[funcName](healthComp, new, packet);
				end
			end
		end)
	end
end

function HealthComponent.new(compOwner: ComponentOwner)
    local self = {
		-- @properties
		Id = nil;
        CompOwner = compOwner;
        IsDead = false;

        CurHealth = 100;
        MaxHealth = 100;
		KillHealth = 0;
		
		CurArmor = 0;
		MaxArmor = 0;

		LastDamagedBy = nil;

		FirstDamageTaken = nil;
		LastDamageTaken = workspace:GetServerTimeNow()-15;
		LastArmorDamageTaken =  workspace:GetServerTimeNow()-15;

		CanBeHurtByBoolString = nil;

		-- @signal
		OnHealthChanged = shared.EventSignal.new("HealthChanged");
		OnArmorChanged = shared.EventSignal.new("ArmorChanged");
		OnIsDeadChanged = shared.EventSignal.new("IsDeadChanged");
    };

	if RunService:IsServer() then
		-- For bound casting
		local ownerCharacter: Model = compOwner.Character;
		if ownerCharacter and ownerCharacter.PrimaryPart then
			ownerCharacter.PrimaryPart:AddTag("HealthCompRootParts");
		end

		HealthComponent.ClientIdCounter = HealthComponent.ClientIdCounter +1;
		local healthCompId = HealthComponent.ClientIdCounter;
		self.Id = healthCompId;

		if compOwner.Character then
			compOwner.Character:SetAttribute("HealthCompId", healthCompId);
		end
		if compOwner.Model then
			compOwner.Model:SetAttribute("HealthCompId", healthCompId);
		end

		local function updateIsDead()
			if compOwner.Character then
				compOwner.Character:SetAttribute("IsDead", self.IsDead);
			end
			if compOwner.Model then
				compOwner.Model:SetAttribute("IsDead", self.IsDead);
			end
		end
		self.OnIsDeadChanged:Connect(updateIsDead);
		updateIsDead();
	end

    setmetatable(self, HealthComponent);
    return self;
end

function HealthComponent.getByModel(model: Model): HealthComp?
	assert(typeof(model) == "Instance", `Invalid model type: {typeof(model)}`);
	
	if model:IsA("Accessory") then
		model = model.Parent;
	end
		
	while model:GetAttribute("EntityParent") do 
		model = model.Parent :: Model; 
	end 
	
	while model:GetAttribute("DestructibleParent") do 
		model = model.Parent :: Model; 
	end
	
	local player: Player = game.Players:GetPlayerFromCharacter(model);
	if player then
		local playerClass: PlayerClass = shared.modPlayers.get(player);
		return playerClass.HealthComp;
	end

	if RunService:IsClient() then
		local healthCompId = model:GetAttribute("HealthCompId");
		if healthCompId == nil then return end;
	end

	local npcClassInstanceModule = model:FindFirstChild("NpcClassInstance");
	if npcClassInstanceModule then
		local npcClass: NpcClass = shared.require(npcClassInstanceModule).NpcClass;

		if RunService:IsClient() and npcClass.HealthComp == nil then
			npcClass.HealthComp = HealthComponent.new(npcClass);
		end

		return npcClass.HealthComp;
	end

	local destructibleObj = model:FindFirstChild("Destructible");
	if destructibleObj then
		local destructible: DestructibleInstance;

		if destructibleObj:IsA("ModuleScript") then
			destructible = shared.require(destructibleObj);

		elseif destructibleObj:IsA("Configuration") then
			local modDestructibles = shared.require(game.ReplicatedStorage.Entity.Destructibles);
			destructible = modDestructibles.getOrNew(destructibleObj);

		end

		return destructible.HealthComp;
	end

	return;
end

function HealthComponent:Destroy()
	self.OnHealthChanged:Destroy();
	self.OnArmorChanged:Destroy();
	self.OnIsDeadChanged:Destroy();
end

function HealthComponent:GetModel()
	if self.CompOwner.ClassName == "PlayerClass" or self.CompOwner.ClassName == "NpcClass"then
		return self.CompOwner.Character;
		
	elseif self.CompOwner.ClassName == "Destructible" then
		return self.CompOwner.Model;
	end

	return nil;
end

function HealthComponent:TakeDamage(damageData: DamageData)
	Debugger:StudioLog(`Unimplemented TakeDamage. {debug.traceback()}`, damageData ~= nil);
end

function HealthComponent:CanTakeDamageFrom(attackerCharacter: CharacterClass?)
    Debugger:StudioLog(`Unimplemented CanTakeDamageFrom. {debug.traceback()}`, attackerCharacter ~= nil);
end

function HealthComponent:SetHealth(value: number, reason: anydict)
	reason = reason or EMPTY_TABLE;
	local prevCurHealth = self.CurHealth;
	self.CurHealth = math.clamp(value, -0.1, self.MaxHealth);

	if self.CurHealth < prevCurHealth then
		self.LastDamageTaken = workspace:GetServerTimeNow();
	end

	if self.CurHealth < self.KillHealth then
		self:SetIsDead(true);
	end

	if self.CurHealth ~= prevCurHealth then
		self.OnHealthChanged:Fire(self.CurHealth, prevCurHealth, reason);
		
		if RunService:IsServer() and self.CompOwner.ClassName == "PlayerClass" then
			local player = (self.CompOwner :: PlayerClass):GetInstance();
			
			local packet = {
				Damage = reason.Damage;
				DamageType = reason.DamageType;
				KillReason = reason.KillReason;
				OldValue = prevCurHealth;
			};
			remoteHealthComponent:FireClient(player, "func", "SetHealth", self.CurHealth, packet);
		end
	end
end

function HealthComponent:SetMaxHealth(value: number, reason: anydict)
	reason = reason or EMPTY_TABLE;
	local prevMaxHealth = self.MaxHealth;
	self.MaxHealth = value;

	local prevCurHealth = self.CurHealth;
	self.CurHealth = math.clamp(prevCurHealth, 0, self.MaxHealth);

	if self.MaxHealth ~= prevMaxHealth then
		self.OnHealthChanged:Fire(self.CurHealth, prevCurHealth, reason);
		
		if RunService:IsServer() and self.CompOwner.ClassName == "PlayerClass" then
			local player = (self.CompOwner :: PlayerClass):GetInstance();
			
			local packet = {
				Damage = reason.Damage;
				DamageType = reason.DamageType;
				KillReason = reason.KillReason;
				OldValue = prevMaxHealth;
			};
			remoteHealthComponent:FireClient(player, "func", "SetMaxHealth", self.MaxHealth, packet);
		end
	end
end

function HealthComponent:SetArmor(value: number, reason: anydict)
	reason = reason or EMPTY_TABLE;
	local prevCurArmor = self.CurArmor;
	self.CurArmor = math.clamp(value, 0, self.MaxArmor);
	
	if self.CurArmor < prevCurArmor then
		self.LastArmorDamageTaken = workspace:GetServerTimeNow();
	end

	if self.CurArmor ~= prevCurArmor then
		self.OnArmorChanged:Fire(self.CurArmor, prevCurArmor, reason);

		if RunService:IsServer() and self.CompOwner.ClassName == "PlayerClass" then
			local player = (self.CompOwner :: PlayerClass):GetInstance();
			
			local packet = {
				Damage = reason.Damage;
				DamageType = reason.DamageType;
				KillReason = reason.KillReason;
				OldValue = prevCurArmor;
			};
			remoteHealthComponent:FireClient(player, "func", "SetArmor", self.CurArmor, packet);
		end
	end
end

function HealthComponent:SetMaxArmor(value: number, reason: anydict)
	reason = reason or EMPTY_TABLE;
	local prevMaxArmor = self.MaxArmor;
	self.MaxArmor = value;

	local prevCurArmor = self.CurArmor;
	self.CurArmor = math.clamp(prevCurArmor, 0, self.MaxHealth);

	if self.CurArmor ~= prevCurArmor then
		self.OnArmorChanged:Fire(self.CurArmor, prevCurArmor, reason);

		if RunService:IsServer() and self.CompOwner.ClassName == "PlayerClass" then
			local player = (self.CompOwner :: PlayerClass):GetInstance();
			
			local packet = {
				Damage = reason.Damage;
				DamageType = reason.DamageType;
				KillReason = reason.KillReason;
				OldValue = prevMaxArmor;
			};
			remoteHealthComponent:FireClient(player, "func", "SetMaxArmor", self.CurArmor, packet);
		end
	end
end

function HealthComponent:SetIsDead(value: boolean, reason: anydict)
	local prevValue = self.IsDead;
	self.IsDead = value;

	if self.IsDead ~= prevValue then
		self.OnIsDeadChanged:Fire(self.IsDead, prevValue, reason);

		if RunService:IsServer() and self.CompOwner.ClassName == "PlayerClass" then
			local player = (self.CompOwner :: PlayerClass):GetInstance();
			
			reason = reason or {};
			local packet = {
				Damage = reason.Damage;
				DamageType = reason.DamageType;
				KillReason = reason.KillReason;
				OldValue = prevValue;
			};
			remoteHealthComponent:FireClient(player, "func", "SetIsDead", self.IsDead, packet);
		end
	end
end

function HealthComponent:Reset()
	self.LastDamagedBy = nil;

	self.FirstDamageTaken = nil;
	self.LastDamageTaken = workspace:GetServerTimeNow()-15;
	self.LastArmorDamageTaken =  workspace:GetServerTimeNow()-15;

	self:SetHealth(self.MaxHealth);
	self:SetArmor(self.MaxArmor);
	self:SetIsDead(false);
end

function HealthComponent:SetCanBeHurtBy(boolString: string)
	local prevBoolString = self.CanBeHurtByBoolString;
	if boolString == prevBoolString then return end;

	self.CanBeHurtByBoolString = boolString;
	self._canBeHurtByTestFunc = modBoolString.newTestFunction(self.CanBeHurtByBoolString);
end


function HealthComponent:CheckCanBeHurtBy(value: {string})
	if self._canBeHurtByTestFunc == nil then return true end;
	return self._canBeHurtByTestFunc(value);
end

return HealthComponent;