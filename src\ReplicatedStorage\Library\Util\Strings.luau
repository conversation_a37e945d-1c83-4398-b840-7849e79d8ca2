--!strict
local Strings = {};

function Strings.MatchStringFromDict(str: string, dict: {[string]: any}): {string}
	str = tostring(str);
	local matches: {string} = {};

	for key: string, _ in pairs(dict) do
        if typeof(key) ~= "string" then continue end;
		if key:lower():find(str:lower()) then
			table.insert(matches, key);
		end
		if str:lower() == key:lower() then
			matches = {str};
			break;
		end
	end

	return matches;
end


local function concat(c: {[any]: any}, useKey: boolean?, level: number?): string
	level = level or 0;
    assert(typeof(level) == "number");
	if level > 4 then return tostring(c) end;
	
	local d="";
	local index = 1;
	for i, b in pairs(c) do
		local bName;
		pcall(function() 
			if typeof(b) == "Instance" then
				bName = b.Name; 
			end
		end)
		d=d..(index==1 and "" or " ")..(useKey and (
				typeof(i) == "string" and '"'..i..'"' or tostring(i))..":" or "")..(
				typeof(b)=="table" and "{"..concat(b, true, level+1).."}" or 
				typeof(b)=="boolean" and tostring(b) or 
				typeof(b)=="userdata" and ((bName or typeof(b)).." "..tostring(b)) or 
			tostring(b or "nil"))
		index = index +1;
	end;
	return d;
end

function Strings.TableToString(...)
	local a = "";
	if #{...} <= 0 then
		a=a.."nil"
	else
		a=a..concat({...});
	end;
	return a;
end

function Strings.EscapeRegex(str: string)
	return str:gsub("[%(%)%.%%%+%-%*%?%[%^%$%]]", "%%%1");
end

function Strings.Replace(str: string, this: string, that: string)
	local mStr = Strings.EscapeRegex(this);
	local rStr = that:gsub("%%", "%%%%");
	local result = str:gsub(mStr, rStr);
	return result;
end


return Strings;