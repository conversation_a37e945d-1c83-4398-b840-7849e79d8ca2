local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");
local TextChatService = game:GetService("TextChatService");

local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modBranchConfigs = shared.require(game.ReplicatedStorage.Library.BranchConfigurations);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);

local Component = {};
--==
function Component.onRequire()
	remoteNpcComponent = modRemotesManager:Get("NpcComponent");
end

function Component.new(npcClass: NpcClass)
	--TextChatService:DisplayBubble(partOrCharacter, message)
	local properties = npcClass.Properties;

	local audioEmitter = Instance.new("AudioEmitter");
	audioEmitter.Parent = npcClass.Head;
	audioEmitter:SetDistanceAttenuation({
		[0] = 1;
		[10] = 1;
		[20] = 0.9;
		[30] = 0.6;
		[40] = 0.3;
		[50] = 0.1;
		[60] = 0;
	});

	local audioTextToSpeech = Instance.new("AudioTextToSpeech");
	audioTextToSpeech.Volume = 5;
	audioTextToSpeech.Parent = npcClass.Head;

	local audioWire = Instance.new("Wire");
	audioWire.SourceInstance = audioTextToSpeech;
	audioWire.TargetInstance = audioEmitter;
	audioWire.Parent = npcClass.Head;

	local voicePreset = npcClass.NpcPackage.Voice;
	if voicePreset then
		audioTextToSpeech.VoiceId = voicePreset.VoiceId or 1;
		audioTextToSpeech.Pitch = voicePreset.Pitch or 1;
		audioTextToSpeech.Speed = voicePreset.Speed or 1;
		audioTextToSpeech.PlaybackSpeed = voicePreset.PlaybackSpeed or 1;
	end

	task.spawn(function()
		task.wait(math.random(5, 30));
		repeat
			if npcClass.HealthComp.IsDead then break; end;

			local speeches = npcClass.NpcPackage.IdleRandomChat;
			if speeches and #speeches > 0 then
				npcClass.Chat(
					game.Players:GetPlayers(), 
					speeches[math.random(1, #speeches)]
				);
			end
		until not task.wait(math.random(32,350));
	end)

	local lastPlayerLook = {};
	npcClass.Garbage:Tag(lastPlayerLook);
	npcClass.Garbage:Tag(remoteNpcComponent.OnServerEvent:Connect(function(player, action, ...)
		if npcClass.Player and player ~= npcClass.Player then return end;
		if action ~= "headtrack" then return end;

		local characterModel = ...;
		if characterModel ~= npcClass.Character then return end;

		lastPlayerLook[player] = tick();

		if properties.ProximityChatMessage == nil then return end;
		local msg = properties.ProximityChatMessage;
		properties.ProximityChatMessage = nil;

		npcClass.Chat(npcClass.Player or game.Players:GetPlayers(), msg);
	end));

	properties.OnChanged:Connect(function(k, v)
		if k ~= "ProximityChatMessage" then return end;
		if properties.ProximityChatMessage == nil then return end;

		local curTick = tick();
		for player, lastLookTick in pairs(lastPlayerLook) do
			if curTick-lastLookTick <= 30 then continue end;
			lastPlayerLook[player] = nil;
		end
		
		if next(lastPlayerLook) then
			local msg = properties.ProximityChatMessage;
			properties.ProximityChatMessage = nil;

			npcClass.Chat(npcClass.Player or game.Players:GetPlayers(), msg);
		end
	end)

	return function(target, message, chatColor, maxRange)
		maxRange = maxRange or 60;
		target = target or game.Players:GetPlayers();
		
		message = message and tostring(message) or nil;
		message = message or "";

		if audioTextToSpeech.IsPlaying then
			audioTextToSpeech:Pause();
		end
		
		if not npcClass.Head:IsDescendantOf(workspace) then return end;
		if #message > 0 then
			if #message <= 290 then
				local ttsText = message:gsub("%b**% ?", "");
				audioTextToSpeech.Text = ttsText;
				audioTextToSpeech:Play();
			end

			local targets: {Player} = {};
			if type(target) == "table" then
				for a=1, #target do
					table.insert(targets, target[a]);
				end
			else
				table.insert(targets, target);
			end
			
			if modBranchConfigs.WorldInfo.Type ~= modBranchConfigs.WorldTypes.Cutscene then
				for a=#targets, 1, -1 do
					if targets[a]:IsA("Player") and targets[a]:DistanceFromCharacter(npcClass.Head.Position) > maxRange then
						table.remove(targets, a);
					end
				end
			end
			
			if npcClass.Head then
				for a=1, #targets do
					remoteNpcComponent:FireClient(targets[a], "chat", npcClass.Head, message);
				end
			end

			local modNpcProfileLibrary = shared.require(game.ReplicatedStorage.Library.NpcProfileLibrary);
			local npcLib = modNpcProfileLibrary:Find(npcClass.Name);
			if npcLib and modNpcProfileLibrary.ClassColors[npcLib.Class] then
				local nc = modNpcProfileLibrary.ClassColors[npcLib.Class];
				local classColor = "rgb("..math.floor(nc.R*255)..","..math.floor(nc.G*255)..","..math.floor(nc.B*255)..")";
				shared.Notify(targets, '<b><font size="16" color="'..classColor..'">'..npcClass.Name..'</font></b>: '..message, "Message");
			else
				shared.Notify(targets, '<b><font size="16" color="'.. (chatColor or '#ddcbb2') ..'">'..npcClass.Name..'</font></b>: '..message, "Message");
			end
		end
	end
end

return Component;