local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");

local localPlayer = game.Players.LocalPlayer;

local modTables = shared.require(game.ReplicatedStorage.Library.Util.Tables);

local interactablePackage = {};
--==

function interactablePackage.init(super) -- Server/Client
    local ButtonInteractable = {
        Name = "Button"; 
        Type = "Button";

        Label = "Button";

        EventServiceKey = "Interactables_BindButtonInteract";
    };

    function ButtonInteractable.new(interactable: InteractableInstance)
        interactable.CanInteract = true;
        interactable.Label = "Press Button";

        interactable.Values.IsButton = true;
	    interactable:SetPermissions("CanInteract", true);
    end

    -- When interacting with interactable.
    function ButtonInteractable.BindInteract(interactable: InteractableInstance, info: InteractInfo)
        if info.Action == "Client" then 
            if info.Player == nil then return end;

            local event: EventPacket = shared.modEventService:ClientInvoke(
                ButtonInteractable.EventServiceKey, 
                {SendBy = localPlayer}, 
                interactable
            );
            if event.Cancelled then return end;

            interactable:InteractServer(info.Values);
            
            return;
        end 
        if RunService:IsClient() then return end;

        local event: EventPacket = shared.modEventService:ServerInvoke(
            ButtonInteractable.EventServiceKey, 
            {ReplicateTo={info.Player}}, 
            interactable
        );
        if event.Cancelled then return end;

        if interactable.Id ~= nil then
            local triggerId = interactable.Id;
            shared.modEventService:ServerInvoke(
                "Generic_BindTrigger", 
                {ReplicateTo={info.Player}}, 
                triggerId, 
                interactable
            );
        end

        if info.Action == "Server" then
            interactable:Sync({info.Player});
        end;
    end
    
    -- When interactable pops up on screen.
    function ButtonInteractable.BindPrompt(interactable: InteractableInstance, info: InteractInfo)

    end

    super.registerPackage(ButtonInteractable);
end

return interactablePackage;
