while shared.ReviveEngineLoaded ~= true do task.wait() end;

local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
shared.GameCache = {};

--== Variables;
local RunService = game:GetService("RunService");
local Players = game:GetService("Players");
local MemoryStoreService = game:GetService("MemoryStoreService");
local ScriptContext = game:GetService("ScriptContext");

--== Cores;
require(game.ReplicatedStorage:WaitForChild("shared"));
local modEngineCore = require(game.ReplicatedStorage.EngineCore);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modCommandsLibrary = shared.require(game.ReplicatedStorage.Library.CommandsLibrary);

local modDataModelCore = shared.require(game.ReplicatedStorage:WaitForChild(`{shared.gameCore}Core`));

--==
local modGlobalVars = shared.require(game.ReplicatedStorage:WaitForChild("GlobalVariables"));
local modAssetHandler = shared.require(game.ReplicatedStorage.Library.AssetHandler);
local modEventService = shared.require(game.ReplicatedStorage.Library.EventService);
local modPlayers = shared.require(game.ReplicatedStorage.Library.Players);
local modScheduler = shared.require(game.ReplicatedStorage.Library.Scheduler); 
local modItemsLibrary = shared.require(game.ReplicatedStorage.Library.ItemsLibrary);
local modSyncTime = shared.require(game.ReplicatedStorage.Library.SyncTime);
local modBranchConfigs = shared.require(game.ReplicatedStorage.Library.BranchConfigurations);
local modConfigurations = shared.require(game.ReplicatedStorage.Library.Configurations);
local modCommandHandler = shared.require(game.ReplicatedStorage.Library.CommandHandler);
local modInteractables = shared.require(game.ReplicatedStorage.Library.Interactables);
local modDestructibles = shared.require(game.ReplicatedStorage.Entity.Destructibles);
local modCollectiblesLibrary = shared.require(game.ReplicatedStorage.Library.CollectiblesLibrary);
local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modUsableItems = shared.require(game.ReplicatedStorage.Library.UsableItems);
local modCutscene = shared.require(game.ReplicatedStorage.Library.Cutscene);
local modRewardsLibrary = shared.require(game.ReplicatedStorage.Library.RewardsLibrary);
local modWorkbenchLibrary = shared.require(game.ReplicatedStorage.Library.WorkbenchLibrary);
local modMissionLibrary = shared.require(game.ReplicatedStorage.Library.MissionLibrary);
local modGpsLibrary = shared.require(game.ReplicatedStorage.Library.GpsLibrary);
local modLeaderboardService = shared.require(game.ReplicatedStorage.Library.LeaderboardService);
local modGoldShopLibrary = shared.require(game.ReplicatedStorage.Library.GoldShopLibrary);
local modCharacterInteractions = shared.require(game.ReplicatedStorage.Library.CharacterInteractions);
local modTools = shared.require(game.ReplicatedStorage.Library.ToolsLibrary);
local modOnEventHandlers = shared.require(game.ReplicatedStorage.Library.OnEventHandlers);
local modTeamsManager = shared.require(game.ReplicatedStorage.Library.TeamsManager);
local modMasterMind = shared.require(game.ReplicatedStorage.Library.Minigames.MasterMind);

local modUniversalBind = shared.require(game.ServerScriptService.ServerLibrary.UniversalEventBind);
local modProfile = shared.require(game.ServerScriptService.ServerLibrary.Profile);
local modStorage = shared.require(game.ServerScriptService.ServerLibrary.Storage);
local modMission = shared.require(game.ServerScriptService.ServerLibrary.Mission);
local modEquipmentSystem = shared.require(game.ServerScriptService.ServerLibrary.EquipmentSystem);
local modMailObject = shared.require(game.ServerScriptService.ServerLibrary.MailObject);
local modPhysics = shared.require(game.ServerScriptService.ServerLibrary.Physics);
local modWorldEvents = shared.require(game.ServerScriptService.ServerLibrary.WorldEventSystem);
local modDialogues = shared.require(game.ServerScriptService.ServerLibrary.DialogueSave);
local modNpcs = shared.require(game.ServerScriptService.ServerLibrary.Entity.Npcs);
local modFactions = shared.require(game.ServerScriptService.ServerLibrary.Factions);
local modServerManager = shared.require(game.ServerScriptService.ServerLibrary.ServerManager);
local modTradingService = shared.require(game.ServerScriptService.ServerLibrary.TradingService);
local modEvents = shared.require(game.ServerScriptService.ServerLibrary.Events);
modPlayers.SkillTree = shared.require(game.ServerScriptService.ServerLibrary.SkillTree);
local modAnalytics = shared.require(game.ServerScriptService.ServerLibrary.GameAnalytics);
local modCharacterAppearance = shared.require(game.ServerScriptService.ServerLibrary.CharacterAppearance);
local modEconomyAnalytics = shared.require(game.ServerScriptService.ServerLibrary.EconomyAnalytics);
local modAnalyticsService = shared.require(game.ServerScriptService.ServerLibrary.AnalyticsService);
local modItemDrops = shared.require(game.ServerScriptService.ServerLibrary.ItemDrops);

local modOnGameEvents = shared.require(game.ServerScriptService.ServerLibrary.OnGameEvents);
local modModerationSystem = shared.require(game.ServerScriptService.ServerLibrary.ModerationSystem);

shared.igniteEngine();
shared.waitForIgnition();

task.spawn(modEngineCore.loadWorldCore, modBranchConfigs.WorldName);
task.delay(1, modEngineCore.connectPlayers);
--==
modScheduler:GetGlobal();

local remotes = game.ReplicatedStorage.Remotes;

local remotePickUpRequest = remotes.Interactable.PickUpRequest;
local remoteWorldTravelRequest = remotes.Interactable.WorldTravelRequest;
local remoteOpenStorageRequest = remotes.Interactable.OpenStorageRequest;

local remotePlayerDataSync = modRemotesManager:Get("PlayerDataSync") :: RemoteEvent;
local remotePlayerDataFetch = modRemotesManager:Get("PlayerDataFetch");

local remoteUpgradeStorage = modRemotesManager:Get("UpgradeStorage");
local remoteOnTrigger = remotes.Interactable.OnTrigger;
local remoteInteractableToggle = modRemotesManager:Get("InteractableToggle");
local remoteLockHydra = modRemotesManager:Get("LockHydra");
--== Remotes;
local remoteMailboxFunction = remotes.Interface.MailboxFunction;
local remoteRequestResetData = modRemotesManager:Get("RequestResetData");
local remoteFastTravel = modRemotesManager:Get("FastTravel");
local remoteDuelRequest = modRemotesManager:Get("DuelRequest");
local remotePlayerSearch = modRemotesManager:Get("PlayerSearch");
local remoteGoldDonate = modRemotesManager:Get("GoldDonate");
local remoteGeneralUIRemote = modRemotesManager:Get("GeneralUIRemote");

--== Script;
workspace:SetAttribute("Version", modGlobalVars.GameVersion.."."..modGlobalVars.GameBuild .. " (".. modBranchConfigs.CurrentBranch.Name ..")");

if shared.gameCore == "Rotd" then
	local oldRotdCore = shared.require(script:WaitForChild("RiseOfTheDead"));
	modDataModelCore.OnPlayerAdded = oldRotdCore.OnPlayerAdded;
	modDataModelCore.OnCharacterAdded = oldRotdCore.OnCharacterAdded;
	modDataModelCore.OnPlayerRemoving = oldRotdCore.OnPlayerRemoving;
end

function shared.ReloadCharacter(player)
	local character = player.Character;
	
	for _, s in pairs(game.ReplicatedStorage.CharacterScripts:GetChildren()) do
		local existingScript = character:FindFirstChild(s.Name);
		if existingScript then 
			if existingScript:GetAttribute("CantReload") == true then
				continue;
			else
				existingScript.Enabled = false;
				existingScript:Destroy()
			end
		end;

		local new = s:Clone();
		new.Parent = character;
		new.Archivable = false;

		if new.ClassName == "LocalScript" then new.Disabled = false; end
	end
end

function OnPlayerAdded(player)
	local dataModule = script:WaitForChild("DataModule"):Clone();
	dataModule.Parent = player;
	
	if modDataModelCore.OnPlayerAdded then
		task.spawn(function() modDataModelCore.OnPlayerAdded(player); end);
	end
	
	local firstLoad = false;
	local function onCharacterAdded(character)
		firstLoad = true;
		for _, obj in pairs(player.PlayerGui:GetChildren()) do
			if obj:GetAttribute("OnPlayerAdded") then
				obj:Destroy();
			end
		end

		if modDataModelCore.OnCharacterAdded then
			task.spawn(modDataModelCore.OnCharacterAdded, player, character);
		end

		shared.ReloadCharacter(player);

		modAnalytics:SyncRemoteConfigs(player);
	end
	
	player.CharacterAdded:Connect(onCharacterAdded);
	
	-- Load OnPlayerAdded
	modAnalytics:SyncRemoteConfigs(player);
	
	
	if player.Character and not firstLoad then
		onCharacterAdded(player.Character);
	end
	
	task.spawn(function()
		local followedPlayer = game.Players:GetPlayerByUserId(player.FollowUserId);
		local referrer: Player;
		if player.FollowUserId ~= 0 and followedPlayer then
			referrer = followedPlayer;
		else
			local oldestFirstJoin = os.time();
			for _, otherPlayer: Player in pairs(game.Players:GetPlayers()) do
				if otherPlayer == player then continue end;

				local isFriend = false;
				pcall(function()
					isFriend = otherPlayer:IsFriendsWith(player.UserId);
				end)
				if not isFriend then continue end;

				local otherProfile = modProfile:Get(otherPlayer);
				if otherProfile.FirstJoined < oldestFirstJoin then
					oldestFirstJoin = otherProfile.FirstJoined;
					referrer = otherPlayer;
				end
			end
		end

		if referrer == nil then
			return;
		end

		local referrerProfile = modProfile:Get(referrer);
		if #referrerProfile.ReferralList < 8 and table.find(referrerProfile.ReferralList, player.UserId) == nil then
			table.insert(referrerProfile.ReferralList, player.UserId);
		end

		local profile = modProfile:Get(player);
		if table.find(profile.ReferralList, referrer.UserId) == nil then
			table.insert(profile.ReferralList, referrer.UserId);
		end
	end)
end

shared.modEngineCore:ConnectOnPlayerAdded(script, OnPlayerAdded, 2);

Players.PlayerRemoving:Connect(function(player)
	--== Connections
	task.spawn(function() modRemotesManager.OnPlayerRemoving(player); end);
	if modDataModelCore.OnPlayerRemoving then
		task.spawn(function() modDataModelCore.OnPlayerRemoving(player); end);
	end
	
	local profile = modProfile:Get(player);
	profile:Save(); -- Disconnect save
	
	local playerSave = profile and profile:GetActiveSave();
	if playerSave then playerSave.Statistics:Save(); end
	
	pcall(function()
		if profile and profile.Junk and profile.Junk.CacheInstances then
			for a=1, #profile.Junk.CacheInstances do
				if profile.Junk.CacheInstances[a] then
					profile.Junk.CacheInstances[a]:Destroy();
				end
			end
		end
	end)
	
	profile:Unload();
	task.spawn(function()
		local onlineSetS, onlineSetE = pcall(function()
			local lastOnlineData = MemoryStoreService:GetHashMap("LastOnline");
			lastOnlineData:RemoveAsync(tostring(player.UserId));

			local accessCodeData = MemoryStoreService:GetHashMap("AccessCode");
			accessCodeData:RemoveAsync(tostring(player.UserId));
		end)
		if not onlineSetS then
			Debugger:Warn("PlayerRemoving>>  ",onlineSetE);
		end
	end)
end)


function remotePlayerDataFetch.OnServerInvoke(player, packet) 
	local profile = modProfile:Find(player.Name);
	if profile == nil then return end;
	
	local action = packet[modRemotesManager.Ref("Action")];
	
	if action == "flagfetch" then
		local flagId = packet[modRemotesManager.Ref("Id")];
		local flagData, flagIndex = profile.Flags:Get(flagId);

		if flagData then
			return {
				[modRemotesManager.Ref("Index")]=flagIndex;
				[modRemotesManager.Ref("Data")]=flagData;
			};
		end
		
	elseif action == "eventfetch" then
		local eventId = packet[modRemotesManager.Ref("Id")];
		local eventData, eventIndex = modEvents:GetEvent(player, eventId)

		if eventData then
			return {
				[modRemotesManager.Ref("Index")]=eventIndex;
				[modRemotesManager.Ref("Data")]=eventData;
			};
		end
		
	elseif action == "npcdatafetch" then
		local npcName = packet[modRemotesManager.Ref("Id")];

		local safehomeData = profile.Safehome;
		local npcData = safehomeData:GetNpc(npcName);
		if npcData == nil then return end;


		local npcStorage = npcData.GetStorage();
		if npcStorage then
			npcStorage:Loop(function(storageItem)
				local attachmentStorage = modStorage.Get(storageItem.ID, player);
				if attachmentStorage then
					attachmentStorage:Sync();
				end
			end)
			npcStorage:Sync();
		end

		profile:Sync("NpcTaskData/Npc/"..npcName);

		return {
			[modRemotesManager.Ref("Data")]={
				NpcData=npcData;
				NpcTasks=profile.NpcTaskData:GetTasks(npcName);
			};
		};
	end

	return;
end

remotePlayerDataSync.OnServerEvent:Connect(function(player, packet)
	local profile = modProfile:Find(player.Name);
	if profile == nil then return end;
	
	local action = packet[modRemotesManager.Ref("Action")];
	local data = packet[modRemotesManager.Ref("Data")];
	
	Debugger:Log("DataSync request action", action, "data size", modRemotesManager.PacketSizeCounter.GetDataByteSize(data));

	if action == "request" or action == "requestfull" then
		if profile.FirstSync == nil or action == "requestfull" then
			profile.FirstSync = true;
			profile:Sync();

			if profile.GameSave then
				profile.GameSave.FirstSync = true;
			end
			Debugger:Warn("Full sync ("..player.Name..")");
		end
		
		local hierarchyKey = packet[modRemotesManager.Ref("HierarchyKey")];
		if hierarchyKey then
			profile:Sync(hierarchyKey);
			
		end
		
	elseif action == "savesettings" then
		if data == nil or type(data) ~= "table" then return end;

		profile:RefreshSettings(data);
		Debugger:Log("Player (",player.Name,") Saving settings.");
	end
end)

function PickUpRequest(player: Player, interactObject, interactModule)
	if interactObject == nil or interactModule == nil then return "Invalid interact object."; end
	
	local interactData = shared.saferequire(player, interactModule);
	if interactData == nil then shared.Notify(player, "Invalid interact object.", "Negative"); return "Invalid interact object." end;
	if not interactData:CheckDebounce(player.Name, 1) then return "Interactable is on cooldown." end;
	
	local profile = modProfile:Get(player);
	local playerSave = profile and profile:GetActiveSave();
	local playerLevel = playerSave and playerSave:GetStat("Level") or 0;
		
	if interactData.LevelRequired and playerLevel < interactData.LevelRequired then return "Insufficient Mastery Level." end;
	
	interactData.Object = interactObject;
	interactData.Script = interactModule;
	
	if interactData.Type == modInteractables.Types.Pickup then
		if interactData.StorageItem then
			interactData.SharedDrop = false;
		end
		
		if interactData.SharedDrop == false and interactData.Taken then
			local takenString = tostring(interactData.Taken).." already picked it up!";
			shared.Notify(player, takenString, "Negative");
			return takenString;
		end;
		
		if interactData.Whitelist and interactData.Whitelist[player.Name] == nil then 
			shared.Notify(player, "Interact object is locked.", "Negative");
			return "Interact object locked."; 
		end
		local itemId = interactData.ItemId;
		
		
		if itemId == "Money" then
			playerSave:AddStat(itemId, interactData.Quantity or 1);
			shared.Notify(player, "You recieved $"..interactData.Quantity..".", "Reward");
			
		elseif modConfigurations.StorageInsertRequest == true then
			local storage = profile.ActiveInventory;
			
			interactData.Taken = player;

			local itemLibrary = modItemsLibrary:Find(itemId);
			local rPacket = storage:InsertRequest(interactData.StorageItem or {ItemId=itemId; Quantity=interactData.Quantity;});
			if rPacket.Success then

				local quantityRemaining = rPacket.QuantityRemaining or 0;
				if quantityRemaining > 0 then
					interactData.Taken = nil;
					interactData:SetQuantity(quantityRemaining);
					interactData:Sync();
					
				else
					game.Debris:AddItem(interactModule.Parent, 0);
					
				end
				
				local pickedUpQuantity = interactData.Quantity-quantityRemaining;
				shared.Notify(player, (pickedUpQuantity > 1 and itemLibrary.Name.." (".. pickedUpQuantity..")" or itemLibrary.Name), "PickUp")
				
				if interactData.OnPickUp then interactData.OnPickUp(player) end;
				
			else
				game.Debris:AddItem(interactModule.Parent, 0);
				return "Failed id: "..tostring(rPacket.Failed);
			end
		else
			
			local storage = profile.ActiveInventory;
			
			if interactData.TargetStorage then
				storage = playerSave.Storages[interactData.TargetStorage];
			end
			
			if storage == nil then return ("Missing storage: ".. interactData.TargetStorage); end;
			local itemLibrary = modItemsLibrary:Find(itemId);
			
			if itemLibrary == nil then
				Debugger:Warn("Missing itemlib ", itemId);
			end
			
			if interactData.Players == nil then interactData.Players = {} end;
			
			if interactData.CollectibleId then
				task.spawn(function()
					local lib = modCollectiblesLibrary:Find(interactData.CollectibleId);
					if lib == nil then return "Unknown collectible." end;
					
					profile:UnlockCollectible(interactData.CollectibleId);
					return;
				end)
			end

			if interactData.StorageItem then -- Unique item;
				if interactData.Taken == nil then
					local storageItem = interactData.StorageItem;
					local emptyIndex = storage:FindEmpty();
					--local hasSpace = activeInventory:SpaceCheck{
					--	ItemId=storageItem.ItemId;
					--	Data={Quantity=storageItem.Quantity;};
					--};
					
					if emptyIndex then
						interactData.Taken = player;
						if interactData.SharedDrop == false then
							game.Debris:AddItem(interactModule.Parent, 0);
						end
						
						storage:Insert(storageItem, emptyIndex);
						shared.Notify(player, (storageItem.Quantity > 1 and itemLibrary.Name.." ("..storageItem.Quantity..")" or itemLibrary.Name), "PickUp");
						
						if interactData.OnPickUp then interactData.OnPickUp(player) end;
						shared.modEventService:ServerInvoke("Generic_BindItemPickup", {ReplicateTo={player}}, interactData, storageItem);
						
					else
						shared.Notify(player, storage.Id.." Storage full!", "Negative");
						return "Inventory full.";
					end
				end
				
			else
				
				local hasSpace = storage:SpaceCheck{{ItemId=interactData.ItemId; Data={Quantity=(interactData.Quantity or 1)}; }};
				if hasSpace then
					if interactData.Players[player.Name] then return "Already picked up item." end;
					interactData.Players[player.Name] = true;
					if interactData.OnPickUp then interactData.OnPickUp(player) end;
					
					local addPacket = {Quantity=interactData.Quantity;};
					
					if interactData.ItemValues then
						addPacket.Values = interactData.ItemValues;
					end
					
					storage:Add(interactData.ItemId, addPacket, function(queueEvent, storageItem)
						shared.modEventService:ServerInvoke("Generic_BindItemPickup", {ReplicateTo={player}}, interactData, storageItem);
						
						if playerSave.Statistics then
							playerSave.Statistics:AddStat("PickUp", interactData.ItemId, (interactData.Quantity or 1));
						end
						
						local rewardsList = modRewardsLibrary:Find(interactData.ItemId);
						if rewardsList and rewardsList.Level then
							profile:AddPlayPoints(120, "Source:Reward");
						else
							profile:AddPlayPoints(2, "Source:Reward");
						end
						
						modStorage.OnItemSourced:Fire(nil, storageItem, storageItem.Quantity);
					end);
					shared.Notify(player, interactData.Quantity > 1 and itemLibrary.Name.." ("..interactData.Quantity..")" or itemLibrary.Name, "PickUp");
					
					interactData.Taken = player;
					if interactData.SharedDrop == false then
						game.Debris:AddItem(interactModule.Parent, 0);
					end
					
					
				else
					local fitList, quantityRemaining = storage:FitStackableItem({ItemId=interactData.ItemId; Data={Quantity=(interactData.Quantity or 1)};});
					--Debugger:Log("fitList", fitList, quantityRemaining);
					
					for a=1, #fitList do
						local fitItem = fitList[a];
						
						storage:Add(fitItem.ItemId, {Quantity=fitItem.Quantity;}, function(queueEvent, storageItem)
							shared.modEventService:ServerInvoke("Generic_BindItemPickup", {ReplicateTo={player}}, interactData, storageItem);
							modStorage.OnItemSourced:Fire(nil, storageItem,  storageItem.Quantity);
						end);
						
						shared.Notify(player, interactData.Quantity > 1 and itemLibrary.Name.." ("..fitItem.Quantity..")" or itemLibrary.Name, "PickUp");

					end
					
					if quantityRemaining > 0 then
						interactData:SetQuantity(quantityRemaining);
						interactData:Sync();
						
					end
					
					if #fitList > 0 then
						shared.Notify(player, storage.Id.." Storage full!", "Negative");
						return "Inventory full after partial pickup.", true;
						
					else
						shared.Notify(player, storage.Id.." Storage full!", "Negative");
						return "Inventory full.";
					end
				end
			end
		end
		
	elseif interactData.Type == modInteractables.Types.Collectible then
		local lib = modCollectiblesLibrary:Find(interactData.Id);
		if lib == nil then return "Unknown collectible." end;
		
		shared.modEventService:ServerInvoke("Generic_BindItemPickup", {ReplicateTo={player}}, interactData);
		profile:UnlockCollectible(interactData.Id);
	end
	
	return true;
end


function remotePickUpRequest.OnServerInvoke(player, interactObject, interactModule)
	if interactObject == nil then shared.Notify(player, "Interact object does not exist.", "Negative"); return "Interact object does not exist."; end;
	if not interactObject:IsDescendantOf(workspace) and not interactObject:IsDescendantOf(game.ReplicatedStorage:WaitForChild("Replicated")) then return "Interact object is illegitimate."; end;
	if player:DistanceFromCharacter(interactObject.Position) > 20 then return "Too far from object."; end;
	
	return PickUpRequest(player, interactObject, interactModule);
end;
modProfile.PickUpRequest = PickUpRequest;


local modLeaderboardService = shared.require(game.ReplicatedStorage.Library.LeaderboardService);
function remoteGoldDonate.OnServerInvoke(player, id)
	if remoteGoldDonate:Debounce(player) then return 5; end;

	local profile = modProfile:Get(player);
	local activeSave = profile and profile:GetActiveSave();
	local traderProfile = profile and profile.Trader;
	
	local playerGold = traderProfile.Gold or 0;
	local productInfo = modGoldShopLibrary.Products:Find(id);
	
	if productInfo == nil then return 3 end;
	
	local donateAmount = productInfo.Product.Gold;
	if playerGold >= donateAmount then
		
		traderProfile:AddGold(-donateAmount);
		modAnalytics.RecordResource(player.UserId, donateAmount, "Sink", "Gold", "Usage", "donate");
		modAnalyticsService:Sink{
			Player=player;
			Currency=modAnalyticsService.Currency.Gold;
			Amount=donateAmount;
			EndBalance=traderProfile.Gold;
			ItemSKU=`Donation`;
		};

		profile.DailyStats.GoldDonor = (profile.DailyStats.GoldDonor or 0) + donateAmount;
		profile.WeeklyStats.GoldDonor = (profile.WeeklyStats.GoldDonor or 0) + donateAmount;
		profile.MonthlyStats.GoldDonor = (profile.MonthlyStats.GoldDonor or 0) + donateAmount;
		profile.SeasonlyStats.GoldDonor = (profile.SeasonlyStats.GoldDonor or 0) + donateAmount;
		profile.YearlyStats.GoldDonor = (profile.YearlyStats.GoldDonor or 0) + donateAmount;
		profile.AllTimeStats.GoldDonor = (profile.AllTimeStats.GoldDonor or 0) + donateAmount;
		
		modLeaderboardService.Update();
		
		shared.Notify(player, "Successfully donated to the development of the game!", "Reward");
		profile:AddPlayPoints(donateAmount, "Sink:Gold");
		return 0;
	end
	
	shared.Notify(player, "Not enough Gold!", "Negative");
	return 1;
end


local fastTravelQuotes = {
	"Next stop, $Location!";
	"It's good doing business with you, $PlayerName.";
	"Buckle up, $PlayerName.";
	"This is going to be a crazy ride, $PlayerName";
}

function remotePlayerSearch.OnServerInvoke(player, searchName)
	if remotePlayerSearch:Debounce(player) then return end;
	
	local profile = modProfile:Get(player);
	if profile.PlayerSearchLimit == nil then
		profile.PlayerSearchLimit = 3;
		task.delay(60, function()
			profile.PlayerSearchLimit = nil;
		end)
	end
	
	if profile.PlayerSearchLimit > 0 then
		profile.PlayerSearchLimit = profile.PlayerSearchLimit -1;
		shared.Notify(player, "Searching for player "..searchName.."..", "Inform");
		
		local targetPlaceId, _, packet = modServerManager:RequestPlayerServer(player, searchName);
		if targetPlaceId == nil then
			shared.Notify(player, "Could not find player "..searchName.." in-game..", "Negative");
			
		else
			shared.Notify(player, searchName.." found!", "Inform");			
			return {
				UserName=searchName;
				VisitorId=packet.UserId;
				PlaceId=targetPlaceId;
			}
		end
	else
		shared.Notify(player, "Please wait one minute before retrying..", "Negative");
	end
end


local travelDebounce = {};
function remoteWorldTravelRequest.OnServerInvoke(player, travelType, data)
	if travelDebounce[player.Name] and tick()-travelDebounce[player.Name] < 30 then return end;
	travelDebounce[player.Name] = tick();
	
	local profile = modProfile:Get(player);
	
	local success = false;
	if travelType == "Social" then
		local targetName = data;
		local playerSave = profile and profile:GetActiveSave();
		local beginningMission = playerSave and playerSave.Missions:Get(1);
		
		if beginningMission and beginningMission.Type == 3 then
			success = modServerManager:TravelToPlayer(player, targetName);
		else
			shared.Notify(player, "You can't join a friend yet. Start campaign first.", "Negative");
		end
		
	elseif travelType == "TravelRequest" then
		local targetName = data;
		modServerManager:SendTravelRequest(player, targetName);
		
	elseif travelType == "AcceptTravel" then
		local targetName = data;
		modServerManager:AcceptTravelRequest(player, targetName);
		
	elseif travelType == "Interact" then
		local src = data;
		local part = src and src.Parent;
		
		if part == nil or player:DistanceFromCharacter(part:IsA("Model") and part:GetPrimaryPartCFrame().Position or part.Position) > 17 then
			return "Too far from object.";
		end;
		
		local interactData = shared.saferequire(player, src);
		if interactData then
			local profile = modProfile:Get(player);
			local activeSave = profile:GetActiveSave();
			if profile and interactData.SetSpawn and activeSave then
				activeSave.Spawn = interactData.SetSpawn;
			end
			success = modServerManager:Travel(player, interactData.WorldId);
		end;
		
	end
	travelDebounce[player.Name] = nil;
	return success;
end

-- MARK: Open Storage Request
function remoteOpenStorageRequest.OnServerInvoke(player, interactObject, interactModule, storagePage)
	if true then
		error(`Attempt to use deprecated remote: OpenStorageRequest`);
		return;
	end
	--== Opening virtual storage;
	local storageItem = interactObject;
	if typeof(storageItem) == "table" and storageItem.ItemId then

		local usableItemLib = modUsableItems:Find(storageItem.ItemId);
		if usableItemLib == nil or usableItemLib.PortableStorage == nil then
			return;
		end
		
		local profile = modProfile:Get(player);
		
		local itemLib = modItemsLibrary:Find(storageItem.ItemId);
		local storageName = itemLib.Name;
		local storageConfig = usableItemLib.PortableStorage;
		
		local storageId = (usableItemLib.PortableStorage.StorageId)..(storagePage and "#p"..storagePage or "");
		if storageId == nil then Debugger:Warn("Missing storage id", storageConfig); return end;
		
		local storage = modStorage.Get(storageId);
		if storage then
			storage.Virtual = true;
			storage:RefreshAuth(player, 60);
			
		else
			storage = modStorage.Get(storageId, player);

		end
		
		if storage then
			if storageConfig.Expandable ~= true then
				storage.Size = storageConfig.MaxSize;
			end
			storage.Values.Siid = storageItem.ID;
			storage.Virtual = true;
			storage.PremiumPage = storageConfig.PremiumPage or storage.PremiumPage;
			storage.PremiumStorage = storageConfig.PremiumStorage or storage.PremiumStorage;
			modOnGameEvents:Fire("OnStorageOpen", player, storageItem);

			task.spawn(function()
				storage:Loop(function(storageItem)
					local attachmentStorage = modStorage.Get(storageItem.ID, player);
					if attachmentStorage then
						attachmentStorage:Sync();
					end
				end)
			end)

			return storage:Shrink();
			
		else
			modOnGameEvents:Fire("OnStorageOpen", player, storageItem);

			local activeSave = profile:GetActiveSave();
		
			local defaultSize = storagePage and 0 or storageConfig.Size;
			
			local newStorage;
			if activeSave == nil then return "Missing active save." end;
			if activeSave.Storages[storageId] == nil then
				activeSave.Storages[storageId] = modStorage.new(storageId, storageName, defaultSize, player);
				newStorage = activeSave.Storages[storageId];
				newStorage:InitStorage();
			end
			newStorage = activeSave.Storages[storageId];
			newStorage.Name = storageName;
			newStorage.MaxPages = storageConfig.MaxPages;
			newStorage.Page = storagePage;
			newStorage.MaxSize = storageConfig.MaxSize;
			newStorage.Size = math.clamp(activeSave.Storages[storageId].Size, defaultSize, storageConfig.MaxSize or activeSave.Storages[storageId].Size);
			newStorage.Expandable = storageConfig.Expandable;

			newStorage.PremiumPage = storageConfig.PremiumPage or newStorage.PremiumPage;
			newStorage.PremiumStorage = storageConfig.PremiumStorage or newStorage.PremiumStorage;

			newStorage.Virtual = true;
			newStorage.Values.Siid = storageItem.ID;
			
			return newStorage:Shrink();
		end
		
		return;
	end
	
	--== Opening physical storage;
	if interactObject == nil or interactModule == nil then
		Debugger:Warn(player.Name,", invalid storage interact object.", interactObject, interactModule);
		return "Invalid interact object.";
	end
	
	local interactData = shared.saferequire(player, interactModule);
	if interactData == nil then
		return "Invalid interact object.";
	end;
	
	interactData.Script = interactModule;
	if interactData.Type ~= modInteractables.Types.Storage then return "Interactable is not a storage." end;
	if not interactData:CheckDebounce(player.Name) then return "Interactable is on cooldown." end; -- Debugger:Warn(player.Name,", interactable is on cooldown.");
	if interactObject == nil or player:DistanceFromCharacter(interactObject.Position) > 20 then
		Debugger:Warn(player.Name,", Too far from object."); 
		return "Too far from object.";
	end;

	if interactObject == nil or interactData == nil then
		return "Invalid interact object.";
	end

	Debugger:Warn(player,"Requesting for physical storage.", interactData.StorageId);

	local profile = modProfile:Get(player);
	local gameSave = profile:GetActiveSave();
	
	if interactData.Whitelist and interactData.Whitelist[player.Name] == nil then
		Debugger:Warn(player.Name,", Interact object locked..");
		return "Interact object locked.";
	end
	
	if interactData.LevelRequired and interactData.LevelRequired > (gameSave:GetStat("Level") or 0) then
		Debugger:Warn(player.Name,", underleveled.");
		return "Interact object locked.";
	end
	
	local storageConfig = interactData.Configurations;
	if storageConfig.MaxPages and storageConfig.MaxPages >= 2 and storagePage then
		storagePage = math.clamp(storagePage, 2, storageConfig.MaxPages);
	end
	
	local storageId = interactData.StorageId..(storagePage and "#p"..storagePage or "");
	if storageId == nil then
		Debugger:Warn("Missing storage id", interactData);
		return "Missing storage";
	end

	local defaultSize = storageConfig.Size or 1;
	if storageConfig.Expandable and storagePage then
		defaultSize = 0;
	end
	
	local storage = modStorage.Get(storageId);
	if storage then
		storage.Physical = interactObject;
		storage:RefreshAuth(player, 60);
		
	else
		storage = modStorage.Get(storageId, player);
		
	end
	interactData:Sync();
	
	if storage then
		modOnGameEvents:Fire("OnStorageOpen", player, interactData);

		task.spawn(function()
			storage:Loop(function(storageItem)
				local attachmentStorage = modStorage.Get(storageItem.ID, player);
				if attachmentStorage then
					attachmentStorage:Sync();
				end
			end)
		end)
		
		if interactData.Configurations.Persistent then
			storage.MaxPages = storageConfig.MaxPages;
			storage.Page = storagePage;
			storage.MaxSize = storageConfig.MaxSize;
			storage.Size = math.clamp(storage.Size, defaultSize, storageConfig.MaxSize or storage.Size);
			storage.Expandable = storageConfig.Expandable;
			storage.PremiumPage = storageConfig.PremiumPage or storage.PremiumPage;
			storage.PremiumStorage = storageConfig.PremiumStorage or storage.PremiumStorage;
			
			if storageConfig.Settings then
				for k, v in pairs(storageConfig.Settings) do
					if storage.Settings[k] ~= nil then
						storage.Settings[k] = v;
					end
				end
			end
		end
		
		return storage:Shrink();
		
	elseif storage == nil and interactData.Configurations then
		modOnGameEvents:Fire("OnStorageOpen", player, interactData);
		
		local publicStorage = interactData.Configurations.PublicStorage;
		local storageOwner = publicStorage ~= true and player or nil;
		
		
		if interactData.Configurations.Persistent then
			local activeSave = profile:GetActiveSave();
			
			if activeSave == nil then return "Missing active save." end;
			if activeSave.Storages[storageId] == nil then
				activeSave.Storages[storageId] = modStorage.new(
					storageId, interactData.StorageName, defaultSize, storageOwner);
				
				if interactData.OnNewStorage then
					interactData:OnNewStorage(activeSave.Storages[storageId]);
				end
			end
			
			local newStorage = activeSave.Storages[storageId];
			newStorage.MaxPages = storageConfig.MaxPages;
			newStorage.Page = storagePage;
			newStorage.MaxSize = storageConfig.MaxSize;
			newStorage.Size = math.clamp(activeSave.Storages[storageId].Size, defaultSize, storageConfig.MaxSize or activeSave.Storages[storageId].Size);
			newStorage.Expandable = storageConfig.Expandable;
			newStorage.PremiumPage = storageConfig.PremiumPage or newStorage.PremiumPage;
			newStorage.PremiumStorage = storageConfig.PremiumStorage or newStorage.PremiumStorage;
			newStorage.Physical = interactObject;
			newStorage:RefreshAuth(player, 60);

			if storageConfig.Settings then
				for k, v in pairs(storageConfig.Settings) do
					if newStorage.Settings[k] ~= nil then
						newStorage.Settings[k] = v;
					end
				end
			end
			
			return newStorage:Shrink();
			
		else
			local cacheStorages = profile:GetCacheStorages();
			cacheStorages[storageId] = modStorage.new(
				storageId, interactData.StorageName, defaultSize, storageOwner);
			cacheStorages[storageId].Physical = interactObject;
			if storageConfig.Settings then
				for k, v in pairs(storageConfig.Settings) do
					if cacheStorages[storageId].Settings[k] ~= nil then
						cacheStorages[storageId].Settings[k] = v;
					end
				end
			end
			
			if interactData.OnNewStorage then
				interactData:OnNewStorage(cacheStorages[storageId]);
			end
			
			return cacheStorages[storageId]:Shrink();
		end
		
	else
		return "Invalid storage.";
	end
end

function remoteOnTrigger.OnServerInvoke(player, interactObject, interactModule, packet)
	packet = packet or {};
	local profile = modProfile:Get(player);
	
	if profile.Cache.remoteOnTriggerCd and tick()-profile.Cache.remoteOnTriggerCd <= 0.5 then
		return "Activate on cooldown.";
	end;
	profile.Cache.remoteOnTriggerCd = tick();
	
	local distanceFromTrigger = interactObject and player:DistanceFromCharacter(interactObject.Position) or -1;
	if interactObject == nil or distanceFromTrigger > 20 then 
		Debugger:Warn(player.Name,"Too far from trigger. ("..distanceFromTrigger..")"); 
		return "Too far from object."; 
	end;

	if interactObject and interactModule then
		
		local interactData = shared.saferequire(player, interactModule);
		if interactData == nil then return "Invalid interact object." end;
		
		interactData.Object = interactObject;
		interactData.Script = interactModule;
		
		shared.modEventService:ServerInvoke("Interactables_BindTrigger", {ReplicateTo={player}}, interactData, packet);
	else
		return "Invalid interact object.";
	end
	return;
end


remoteInteractableToggle.OnServerEvent:Connect(function(player, interactObject, interactModule)
	local distanceFromTrigger = interactObject and player:DistanceFromCharacter(interactObject.Position) or -1;
	if interactObject == nil or distanceFromTrigger > 20 then Debugger:Warn(player.Name,"Too far from toggle. ("..distanceFromTrigger..")"); return "Too far from object."; end;
	if interactObject and interactModule then
		
		local interactData = shared.saferequire(player, interactModule);
		if interactData == nil then return "Invalid interact object." end;
		
		interactData.Object = interactObject;
		interactData.Script = interactModule;
		
		if interactData.OnToggle then
			modOnGameEvents:Fire("OnToggle", player, interactData);
			interactData:OnToggle(player);
		end
	else
		return "Invalid interact object.";
	end
end)


function remoteUpgradeStorage.OnServerInvoke(player, storageId)
	local profile = modProfile:Get(player);
	local playerSave = profile and profile:GetActiveSave();
	local storage = storageId and modStorage.Get(storageId, player);
	
	if storage and storage.Expandable and storage.Size < storage.MaxSize then
		local newSize = storage.Size+1;
		local cost = modWorkbenchLibrary.StorageCost(storageId, storage.Size, storage.Page or 1);
		local playerCurrency = playerSave and playerSave.GetStat and playerSave:GetStat("Perks");
		
		if playerCurrency-cost >= 0 then
			playerSave:AddStat("Perks", -cost);

			modAnalytics.RecordResource(player.UserId, cost, "Sink", "Perks", "Gameplay", "StorageUpgrade");
			modAnalyticsService:Sink{
				Player=player;
				Currency=modAnalyticsService.Currency.Perks;
				Amount=cost;
				EndBalance=playerSave:GetStat("Perks");
				ItemSKU="StorageUpgrade";
			};

			storage.Size = newSize;
			storage.OnChanged:Fire(storage);
			
			profile:AddPlayPoints(cost, "Sink:Perks");
			return storage:Shrink();
		else
			return 2;
		end
	else
		return 1;
	end	
end

function remoteMailboxFunction.OnServerInvoke(player, index, action)
	local profile = modProfile:Get(player);
	
	if profile.Cache.remoteMailboxFunctionDebounce then return end;
	profile.Cache.remoteMailboxFunctionDebounce = true;
	
	if index == nil then Debugger:WarnClient(player, "Missing Mail Index"); return end;
	local profile = modProfile:Get(player);
	local activeSave = profile and profile:GetActiveSave();
	local successful = nil;
	
	if activeSave and index <= #activeSave.Mailbox then
		local mailData = activeSave.Mailbox[index];
		if mailData.Type == 1 then
			if mailData.Data.Amount and mailData.Data.Amount > 0 then
				activeSave:AddStat("Perks", mailData.Data.Amount);
			end
			successful = true;
			
		elseif mailData.Type == 2 then
			activeSave:AddStat("Perks", 10);
			successful = true;
			
		elseif mailData.Type == 3 then
			local activeInventory = profile.ActiveInventory;
			if activeInventory then
				if mailData.Data and mailData.Data.ItemId then
					local itemId = mailData.Data.ItemId;
					local itemLib = modItemsLibrary:Find(itemId);
					if itemLib then
						if activeInventory:SpaceCheck{{ItemId=itemId}} then
							activeInventory:Add(itemId, nil, function(event, insert)
								shared.Notify(player, string.gsub("You recieved a $Item.", "$Item", itemLib.Name), "Reward");
							end);
							successful = true;
						else
							successful = "Inventory full!";
						end
					else
						successful = "Invalid item id ("..itemId..")";
					end
				end
			end
			
		elseif mailData.Type == 4 then
			if modEvents:GetEvent(player, "masteryLevelReward") == nil then
				if mailData.Data.Amount and mailData.Data.Amount > 0 then
					activeSave:AddStat("Perks", mailData.Data.Amount);
				end
				modEvents:NewEvent(player, {Id="masteryLevelReward"});
			end
			successful = true;
			
		elseif mailData.Type == 5 then
			-- Referral Complete
			successful = true;
			
		elseif mailData.Type == 6 then
			activeSave:AddStat("TweakPoints", mailData.Data.Amount);
			shared.Notify(player, "You recieved "..mailData.Data.Amount.." tweak points.", "Reward");
			successful = true;
			
		elseif mailData.Type == 99 then
			if action == "Claim" then
				successful = true;
				local weapons = mailData.Data.Weapons;
				if weapons then
					local activeInventory = profile.ActiveInventory;
					if activeInventory then
						local itemsCheck = {};
						for weaponName, _ in pairs(weapons) do
							local itemLib = modItemsLibrary:Find(weaponName);
							if itemLib then
								local itemId = itemLib.Id;
								table.insert(itemsCheck, {ItemId=itemId});
							end
						end
						if activeInventory:SpaceCheck(itemsCheck) then
							for a=1, #itemsCheck do
								if itemsCheck[a] then
									local itemLib = modItemsLibrary:Find(itemsCheck[a].ItemId);
									activeInventory:Add(itemsCheck[a].ItemId, nil, function(event, insert)
										shared.Notify(player, string.gsub("You recieved a $Item.", "$Item", itemLib.Name), "Reward");
									end);
								end
							end
						else
							successful = "Not enough inventory space.";
						end
					end
				end
				--local blueprints = mailData.Data.Blueprints;
				--if blueprints then
				--	local userBlueprints = activeSave.Blueprints;
				--	for bpName,_ in pairs(blueprints) do
				--		local bpLib = :FindByName(bpName);
				--		if bpLib then
				--			userBlueprints:UnlockBlueprint(bpLib.Id);
				--			mailData.Data.Blueprints[bpName] = nil;
				--		end
				--	end
				--	mailData.Data.Blueprints = {};
				--end
			elseif action == "Destroy" then
				successful = true;
			end
		else
			Debugger:Log("Missing mail type function");
		end
		if successful == true then
			table.remove(activeSave.Mailbox, index);
		end
		activeSave:SyncMail();
	end
	
	profile.Cache.remoteMailboxFunctionDebounce = nil;
	return successful;
end

local function isGameOnline()
	local isGameOnline = false;
	local s, e = pcall(function()
		isGameOnline = game:GetService("DataStoreService"):GetDataStore("LiveConfig"):GetAsync("Online") ~= false
		workspace:SetAttribute("IsGameOnline", isGameOnline);
	end)
	return isGameOnline;
end
task.spawn(function()
	if modBranchConfigs.IsWorld("MainMenu") then
		while true do
			isGameOnline();
			task.wait(5);
		end
	end
end)

remoteDuelRequest.OnServerEvent:Connect(function(player, requestType, targetName)
	local classPlayerSpeaker = modPlayers.get(player);
	local speakerPvp = classPlayerSpeaker.Properties.Pvp;
	if speakerPvp == nil then
		classPlayerSpeaker.Properties.Pvp = {};
		speakerPvp = classPlayerSpeaker.Properties.Pvp;
	end
	
	if speakerPvp.Requesting and os.time()-speakerPvp.Requesting < 60 then
		shared.Notify(player, "You pvp request is on cooldown for ".. 60-(os.time()-speakerPvp.Requesting) .."s.", "Negative");
		return false;
	end
		
	if player.Name == targetName then
		shared.Notify(player, "You can't duel yourself.", "Negative"); 
		return false;
	end;
	
	local targetPlayer = game.Players:FindFirstChild(targetName);
	if targetPlayer then
		local classPlayerTarget = modPlayers.get(targetPlayer);
		local targetPvp = classPlayerTarget.Properties.Pvp;
		if targetPvp and targetPvp.InDuel == nil and targetPvp.Name == player.Name and os.time()-targetPvp.Requesting <= 30 then
			local players = {player, targetPlayer};
			shared.Notify(game.Players:GetPlayers(), "A duel has broke out between "..targetPlayer.Name.." and "..player.Name..".", "Defeated");
			for a=5, 1, -1 do
				shared.Notify(players, "The duel begins in "..a..".", "Defeated");
				wait(1);
			end
			shared.Notify(players, "The duel has begun!", "Defeated");
			targetPvp.InDuel = player.Name;
			speakerPvp.InDuel = targetPlayer.Name;
			speakerPvp.DmgMultiplier = targetPvp.DmgMultiplier;
			
		else
			speakerPvp.Requesting = os.time();
			speakerPvp.Name = targetPlayer.Name;
			speakerPvp.DmgMultiplier = 1;
			shared.Notify(player, "Requesting "..targetPlayer.Name.." to a duel..\nThe request will expire in 30 seconds.", "Defeated");
			shared.Notify(targetPlayer, player.Name.." is requesting you to a duel..", "Defeated");
			remoteDuelRequest:FireClient(targetPlayer, "request", player.Name);
			
		end
	else
		shared.Notify(player, "Could not find player ("..targetName..")..", "Negative");
		
	end
end)

remoteRequestResetData.OnServerEvent:Connect(function(player)
	local profile = modProfile:Get(player);
	--profile.ActiveSave = nil;
	--profile.Saves = {};
	
	profile:ResetSave();
	profile.Reset = true;
	modServerManager:Travel(player, "MainMenu");
	shared.Notify(player, "Data reset complete.", "Positive");
end)

function remoteGeneralUIRemote.OnServerInvoke(player, action)
	if remoteGeneralUIRemote:Debounce(player) then return end;
	
	local profile = modProfile:Get(player);
	local saveData = profile:GetActiveSave();
	local playerFlags = profile.Flags;
	
	if action == "closeupdatelog" then
		modEvents:NewEvent(player, {Id="seenupdatelog"});
	end
end


function remoteLockHydra.OnServerInvoke(player, action, interactData, ...)
	if interactData == nil then return end;

	local interactObject: BasePart = interactData.Object;
	local interactModule: ModuleScript = interactData.Script;
	
	local interactableModel = interactObject.Parent;
	
	while interactableModel:GetAttribute("InteractableParent") == true do
		interactableModel = interactableModel.Parent;
	end
	
	if not interactableModel:IsAncestorOf(interactModule) then Debugger:Warn("Invalid Interactable.") return end;

	local interactData = shared.saferequire(player, interactModule);
	if interactData == nil then return "Invalid interact object." end;

	interactData.Object = interactObject;
	interactData.Script = interactModule;

	if interactData.Type ~= "Terminal" then return "Invalid interact object." end;

	modOnGameEvents:Fire("OnLockHydra", player, action, interactData);
end

modStorage.OnItemSourced:Connect(function(sourceStorage, storageItem, quantity)
	if quantity <= 0 then return; end
	
	modEconomyAnalytics.Record(storageItem.ItemId, quantity);
end)

modStorage.OnItemSunk:Connect(function(sourceStorage, storageItem, quantity)
	quantity = -quantity;
	if quantity >= 0 then return; end
	
	modEconomyAnalytics.Record(storageItem.ItemId, quantity);
end)


task.spawn(function()
	while true do
		task.wait(60);
		if modBranchConfigs.IsWorld("MainMenu") then
			Debugger:Warn("LiveProfile Update disabled for menu.");
			continue;
		end;
		
		local players = game.Players:GetPlayers();
		for _, player in pairs(players) do
			local onlineSetS, onlineSetE = pcall(function()
				local lastOnlineData = MemoryStoreService:GetHashMap("LastOnline");
				lastOnlineData:SetAsync(tostring(player.UserId), DateTime.now().UnixTimestamp, 61);

				local accessCodeData = MemoryStoreService:GetHashMap("AccessCode");

				if modServerManager.AccessCode then
					accessCodeData:SetAsync(tostring(player.UserId), modServerManager.AccessCode, 61);
				else
					accessCodeData:RemoveAsync(tostring(player.UserId));
				end
			end)
		end
	end
end)
	
game:BindToClose(function()
	if RunService:IsStudio() then return end;
	task.wait();
	local threads = {};
	
	for playerName, profile in pairs(modProfile.Profiles) do
		if profile ~= nil then
			threads[playerName] = false;
			task.spawn(function()
				profile:Save(); -- Shutdown save;
				Debugger:Log("Profile(",playerName,") shutdown save completed.");
				threads[playerName] = true;
			end)
		end
	end
	
	local done = true;
	repeat
		done = true;
		for name, complete in pairs(threads) do
			if not complete then
				done = false;
				break;
			end
		end
		task.wait();
	until done;
	
	Debugger:Log("All profiles shutdown save completed.");
	task.wait(0.5);
end);

modCutscene.Init();
Debugger:Log("Initialized server master script.");

if modBranchConfigs.CurrentBranch.Name == "Dev" then
	workspace:SetAttribute("IsDev", true);
	if RunService:IsStudio() then return end;
	
	ScriptContext.Error:Connect(function(message, trace, scr)
		if scr == nil then return end;
		if scr:GetAttribute("SuppressWarnClient") == true then return end;

		if message == nil then return end;
		if message:match("resume") and message:match("coroutine") then return end;

		Debugger:WarnClient(game.Players:GetPlayers(), scr.Name ..">> [ERROR] ".. message .. "\n".. trace);
	end)
end

if modBranchConfigs.WorldInfo.InitLeaderboard then
	task.spawn(modLeaderboardService.Init);
end

shared.MasterScriptInit = true;