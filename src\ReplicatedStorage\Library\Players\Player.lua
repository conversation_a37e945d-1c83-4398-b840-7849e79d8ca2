local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local Player = {};
local PlayerService = {
	get = function(player)end;
	Players = nil;
	SkillTree = nil;
	OnPlayerSpawn = nil;
	OnPlayerDied = nil;
};

local RunService = game:GetService("RunService");
local CollectionService = game:GetService("CollectionService");

local modConfigurations = shared.require(game.ReplicatedStorage.Library.Configurations);
local modBranchConfigs = shared.require(game.ReplicatedStorage.Library.BranchConfigurations);
local modSyncTime = shared.require(game.ReplicatedStorage.Library.SyncTime);
local modInfoBubbles = shared.require(game.ReplicatedStorage.Library.InfoBubbles);
local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);
local modCustomizeAppearance = shared.require(game.ReplicatedStorage.Library.CustomizeAppearance);
local modMapLibrary = shared.require(game.ReplicatedStorage.Library.MapLibrary);
local modLayeredVariable = shared.require(game.ReplicatedStorage.Library.LayeredVariable);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modDamageTag = shared.require(game.ReplicatedStorage.Library.DamageTag);
local modGarbageHandler = shared.require(game.ReplicatedStorage.Library.GarbageHandler);
local modConfigVariable = shared.require(game.ReplicatedStorage.Library.ConfigVariable);
local modPropertiesVariable = shared.require(game.ReplicatedStorage.Library.PropertiesVariable);
local modStrings = shared.require(game.ReplicatedStorage.Library.Util.Strings);

local modStatusComponent = shared.require(game.ReplicatedStorage.Components.StatusComponent);
local modHealthComponent = shared.require(game.ReplicatedStorage.Components.HealthComponent);
local modWieldComponent = shared.require(game.ReplicatedStorage.Components.WieldComponent);

local DamageData = shared.require(game.ReplicatedStorage.Data.DamageData);

local modMath = shared.require(game.ReplicatedStorage.Library.Util.Math);

local localPlayer = game.Players.LocalPlayer;

local remotePlayerClass: RemoteEvent;
local remoteDamagePacket;
local remoteToolHandler;
--==

function Player.new(playerInstance: Player)
	playerInstance.ReplicationFocus = workspace:FindFirstChildWhichIsA("SpawnLocation");

	local meta: anydict = {};

	local playerClass = setmetatable({}, meta);

	meta.Name = playerInstance.Name;
	meta.ClassName = "PlayerClass";
	meta.HumanoidType = "Player";

	meta.LastLoadCharacter = tick()-5;

	meta.OnCharacterSpawn = shared.EventSignal.new("OnCharacterSpawn");
	meta.OnDamageTaken = shared.EventSignal.new("OnDamageTaken");
	meta.OnIsDeadChanged = shared.EventSignal.new("OnIsDeadChanged");

	meta.Garbage = modGarbageHandler.new();
	meta.CharacterGarbage = modGarbageHandler.new();

	function meta:GetInstance()
		return playerInstance;
	end
	meta.__index = meta;

	playerClass.Name = playerInstance.Name;

	meta.OldConfigurations = {};
	local configurations = modConfigVariable.new({
		HealRate = 0;
		MaxHealth = 100;
		MaxHealthOverCharge = 0;

		MaxArmor = 0;
		ArmorRate = 0.1;
		ArmorRegenDelay = 1;
		MaxArmorOverCharge = 0;

		Oxygen = 100;

		HotEquipSlots = 5;
	});
	playerClass.Configurations = configurations;

	local properties = modPropertiesVariable.new{
		Ragdoll = 0; -- 1 server ragdoll, 2 client ragdoll.

		OverHeal = 0;
		OverHealLimit = 35;

		HealthOverchargeSources = {};

		TemperatureOffset = modLayeredVariable.new(0);

		IsUnderWater = false;
	};
	meta.OnPropertyChanged = properties.OnChanged;
	playerClass.Properties = properties;

	playerClass.LastDamageDealt = workspace:GetServerTimeNow()-15;

	playerClass.LowestFps = 999;
	playerClass.AverageFps = 999;
	--==


	--MARK: Def StatusComp
	local statusComp: StatusComp = modStatusComponent.new(playerClass :: any, false);
	playerClass.StatusComp = statusComp;

	statusComp.OnProcess:Connect(function()
		
	end)


	-- MARK: Enddef StatusComp
	--
	--
	--
	--
	--
	-- MARK: Def HealthComp
	local healthComp: HealthComp = modHealthComponent.new(playerClass :: any);
	healthComp:SetCanBeHurtBy("Zombie|Bandit|Rat|Cultist");

	-- @override TakeDamage
	function healthComp:TakeDamage(damageData: DamageData)
		local damage: number = damageData.Damage;
		local damageType = damageData.DamageType;

		local damageCategory = damageData.DamageCate or DamageData.DamageCategory.Generic;
		local hitPart: BasePart? = damageData.TargetPart;

		damageData.DamageTo = playerClass;
		local damageTo: CharacterClass? = damageData.DamageTo;
		local damageBy: CharacterClass? = damageData.DamageBy;
		local storageItem: StorageItem? = damageData.StorageItem;

		if damage == nil then return end;

		if damageType == "Heal" and damage > 0 then
			damage = -damage;
		end

		if damage > 0 and damageTo then
			if damageCategory == DamageData.DamageCategory.Melee then
				local hasTireArmor = damageTo.Properties.TireArmor;
				if hasTireArmor and hasTireArmor.Visible and math.random(1, 100) <= 73 then -- pseudo random
					damage = math.max(1, damage-40);
					modAudio.Play("TireArmorBlock", damageTo.RootPart);
				end

			elseif damageCategory == DamageData.DamageCategory.FumesGas then
				local cGasProtection = damageTo.Configurations.GasProtection;
				local cLabCoat = damageTo.Configurations.LabCoat;

				if cGasProtection then
					cGasProtection = cLabCoat and cGasProtection + cLabCoat or cGasProtection;
					damage = damage * (1-cGasProtection);

					if cLabCoat then
						statusComp:Apply("LabCoat", {
							Expires = workspace:GetServerTimeNow()+2;
						});
					end
				end
			end
		end

		--==
		local initDamage = damage;
		local armorDamage = damage;

		damageData.InitDamage = initDamage;

		if damage > 0 and damageTo then
			if damageBy then
				task.spawn(function()
					if damageBy.ClassName == "PlayerClass" then
						modDamageTag.Tag(damageTo.Character, damageBy.Character, {
							WeaponItemId=(storageItem and storageItem.ItemId or nil);
							IsHeadshot=(hitPart and hitPart.Name == "Head");
						});

					elseif damageBy.ClassName == "NpcClass" then
						modDamageTag.Tag(damageTo.Character, damageBy.Character, {
							WeaponItemId=(storageItem and storageItem.ItemId or nil);
							IsHeadshot=(hitPart and hitPart.Name == "Head");
						});

					end
				end)
			end

			if initDamage > 0 and (damageTo.Properties.ThornCooldown == nil or tick()-damageTo.Properties.ThornCooldown >= 0.1) and healthComp.CurArmor > 0 and damageType ~= "Thorn" then
				local cDamageReflection = damageTo.Configurations.DamageReflection;
				if modConfigurations.DisableGearMods then
					cDamageReflection = nil;
				end

				if cDamageReflection and cDamageReflection > 0 and damageBy and damageCategory == DamageData.DamageCategory.Melee then
					-- Damage Reflection

					local healthComp: HealthComp? = modHealthComponent.getByModel(damageBy.Character);
					if healthComp and damage then
						local reflectedDmg = math.max(healthComp.CurHealth * cDamageReflection, 10);

						local thornDmgData = DamageData.new{
							Damage = reflectedDmg;
							DamageBy = damageTo;

							TargetModel = damageBy.Character;
							TargetPart = damageBy.RootPart;
							DamageType = "Thorn";
						};

						healthComp:TakeDamage(thornDmgData);

						damageTo.Properties.ThornCooldown = tick();
					end
				end
			end

			if damageType ~= "IgnoreArmor" then
				local armorBreakStatus = statusComp:GetOrDefault("ArmorBreak");

				if healthComp.CurArmor > 0 then
					damage = math.max(1, damage);
					armorDamage = damage > healthComp.CurArmor and healthComp.CurArmor or damage;

					healthComp:SetArmor(healthComp.CurArmor - damage);

					if damageTo.RootPart then
						modAudio.Play("ArmorHit"..math.random(1,4), damageTo.RootPart).PlaybackSpeed = math.random(60, 80)/100;
					end

					damageType = "Armor";

					if healthComp.CurArmor <= 0 then
						damage = math.floor(damage*10)/10;

						local newRegenDelay = math.abs(healthComp.CurArmor) / (healthComp.MaxHealth/50);
						newRegenDelay = math.clamp( math.floor(newRegenDelay*10) / 10, 10, 60);
						damageTo.Properties.ArmorRegenDelay = newRegenDelay;

						statusComp:Apply("ArmorBreak", {
							Expires = workspace:GetServerTimeNow() + newRegenDelay;
							Duration = newRegenDelay;

							Values = {
								ApplyTo = damageTo;
								Delay = newRegenDelay;
								Damage = damage;
							};
						});

						damageType = "ArmorBreak";
					end
					damage = 0;

				elseif armorBreakStatus ~= nil then

					local newTime = workspace:GetServerTimeNow() + 5;

					if newTime > armorBreakStatus.Expires then
						armorBreakStatus.Expires = newTime;
						armorBreakStatus.Values.Delay = 5;

						armorBreakStatus:Sync();
					end

				end

			else
				damageType = nil;
			end

			if damageType == "ArmorOnly" then
				return;
			end

			local modStatusEffects = shared.require(game.ReplicatedStorage.Library.StatusEffects :: ModuleScript);
			if damageTo.Properties.Freezing == nil and damageTo.Properties.Burn == nil then
				if damageTo.Properties.TooHot and math.random(1, 2) == 1 then
					modStatusEffects.Burn(playerInstance, math.clamp(damageTo.Properties.TooHot.Amount-40, 0, 10), 5);

				elseif damageTo.Properties.TooCold and math.random(1, 2) == 1 then
					modStatusEffects.Freezing(playerInstance, 5);

				end
			end

			if damageTo.Character:FindFirstChildWhichIsA("ForceField") then
				damage = 0;
			end

			local borrowedTimeEnabled = modConfigurations.PvpMode == false;

			local isLethal = healthComp.CurHealth - damage <= healthComp.KillHealth;
			if borrowedTimeEnabled and meta.BorrowedTime and tick()-meta.BorrowedTime <= 0.5 then

			elseif borrowedTimeEnabled and isLethal and (meta.BorrowedTime == nil or tick()-meta.BorrowedTime >= 60) and math.random(0, 10) >= 4 then
				meta.BorrowedTime = tick();
				healthComp:SetHealth(math.min(healthComp.CurHealth, math.random(1, 9)), damageData);

				if RunService:IsStudio() then
					Debugger:Warn(playerInstance, "[Studio] BorrowedTime strikes!");
				end

			else
				if isLethal then
					local baseWoundedDuration = modConfigurations.BaseWoundedDuration;

					-- if moddedSelf and moddedSelf.OnLethalDamageTaken then
					-- 	moddedSelf:OnLethalDamageTaken(playerClass);
					-- else
					if baseWoundedDuration > 0 then
						local woundedStatus: StatusClassInstance = statusComp:GetOrDefault("Wounded");
						local knockOutStatus: StatusClassInstance = statusComp:GetOrDefault("KnockedOut");

						if woundedStatus == nil and knockOutStatus == nil then
							statusComp:Apply("Wounded", {
								Expires = workspace:GetServerTimeNow() + baseWoundedDuration;
								Duration = baseWoundedDuration;
							});

						end

					else
						damageTo:Kill();

					end

					damageTo.WieldComp:Unequip()
					
				else
					healthComp:SetHealth(healthComp.CurHealth - damage, damageData);

				end
			end

		elseif damage < 0 then -- MARK: Heal
			local healAmount = -damage;

			shared.modEventService:ServerInvoke(
				"Players_BindHeal", 
				{ReplicateTo={playerInstance}}, 
				playerClass, 
				damageData
			);
			healthComp:SetHealth(healthComp.CurHealth + healAmount, damageData);

		end

		if hitPart then
			local damageByPlayer: Player? = damageBy and damageBy.ClassName == "PlayerClass" and (damageBy :: PlayerClass):GetInstance() or nil;

			if damageType == "ArmorBreak" then
				damageData.DamageType = "Armor";
				modInfoBubbles.Create{
					Players={playerInstance; damageByPlayer};
					Position=hitPart.Position;
					Type="AntiShield";
				};

			elseif damageType == "Armor" then
				damageData.DamageType = "Armor";
				modInfoBubbles.Create{
					Players={playerInstance; damageByPlayer};
					Position=hitPart.Position;
					Type="Armor";
					Value=armorDamage;
				};


			elseif damage > 0 then
				local killSnd;
				if healthComp.CurHealth <= 0 then
					killSnd = (hitPart.Name == "Head" or hitPart:GetAttribute("IsHead") == true) and "KillHead" or "KillFlesh";
				end

				modInfoBubbles.Create{
					Players={playerInstance; damageByPlayer};
					Position=hitPart.Position;
					Type=(damageType or "Damage");
					Value=math.ceil(damage);
					KillSnd=killSnd;
				};


			elseif damage < 0 then
				modInfoBubbles.Create{
					Players={playerInstance; damageByPlayer};
					Position=hitPart.Position;
					Type="Heal";
					Value=math.abs(damage);
				};

			else
				modInfoBubbles.Create{
					Players={playerInstance; damageByPlayer};
					Position=hitPart.Position;
					Type="Immune";
				};

			end
		end

		local targetPart = damageData.TargetPart;
		if targetPart == nil and damageBy and damageBy.Character and damageBy.Character.PrimaryPart then
			targetPart = damageBy.Character.PrimaryPart;
		end

		remoteDamagePacket:FireClient(damageTo:GetInstance(), {
			Damage = damage;
			DamageType = damageType;
			TargetPart = targetPart;
		});
		damageTo.OnDamageTaken:Fire(damage);

		shared.modEventService:ServerInvoke("Players_BindDamaged", {ReplicateTo={playerInstance}}, damageData, damage);
	end

	-- @override CanTakeDamageFrom
	function healthComp:CanTakeDamageFrom(attackerCharacter: CharacterClass?)
		if attackerCharacter == nil then return true; end;
		if attackerCharacter == playerClass then return false end;
		
		local checkBoolString = {
			attackerCharacter.HumanoidType;
			attackerCharacter.ClassName;
			attackerCharacter.Name;
		};
		if self:CheckCanBeHurtBy(checkBoolString) == false then
			Debugger:Warn("CanTakeDamageFrom", "Failed CanBeHurtBy check.", checkBoolString, self.CanBeHurtByBoolString);
			return false;
		end

		local npcClass: NpcClass = (attackerCharacter :: NpcClass);
		if attackerCharacter.ClassName == "NpcClass" and npcClass.NetworkOwners then
			local isNetworkOwner = false;
			for a=1, #npcClass.NetworkOwners do
				if npcClass.NetworkOwners[a] == playerInstance then
					isNetworkOwner = true;
					break;
				end
			end
			if isNetworkOwner == false then
				return false;
			end
		end

		return true;
	end

	playerClass.HealthComp = healthComp;
	-- MARK: Enddef HealthComp
	--
	--
	--
	--
	--
	--
	--MARK: Def WieldComp
	local wieldComp: WieldComp = modWieldComponent.new(playerClass :: any);

	function wieldComp:Equip(packet)
		packet = packet or {};
		local returnPacket = modEquipmentSystem.equipTool(playerInstance, packet);

		if packet.ClientInvoked ~= true then
			remoteToolHandler:InvokeClient(playerInstance, returnPacket);
		end

		return returnPacket;
	end

	function wieldComp:Unequip(packet)
		packet = packet or {};
		
		modEquipmentSystem.unequipTool(playerInstance, packet);
		if packet.ClientInvoked ~= true then
			remoteToolHandler:InvokeClient(playerInstance, packet);
		end
		
		return packet;
	end

	playerClass.WieldComp = wieldComp;
	--MARK:	Enddef WieldComp
	--
	--
	--
	--
	--
	--
	--MARK: Spawn()
	function meta:Spawn()
		local crTick = tick();
		while self.ClientReady ~= true do
			task.wait();
			if (tick()-crTick) >= 5 then
				Debugger:Warn(":Spawn() still waiting for ClientReady. (",playerInstance,").");
				crTick = tick();
			end
		end

		while playerInstance:GetAttribute("Ignited") ~= true do
			--Debugger:WarnClient(playerInstance, "Waiting for ignition..");
			task.wait();
			if (tick()-crTick) >= 5 then
				Debugger:Warn(":Spawn() still waiting for Ignited. (",playerInstance,").");
				crTick = tick();
			end
		end

		if not game.Players:IsAncestorOf(playerInstance) then
			Debugger:Warn(":Spawn() canceled, playerInstance no longer in game.Players.")
			return
		end;

		if tick()-meta.LastLoadCharacter <= 2 then
			Debugger:Warn(":Spawn() canceled, playerInstance spawned too recently.");
			return
		end
		meta.LastLoadCharacter = tick();
		playerInstance:LoadCharacter();
	end

	function meta:Despawn()
		if not game.Players:IsAncestorOf(playerInstance) then return end;

		if meta.StatusCycle then
			meta.StatusCycle:Disconnect();
		end

		if playerInstance.Character then
			playerInstance.Character:Destroy();
		end
	end

	function meta:Kill(killReason)
		if meta.DeathDebounce then return end;
		meta.DeathDebounce = true;

		if RunService:IsServer() then
			remotePlayerClass:FireAllClients(playerClass.Name, "Kill");

			local face = playerClass.Head:FindFirstChild("face");
			if face then
				face.Texture = "rbxassetid://**********";
			end
		end

		healthComp:SetHealth(0);
		healthComp:SetArmor(0);
		healthComp:SetIsDead(true, {KillReason=killReason});
		playerClass.Humanoid:ChangeState(Enum.HumanoidStateType.Dead);

		Debugger:StudioWarn("PlayerClass:Kill");
	end

	function meta:SyncProperty(key, players)
		if playerInstance.Parent == nil then return end;
		if RunService:IsClient() then return end;
		
		if players == nil then
			players = {playerInstance};
		end
		players = typeof(players) == "table" and players or {players};

		local value = properties[key];
		for a=1, #players do
			remotePlayerClass:FireClient(players[a], playerInstance.Name, "syncprop", key, value);
		end
	end

	function meta:SetProperties(key, value)
		Debugger:Warn(`Using deprecated SetProperties`, key, value);
	end

	function meta:GetCFrame()
		return playerClass and playerClass.RootPart and playerClass.RootPart.CFrame or nil;
	end

	function meta:GetMass()
		if self.Character == nil then return end;

		local mass = 0;
		for _, child in pairs(self.Character:GetDescendants()) do
			if child:IsA("BasePart") and child.Parent.ClassName ~= "Accessory" then
				mass = mass + child:GetMass();
			end
		end
		return mass;
	end

	function meta:GetHealthComp(bodyPart: BasePart): HealthComp
		return healthComp;
	end

	function meta:UpdateHealthStats()
		local cMaxHealth = configurations.MaxHealth;
		local cHealthPoints = (configurations.HealthPoints or 0);

		if modConfigurations.DisableGearMods then
			cHealthPoints = 0;
		end
		

		local cHealRate = configurations.HealRate;
		local cMaxHealthOverCharge = configurations.MaxHealthOverCharge;
		
		for _, statusClass: StatusClassInstance in pairs(statusComp.List) do
			local values = statusClass.Values;
			if values.HealRate then
				cHealRate = cHealRate + statusClass.Values.HealRate;
			end
			if values.MaxHealthOverCharge then
				cMaxHealthOverCharge = cMaxHealthOverCharge + values.MaxHealthOverCharge;
			end
		end

		if cMaxHealthOverCharge > 0 then
			healthComp:SetMaxHealth(cMaxHealth + cHealthPoints + cMaxHealthOverCharge);
			
		elseif healthComp.MaxHealth > cMaxHealth + cHealthPoints then
			healthComp:SetMaxHealth(healthComp.MaxHealth - 0.1);

		else
			healthComp:SetMaxHealth(cMaxHealth + cHealthPoints + cMaxHealthOverCharge);

		end

		if playerClass.StatusComp:GetOrDefault("Wounded") then
			cHealRate = 0;
		end
		properties.HealRate = cHealRate;
	end

	function meta.OnCharacterAdded(character: Model)
		if character == nil then return end;
		meta.CharacterGarbage:Destruct();
		healthComp:Reset();

		Debugger:Log("Character", playerInstance.Name,"spawned. ", character:GetFullName());

		local cache = {
			ColdBreathParticles = nil;
			WaterBubblesParticles = nil;
		};

		character:SetAttribute("PlayerCharacter", playerInstance.Name);

		playerClass.Character = character;
		playerClass.Humanoid = character:WaitForChild("Humanoid") :: Humanoid;
		playerClass.RootPart = character:WaitForChild("HumanoidRootPart", 20) :: BasePart;
		playerClass.Head = character:WaitForChild("Head") :: BasePart;
		playerClass.CharacterModule = shared.require(character:WaitForChild("CharacterModule") :: ModuleScript);

		playerClass.Humanoid:SetStateEnabled(Enum.HumanoidStateType.Dead, false);
		meta.DeathDebounce = false;

		local ragdollActive = false;
		local function onStateChanged(oldState: Enum.HumanoidStateType, newState: Enum.HumanoidStateType)
			playerClass.CurrentState = newState;

			if RunService:IsClient() and playerInstance ~= localPlayer then
				return;
			end

			playerClass.CharacterModule.UpdateBodyObjects();
			if healthComp.IsDead then return end;

			if cache.ColdBreathParticles == nil then
				cache.ColdBreathParticles = playerClass.Head:WaitForChild("MouthAttachment"):WaitForChild("ColdBreath");
			end

			if cache.WaterBubblesParticles == nil then
				cache.WaterBubblesParticles = playerClass.Head:WaitForChild("MouthAttachment"):WaitForChild("WaterBubbles");
			end

		end

		meta.Garbage:Tag(character:GetAttributeChangedSignal("Ragdoll"):Connect(function()
			playerClass.CharacterModule.UpdateBodyObjects();
		end))

		meta.Garbage:Tag(playerClass.Humanoid.StateChanged:Connect(onStateChanged));
		onStateChanged(Enum.HumanoidStateType.Running, Enum.HumanoidStateType.Running);
		PlayerService.OnPlayerSpawn:Fire(playerClass);

		if RunService:IsClient() then
			if playerInstance == localPlayer then
				-- Is local client;

			end

			if playerInstance ~= localPlayer then
				-- Is other player client;

				return
			end;

		end


		function playerClass.OnDeathServer()
			table.clear(cache);
			if RunService:IsClient() then return end;
			Debugger:Log(playerInstance," died.");

			properties.Ragdoll = 1;
			playerClass.Humanoid.PlatformStand = true;

			local deathTime = tick();
			task.spawn(function()
				while tick()-deathTime <= game.Players.RespawnTime do
					task.wait(0.1);
				end

				while game.Players:GetAttribute("AutoRespawn") ~= true do
					task.wait(0.1);
				end
				if meta.CancelAutoRespawn == true then
					Debugger:Warn("AutoRespawn cancelled.");
					return;
				end

				playerClass:Spawn();
			end)

			PlayerService.OnPlayerDied:Fire(playerClass);

			properties.InBossBattle = nil;

			modOnGameEvents:Fire("OnPlayerDied", playerInstance);

			if properties.Pvp then
				local targetName = properties.Pvp.InDuel;
				local targetPlayer = targetName and PlayerService.Players[targetName];
				if targetPlayer then
					shared.Notify(game.Players:GetPlayers(), targetName.." has won a duel against "..playerClass.Name.."!", "Important");
					targetPlayer.Properties.Pvp = nil;
				end;
				properties.Pvp = nil;
			end;

		end

		local function onHealthChangedServer(damageData: DamageData?)
			if not game.Players:IsAncestorOf(playerInstance) then return end;

			if RunService:IsServer() then
				if PlayerService.SkillTree and healthComp.CurHealth > 1 then
					PlayerService.SkillTree:TriggerSkills(playerInstance, "OnHealthChange");
				end
			end
		end

		local function healthStatusCycle(currentTick, tickPack)
			if tickPack.ms100 ~= true then return end;

			meta:UpdateHealthStats();

			local activeHealRate = properties.HealRate;
			if math.abs(activeHealRate) == 0 then return end;
			healthComp:TakeDamage(DamageData.new{
				Damage = -activeHealRate;
				DamageType = "Heal";
			});
		end

		local function cycleUpdateArmor(currentTick, ms100, ms1000)
			if ms100 then
				local cArmorRate = configurations.ArmorRate;

				if healthComp.IsDead then
					local armorRegenDelay = math.max(configurations.ArmorRegenDelay, 1)

					local hasNotTakenDamageInDelay = healthComp.LastDamageTaken == nil 
						or currentTick-healthComp.LastDamageTaken >= armorRegenDelay;
					local hasNotTakenArmorDamageInDelay = healthComp.LastArmorDamageTaken == nil 
						or currentTick-healthComp.LastArmorDamageTaken >= armorRegenDelay;

					if hasNotTakenDamageInDelay and hasNotTakenArmorDamageInDelay then
						local armorBreakStatus: StatusClassInstance = statusComp:GetOrDefault("ArmorBreak");
						if armorBreakStatus ~= nil then
							cArmorRate = 0;
						end

						healthComp:SetArmor(healthComp.CurArmor + cArmorRate);
					end
				end

				local cArmorPoints = (configurations.ArmorPoints or 0);
				local cMaxArmorOverCharge = (configurations.MaxArmorOverCharge or 0);

				if modConfigurations.DisableGearMods then
					cArmorPoints = 0;
				end

				local cMaxArmor = configurations.MaxArmor;
				if cMaxArmorOverCharge > 0 then
					if healthComp.MaxArmor >= cMaxArmor + cArmorPoints + cMaxArmorOverCharge then
						healthComp.MaxArmor = cMaxArmor + cArmorPoints + cMaxArmorOverCharge;

					else
						healthComp.MaxArmor = cMaxArmor + cArmorRate;

					end

				elseif cArmorPoints > 0 and healthComp.MaxArmor > cMaxArmor + cArmorPoints then
					healthComp.MaxArmor = math.clamp(healthComp.MaxArmor - 1, cMaxArmor, cMaxArmor + cArmorPoints);

				elseif healthComp.MaxArmor > cMaxArmor + cArmorPoints then
					healthComp.MaxArmor = healthComp.MaxArmor - 1;

				else
					healthComp.MaxArmor = (cMaxArmor + cArmorPoints + cMaxArmorOverCharge);

				end

			end

			playerClass.Humanoid:SetAttribute("Armor", healthComp.CurArmor);
			playerClass.Humanoid:SetAttribute("MaxArmor", healthComp.MaxArmor);
		end


		-- Init Variables OnCharAdd;
		meta.BorrowedTime = nil;
		playerClass.CurrentState = Enum.HumanoidStateType.None;
		playerClass.Invisible = false;

		-- Connect signals;
		meta.Garbage:Tag(playerClass.HealthComp.OnHealthChanged:Connect(onHealthChangedServer));
		meta.Garbage:Tag(playerClass.Humanoid.Seated:Connect(function(active, seatPart)
			if seatPart then
				local weld = seatPart:FindFirstChild("SeatWeld");
				if weld then
					weld.C0 = CFrame.new(0, -1.1, 0) * CFrame.Angles(math.rad(-90), 0, 0);
				end
			end
		end))

		-- Fire events;
		onHealthChangedServer();
		playerClass.OnCharacterSpawn:Fire(playerClass.Character :: Model);

		if RunService:IsServer() then
			character:SetAttribute("StatusCompId", statusComp.Id);

			CollectionService:AddTag(playerClass.RootPart, "PlayerRootParts");
			character:AddTag("PlayerCharacters");
			shared.modEventService:ServerInvoke("Players_BindSpawn", {ReplicateTo={playerInstance}}, playerClass);

			meta.Garbage:Tag(character.ChildRemoved:Connect(function(child)
				if child.Name ~= "HumanoidRootPart" then return end;
				playerClass:Kill("FallenIntoVoid");
			end))

			if cache.DivingMouthpiece == nil then
				cache.DivingMouthpiece = game.ReplicatedStorage.Prefabs.Objects.DivingSuitMouthpiece:Clone();
				cache.DivingMouthpiece.Parent = character;
				cache.ShowDivingMouthpiece = 1;
			end

			local globalTemperature = workspace:GetAttribute("GlobalTemperature");

			--== Status Cycle;
			local currentTick = tick();
			local tick10s = currentTick-10;
			local tick5s = currentTick-5;
			local tick1s = currentTick-1;
			local tick500ms = currentTick-0.5;
			local tick100ms = currentTick-0.1;

			--== cycleUpdates;
			healthStatusCycle(currentTick, {ms100=true;});
			cycleUpdateArmor(currentTick, true);

			healthComp:SetArmor(healthComp.MaxArmor-1);

			--
			local lastSafeTp = tick();

			--MARK: <Server.Heartbeat>
			if meta.StatusCycle then meta.StatusCycle:Disconnect(); end
			meta.StatusCycle = RunService.Heartbeat:Connect(function(delta)
				if not game.Players:IsAncestorOf(playerInstance) then meta.StatusCycle:Disconnect(); return end;

				playerClass.Humanoid.Health = healthComp.CurHealth;
				playerClass.Humanoid.MaxHealth = healthComp.MaxHealth;

				local tickData = {};

				currentTick = tick();

				local currentState = playerClass.CurrentState;

				playerClass.IsSwimming = currentState == Enum.HumanoidStateType.Swimming;

				if currentState == Enum.HumanoidStateType.Running then --classPlayer.Humanoid.Jump
					meta.SafeCFrame = playerClass.RootPart.CFrame + Vector3.new(0, 1, 0);

				elseif playerClass.RootPart.Position.Y < -500 then
					if tick()-lastSafeTp <= 5 then
						meta.SafeCFrame = nil;
					end

					if meta.SafeCFrame == nil then
						meta.SafeCFrame = meta.SpawnCFrame;
					end
					lastSafeTp = tick();

					if meta.SafeCFrame then
						playerClass:SetCFrame(meta.SafeCFrame);
					end
				end


				local ms1000, ms500, ms100 = false, false, false;
				local s10, s5 = false, false;
				if currentTick-tick100ms >= 0.1 then
					tick100ms = currentTick;
					ms100 = true;

					if currentTick-tick500ms >= 0.5 then
						tick500ms = currentTick;
						ms500 = true;

						if currentTick-tick1s >= 1 then
							tick1s = currentTick;
							ms1000 = true;

							if currentTick-tick5s >= 5 then
								tick5s = currentTick;
								s5 = true;

								if currentTick-tick10s >= 10 then
									tick10s = currentTick;
									s10 = true;
								end
							end
						end
					end
				end

				tickData.Delta = delta;
				tickData.ms1000 = ms1000;
				tickData.ms500 = ms500;
				tickData.ms100 = ms100;
				tickData.s10 = s10;
				tickData.s5 = s5;

				if ms1000 then
					local playerWarmth = globalTemperature + (configurations.Warmth or 0);

					if cache.LastWarmthOffset == nil then cache.LastWarmthOffset = 0 end;
					local newTempOffset = properties.TemperatureOffset:Get();
					if math.abs(newTempOffset-cache.LastWarmthOffset) <=0.1 then
						cache.LastWarmthOffset = newTempOffset;
					else
						cache.LastWarmthOffset = modMath.Lerp(cache.LastWarmthOffset, properties.TemperatureOffset:Get(), 0.4);
					end
					playerWarmth = playerWarmth + cache.LastWarmthOffset;

					if playerClass.IsSwimming then
						playerWarmth = playerWarmth -10;
					end

					playerWarmth = math.floor(playerWarmth*10)/10
					playerInstance:SetAttribute("Warmth", playerWarmth);

					if playerWarmth <= 10 then
						if properties.TooCold == nil or properties.TooCold.Amount ~= playerWarmth then
							statusComp:Apply("TooCold", {
								Values={
									Amount=playerWarmth;
								}
							});
						end

						if playerWarmth < 0 then
							healthComp:TakeDamage(DamageData.new{
								Damage = healthComp.MaxHealth * 0.025;
								DamageType = "IgnoreArmor";
								TargetPart = playerClass.RootPart;
							});
						end

					elseif playerWarmth > 40 then
						if properties.TooHot == nil or properties.TooHot.Amount ~= playerWarmth then
							statusComp:Apply("TooHot", {
								Values={
									Amount=playerWarmth;
								}
							});
						end

						if playerWarmth > 50 then
							healthComp:TakeDamage(DamageData.new{
								Damage = healthComp.MaxHealth * 0.025;
								DamageType = "IgnoreArmor";
								TargetPart = playerClass.RootPart;
							});
						end

					else
						if properties.TooCold then
							statusComp:Apply("TooCold", nil);
						end
						if properties.TooHot then
							statusComp:Apply("TooHot", nil);
						end
					end

					if properties.Freezing then
						if cache.ColdBreathParticles then
							cache.ColdBreathParticles:Emit(math.random(8, 12));
						end
					end

					playerClass.Humanoid:SetAttribute("IsSwimming", playerClass.IsSwimming);

					local layerName, _layerData = modMapLibrary:GetLayer(playerClass.RootPart.Position);
					playerInstance:SetAttribute("Location", layerName);

				end

				if ms500 then
					local headInMaterial = nil;
					if playerClass.IsSwimming then
						local headpos = playerClass.Head.Position + Vector3.new(0, 1, 0);
						local headRegion = Region3.new(headpos, headpos):ExpandToGrid(4);
						local mats, _occs = workspace.Terrain:ReadVoxels(headRegion, 4);
						headInMaterial = mats and mats[1][1][1] or nil;
					end

					properties.IsUnderWater = headInMaterial and headInMaterial == Enum.Material.Water or false;
					playerClass.Humanoid:SetAttribute("IsUnderWater", properties.IsUnderWater);
				end

				healthStatusCycle(currentTick, tickData);
				cycleUpdateArmor(currentTick, ms100, ms1000);

				if ms100 then
					local cOxygen = configurations.Oxygen;
					if properties.Oxygen == nil or character:FindFirstChild("SpawnProtection") then
						properties.Oxygen = cOxygen;
					end
					local cOxygenDrainReduction = configurations.OxygenDrainReduction;
					if cache.DivingMouthpiece and cache.DivingMouthpiece:FindFirstChild("Handle") then
						local handle = cache.DivingMouthpiece.Handle;

						if cOxygenDrainReduction then
							cache.ShowDivingMouthpiece = properties.IsUnderWater and 0 or 1;
						else
							cache.ShowDivingMouthpiece = 1; -- set transparency;
						end
						if cache.ShowDivingMouthpiece ~= handle.Transparency then
							handle.Transparency = cache.ShowDivingMouthpiece;

							local oLinkL = handle.OLinkL;
							local oLinkR = handle.OLinkR;
							local ropeL = handle.RopeL;
							local ropeR = handle.RopeR;

							ropeL.Attachment0 = oLinkL
							ropeR.Attachment0 = oLinkR;

							local suitLinkL = character:FindFirstChild("OxygenLinkL", true);
							if suitLinkL then
								ropeL.Attachment1 = suitLinkL;
							end
							local suitLinkR = character:FindFirstChild("OxygenLinkR", true);
							if suitLinkR then
								ropeR.Attachment1 = suitLinkR;
							end

							ropeL.Visible = handle.Transparency == 0;
							ropeR.Visible = handle.Transparency == 0;
						end
					end

					if properties.IsUnderWater then
						local oxygenDrainReduction = math.clamp(1 - (cOxygenDrainReduction or 0), 0.01, 1);

						local amt = (0.5 * oxygenDrainReduction);

						if cache.WaterBubblesParticles and ms1000 then
							cache.WaterBubblesParticles:Emit(math.ceil(amt*math.random(12, 14)));
						end

						if cOxygen > 0 then
							properties.Oxygen = math.clamp(
								properties.Oxygen - amt, 
								0, 
								cOxygen
							);
						end

						if properties.Oxygen <= 0 then
							local drowningStatus: StatusClassInstance = statusComp:GetOrDefault("Drowning");
							if drowningStatus == nil then
								statusComp:Apply("Drowning", {});
							end
						end

						if properties.Wounded and properties.Ragdoll ~= 1 then
							statusComp:Apply("Ragdolling", {});
							
						end

					else
						local drowningStatus: StatusClassInstance = statusComp:GetOrDefault("Drowning");
						if drowningStatus then
							statusComp:Apply("Drowning", nil);
						end

						local conOxygenRecoveryIncrease = 1+(configurations.OxygenRecoveryIncrease or 0);
						properties.Oxygen = math.clamp(
							properties.Oxygen + (2 * conOxygenRecoveryIncrease), 
							0, 
							math.max(cOxygen, 1)
						);
					end
				end

				playerClass.Humanoid:SetAttribute("Oxygen", properties.Oxygen);
				playerClass.Humanoid:SetAttribute("MaxOxygen", configurations.Oxygen);
				playerClass.Humanoid:SetAttribute("LastDamageDealt", playerClass.LastDamageDealt);

				-- MARK: StatusComp process;
				local isPlayerInvisible = false;
				playerClass.StatusComp:Process(function(key, statusClass: StatusClassInstance, processData: anydict)
					if statusClass.BindTickUpdate then
						statusClass:BindTickUpdate(tickData);
					end

					if statusClass.Values.Invisible then isPlayerInvisible = true; end;
				end);

				if properties.IsInvisible ~= isPlayerInvisible and playerInstance.Character then
					properties.IsInvisible = isPlayerInvisible;
					playerInstance.Character:SetAttribute("IsInvisible", properties.IsInvisible);

					for _, obj in pairs(playerInstance.Character:GetDescendants()) do
						if (obj:IsA("BasePart") or obj:IsA("Decal")) then
							local isInATool = obj.Parent:IsA("Model") and obj.Parent.PrimaryPart and obj.Parent.PrimaryPart.Name == "Handle"
								or obj.Parent.Parent:IsA("Model") and obj.Parent.Parent.PrimaryPart and obj.Parent.Parent.PrimaryPart.Name == "Handle";

							if not isInATool and obj.Parent.Name ~= "DisguiseModel" and obj.Parent.Parent.Name ~= "DisguiseModel"
								and obj.Name ~= "HumanoidRootPart" and obj.Name ~= "CollisionRootPart" and obj:GetAttribute("CustomTransparency") ~= true then

								local invisValue = obj:GetAttribute("InvisibleValue") or 1;

								if obj:GetAttribute("ToggleClothing") ~= false then
									obj.Transparency = properties.IsInvisible and invisValue or 0;
								else
									obj.Transparency = 1;
								end

								if obj:IsA("BasePart") and not properties.IsInvisible then
									obj.Material = Enum.Material.Plastic;
								end
							end

						elseif obj.Name == "NameDisplay" then
							obj:SetAttribute("CharacterIsInvisible", properties.IsInvisible);

						end
					end

					if not properties.IsInvisible then
						modCustomizeAppearance.RefreshIndex(playerInstance.Character);
					end
				end

				if properties.Ragdoll ~= 0 then
					if ragdollActive == false then
						ragdollActive = true;
					end

				else
					if ragdollActive == true then
						ragdollActive = false;
					end

				end

				shared.coreCall(Player, "_status_tick_update", playerClass, tickData);
			end)
			--MARK: </Server.Heartbeat>
			
			local modProfile = shared.modProfile;
			local profile = modProfile:Get(playerInstance);
			local activeSave = profile and profile:GetActiveSave();

			if activeSave then
				local statusSave = activeSave.StatusSave;
				statusSave:ApplyEffects();
			end

		else
			--==  Client side character added;
			local currentTick = tick();
			local tick10s = currentTick-10;
			local tick5s = currentTick-5;
			local tick1s = currentTick-1;
			local tick500ms = currentTick-0.5;
			local tick100ms = currentTick-0.1;

			--MARK: <Client.Heartbeat>
			if meta.StatusCycle then meta.StatusCycle:Disconnect(); end
			meta.StatusCycle = RunService.Heartbeat:Connect(function(delta)
				currentTick = tick();

				local tickData = {};

				local ms1000, ms500, ms100 = false, false, false;
				local s10, s5 = false, false;
				if currentTick-tick100ms >= 0.1 then
					tick100ms = currentTick;
					ms100 = true;

					if currentTick-tick500ms >= 0.5 then
						tick500ms = currentTick;
						ms500 = true;

						if currentTick-tick1s >= 1 then
							tick1s = currentTick;
							ms1000 = true;

							if currentTick-tick5s >= 5 then
								tick5s = currentTick;
								s5 = true;

								if currentTick-tick10s >= 10 then
									tick10s = currentTick;
									s10 = true;
								end
							end
						end
					end
				end

				tickData.Delta = delta;
				tickData.ms1000 = ms1000;
				tickData.ms500 = ms500;
				tickData.ms100 = ms100;
				tickData.s10 = s10;
				tickData.s5 = s5;

				playerClass.StatusComp:Process(function(key: string, statusClass: StatusClassInstance, processData: anydict)
					if statusClass.BindTickUpdate then
						statusClass:BindTickUpdate(tickData);
					end
				end);

			end)
			--MARK: </Client.Heartbeat>

			remotePlayerClass:FireServer("SpawnSync");
			task.spawn(function()
				wieldComp:RefreshCharacterModifiers();
			end)
		end

		properties.Ragdoll = 0;

		shared.coreCall(Player, "_character_added", playerClass, character);
		-- OnCharacterAdded End
	end

	function meta:SetCFrame(cframe: CFrame)
		(shared :: any).modAntiCheatService:Teleport(playerInstance, cframe);
	end

	function meta.OnPlayerTeleport(teleportState, placeId, spawnName)
		Debugger:Log("Player (",playerClass.Name,") is teleporting to: ", modBranchConfigs.GetWorldName(placeId));
		playerClass.IsTeleporting = true;
		playerClass.TeleportPlaceId = placeId;
	end

	function meta:GetCharacter()
		return playerInstance.Character;
	end

	function meta:GetCharacterChild(name)
		local char = self:GetCharacter();
		local obj = char and char:FindFirstChild(name) or nil;
		if obj then
			if name == "HumanoidRootPart" then
				self.RootPart = obj;
			elseif name == "Humanoid" or name == "Head" then
				self[name] = obj;
			end
			return obj;
		end
		return;
	end

	function meta:SyncProperties(keys)
		if RunService:IsClient() then return end;

		for a=1, #keys do
			local k = keys[a];
			remotePlayerClass:FireClient(playerInstance, playerInstance.Name, "SetProperties", k, self.Properties[k]);
		end
	end

	function meta:Destroy()
		if PlayerService.Players[self.Name] == nil then return end;
		if meta.StatusCycle then meta.StatusCycle:Disconnect(); end

		self.Configurations:Destroy();
		self.OnCharacterSpawn:Destroy();
		self.OnDamageTaken:Destroy();
		self.OnPropertyChanged:Destroy();
		self.OnIsDeadChanged:Destroy();

		self.Properties.TemperatureOffset:Destroy();
		meta.Garbage:Destruct();

		local wieldComp: WieldComp = self.WieldComp;
		wieldComp:Destroy();
		
		self.Character = nil;
		self.Humanoid = nil;
		self.RootPart = nil;
		self.Head = nil;
		self.CharacterModule = nil;

		PlayerService.Players[self.Name] = nil;
		table.clear(self);
		table.clear(meta);
	end

	function meta:CastGroundRay(distance)
		if self.RootPart == nil then
			return nil, Vector3.zero, Vector3.zero, nil;
		end

		distance = distance or 16;

		local raycastParams = RaycastParams.new();
		raycastParams.FilterType = Enum.RaycastFilterType.Include;
		raycastParams.IgnoreWater = true;
		raycastParams.FilterDescendantsInstances = {workspace.Environment; workspace.Terrain};
		raycastParams.CollisionGroup = "Raycast";

		local newOrigin = self.RootPart.Position;
		local direction = Vector3.new(0, -1, 0);

		local raycastResult = workspace:Raycast(newOrigin, direction*distance, raycastParams);

		local rayBasePart, rayPoint, rayNormal, rayMaterial;
		if raycastResult then
			rayBasePart = raycastResult.Instance;
			rayPoint = raycastResult.Position;
			rayNormal = raycastResult.Normal;
			rayMaterial = raycastResult.Material;

		else
			rayPoint = newOrigin + direction*distance;

		end

		return rayBasePart, rayPoint, rayNormal, rayMaterial;
	end


	--MARK: Signal Connections
	meta.OnPropertyChanged:Connect(function(k, v, ov)
		if RunService:IsClient() then return end;

		meta:SyncProperty(k);

		if k == "Ragdoll" then
			playerClass.Character:SetAttribute("Ragdoll", v);
		end

		-- 	if statusClass.PresistUntilExpire and statusClass.Expires then
		-- 		local profile = shared.modProfile:Get(playerInstance);
		-- 		local activeSave = profile:GetActiveSave();
		-- 		local statusSave = activeSave.StatusSave;

		-- 		statusSave:Save(statusComp.List);
		-- 	end

		-- 	modOnGameEvents:Fire("OnStatusSet", statusClass);
	end)

	healthComp.OnIsDeadChanged:Connect(function(...)
		meta.OnIsDeadChanged:Fire(...);

		if not healthComp.IsDead then return end;
		if playerClass.OnDeathServer then
			playerClass:OnDeathServer();
		end
	end)

	meta.Garbage:Tag(playerInstance.OnTeleport:Connect(playerClass.OnPlayerTeleport));

	if playerInstance.Character then
		task.spawn(playerClass.OnCharacterAdded, playerInstance.Character);
	end;
	meta.Garbage:Tag(playerInstance.CharacterAdded:Connect(playerClass.OnCharacterAdded));

	function meta.OnCharacterRemoving(character)
		shared.coreCall(Player, "_character_removing", playerClass, character);
	end

	playerInstance.CharacterRemoving:Connect(meta.OnCharacterRemoving);

	shared.coreCall(Player, "_new", playerClass);
	
	return playerClass;
end


-- MARK: .onRequire
function Player.onRequire(playerService)
	PlayerService = playerService;
	
	remotePlayerClass = modRemotesManager:Get("PlayerClass");
	remoteDamagePacket = modRemotesManager:Get("DamagePacket");
	remoteToolHandler = modRemotesManager:Get("ToolHandler");

	if RunService:IsClient() then
		localPlayer = game.Players.LocalPlayer;

	else
		modEquipmentSystem = shared.require(game.ServerScriptService.ServerLibrary.EquipmentSystem);
		modOnGameEvents = shared.require(game.ServerScriptService.ServerLibrary.OnGameEvents);
		
		function remoteToolHandler.OnServerInvoke(player, action, paramPacket)
			if remoteToolHandler:Debounce(player) then return {}; end;
			
			paramPacket = paramPacket or {};
			paramPacket.ClientInvoked = true;
		
			local playerClass: PlayerClass = shared.modPlayers.get(player);
			if playerClass == nil then return end;

			if action == "equip" then
				return playerClass.WieldComp:Equip(paramPacket);
			else
				return playerClass.WieldComp:Unequip(paramPacket);
			end
		end
		
	end
	
	shared.modCommandsLibrary.bind{
		["equipment"]={
			Permission = shared.modCommandsLibrary.PermissionLevel.DevBranch;
			Description = [[Equipment commands.

/equipment <u>p</u>rint<u>m</u>odifier<u>c</u>hain [siid] -- Print equipped/[siid] modifier chain
/equipment <u>p</u>rint<u>c</u>haracter<u>c</u>hain -- Print modifier chain equipped on character.
/equipment <u>l</u>ist<u>c</u>haracter<u>c</u>hain -- List modifiers active on character.
/equipment <u>g</u>et<u>i</u>tem<u>m</u>odifier [modifierId] -- Print item modifier.
/equipment <u>l</u>ist<u>a</u>ctive<u>e</u>quipments -- List active equipments.
/equipment <u>l</u>ist<u>a</u>ctive<u>m</u>odifiers -- List active modifiers.
/equipment listcombined [tags...] -- List combination for specific calculations.
			]];

			RequiredArgs = 0;
			UsageInfo = "/equipment cmd";
			Function = function(player, args)
				local playerClass: PlayerClass = PlayerService.get(player);
				local actionId = args[1];

				if actionId == "printmodifierchain" or actionId == "pmc" then
					local siid = args[2];

					if siid == nil and playerClass.WieldComp.EquipmentClass == nil then
						shared.Notify(player, "You are not equipping any tool.", "Negative");
						return;
					end
					if siid == nil then
						siid = playerClass.WieldComp.Siid;
					end

					shared.Notify(player, `Printing for item: {siid}`, "Inform");

					local equipmentClass = playerClass.WieldComp:GetEquipmentClass(siid);
					local configs = equipmentClass.Configurations;

					local strList = configs:PrintChain();
					for a=1, #strList do
						shared.Notify(player, strList[a], "Inform");
					end
					Debugger:Log(`Chain item {siid}:`, strList);

				elseif actionId == "printcharacterchain" or actionId == "pcc" then
					local configs = playerClass.Configurations;
					
					local strList = configs:PrintChain();
					for a=1, #strList do
						shared.Notify(player, strList[a], "Inform");
					end
					Debugger:Log("Chain:", strList);

				elseif actionId == "listcharacterchain" or actionId == "lcc" then
					local configs = playerClass.Configurations;

					local list = {};
					for a=1, #configs.Modifiers do
						local modifier = configs.Modifiers[a];
						local tags = {};
						for t,_ in pairs(modifier.Tags) do table.insert(tags, t) end;

						local mhint = "";
						if modifier.ItemModStorageItem then 
							mhint = modifier.ItemModStorageItem.ItemId;
						elseif modifier.EquipmentClass then
							mhint = modifier.EquipmentClass.ItemId;
						end

						table.insert(list, `{modifier.Id} ({mhint}): {table.concat(tags, ", ")}`);
					end
					local msg = `Character Modifiers:\n-> {table.concat(list, "\n-> ")}`;
					shared.Notify(player, msg, "Inform");
					Debugger:Log("Character Modifiers:", list);

				elseif actionId == "getitemmodifier" or actionId == "gim" then
					local modifierId = args[2];
					local itemModifierList = playerClass.WieldComp.ItemModifierList;

					local matches = modStrings.MatchStringFromDict(modifierId, itemModifierList);

					if #matches > 1 then
						shared.Notify(player, `{#matches} matches: {table.concat(matches, ", ")}`, "Inform");
						return;
					end
					if #matches <= 0 then
						shared.Notify(player, `No matches for {modifierId}`, "Negative");
						return;
					end
					if #matches ~= 1 then return end;

					local itemModifier = itemModifierList[matches[1]];

					local msg = `ItemModifier {matches[1]}:\n{modStrings.TableToString(itemModifier)}`;
					shared.Notify(player, msg, "Inform");
					Debugger:Log("ItemModifier", matches[1],"=",itemModifier);

				elseif actionId == "listactiveequipments" or actionId == "lae" then
					local equipmentList = playerClass.WieldComp.EquipmentClassList;
					local list = {};

					for siid, equipmentClass: EquipmentClass in pairs(equipmentList) do
						if equipmentClass.Enabled == false then continue end;

						table.insert(list, `{siid} ({equipmentClass.Class})`);
					end

					local msg = `Active Equipment: {table.concat(list, ", ")}`;
					shared.Notify(player, msg, "Inform");
					Debugger:Log("Active Equipments:", list);

				elseif actionId == "listactivemodifiers" or actionId == "lam" then

					local itemModifierList = playerClass.WieldComp.ItemModifierList;
					local list = {};

					for siid, itemModifier: ItemModifierInstance in pairs(itemModifierList) do
						if itemModifier.Enabled == false then continue end;

						table.insert(list, `{siid} ({itemModifier.Script})`);
					end

					local msg = `Active ItemModifiers: {table.concat(list, ", ")}`;
					shared.Notify(player, msg, "Inform");
					Debugger:Log("Active ItemModifiers:", list);

				elseif actionId == "listcombined" then
					local tags = args[2];

				elseif actionId == "recalculate" then
					playerClass.Configurations:Calculate();
					
				end

				return true;
			end;
			ClientFunction = function(player, args)
				Debugger:Warn("client /equipment", player, args);
				local playerClass: PlayerClass = PlayerService.get(player);
				local actionId = args[1];

				if actionId == "printcharacterchain" or actionId == "pcc" then
					local configs = playerClass.Configurations;
					
					local strList = configs:PrintChain();
					for a=1, #strList do
						shared.Notify(player, strList[a], "Inform");
					end
					Debugger:Log("Chain:", strList);
					
				elseif actionId == "listcharacterchain" or actionId == "lcc" then
					local configs = playerClass.Configurations;

					local list = {};
					for a=1, #configs.Modifiers do
						local modifier = configs.Modifiers[a];
						local tags = {};
						for t,_ in pairs(modifier.Tags) do table.insert(tags, t) end;

						local mhint = "";
						if modifier.ItemModStorageItem then 
							mhint = modifier.ItemModStorageItem.ItemId;
						elseif modifier.EquipmentClass then
							mhint = modifier.EquipmentClass.ItemId;
						end

						table.insert(list, `{modifier.Id} ({mhint}): {table.concat(tags, ", ")}`);
					end
					local msg = `Character Modifiers:\n-> {table.concat(list, "\n-> ")}`;
					shared.Notify(player, msg, "Inform");
					Debugger:Log("Character Modifiers:", list);

				elseif actionId == "listactivemodifiers" or actionId == "lam" then
					local itemModifierList = playerClass.WieldComp.ItemModifierList;
					local list = {};

					for siid, itemModifier: ItemModifierInstance in pairs(itemModifierList) do
						if itemModifier.Enabled == false then continue end;

						table.insert(list, `{siid} ({itemModifier.Script})`);
					end

					local msg = `Active ItemModifiers: {table.concat(list, ", ")}`;
					shared.Notify(player, msg, "Inform");
					Debugger:Log("Active ItemModifiers:", list);

				elseif actionId == "listactiveequipments" or actionId == "lae" then
					local equipmentList = playerClass.WieldComp.EquipmentClassList;
					local list = {};

					for siid, equipmentClass: EquipmentClass in pairs(equipmentList) do
						if equipmentClass.Enabled == false then continue end;

						table.insert(list, `{siid} ({equipmentClass.Class})`);
					end

					local msg = `Active Equipment: {table.concat(list, ", ")}`;
					shared.Notify(player, msg, "Inform");
					Debugger:Log("Active Equipments:", list);
					
				end

				return;
			end;
		};
	};
end

return Player;