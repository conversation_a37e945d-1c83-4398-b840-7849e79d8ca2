local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");
local UserInputService = game:GetService("UserInputService");

local modToolHandler = shared.require(game.ReplicatedStorage.Library.ToolHandler);
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);
local modItemsLibrary = shared.require(game.ReplicatedStorage.Library.ItemsLibrary);
local modKeyBindsHandler = shared.require(game.ReplicatedStorage.Library.KeyBindsHandler);
local modClientGuis = shared.require(game.ReplicatedStorage.PlayerScripts.ClientGuis);

local remoteToolInputHandler = modRemotesManager:Get("ToolInputHandler");
--==
local toolHandler: ToolHandler = modToolHandler.new();

function toolHandler.Setup(handler: ToolHandlerInstance)

	handler:LoadWieldConfig();
end

if RunService:IsClient() then -- MARK: Client
	local localPlayer = game.Players.LocalPlayer;
	local modData = shared.require(localPlayer:WaitForChild("DataModule") :: ModuleScript);

	function toolHandler.ClientEquip(handler: ToolHandlerInstance)
		local playerClass: PlayerClass = shared.modPlayers.get(localPlayer);
		local modCharacter = modData:GetModCharacter();
		
		local mouseProperties = modCharacter.MouseProperties;
		local characterProperties = modCharacter.CharacterProperties;

		local toolPackage = handler.ToolPackage;
		local equipmentClass: EquipmentClass = handler.EquipmentClass;
		local toolAnimator: ToolAnimator = handler.ToolAnimator;
		local storageItem: StorageItem = handler.StorageItem;

		local itemId = storageItem.ItemId;

		local configurations = toolPackage.Configurations;
		local properties = toolPackage.Properties;
	
		local itemLib = modItemsLibrary:Find(itemId);
		
		local animations = toolPackage.Animations;
		local audio = toolPackage.Audio;

		local toolModel = handler.Prefabs[1];
		local handle = toolModel and toolModel:WaitForChild("Handle") or nil;
		
		properties.IsActive = false;
		
		toolAnimator:LoadAnimations(animations, toolPackage.DefaultAnimatorState, handler.Prefabs);
		toolAnimator:Play("Core");
		toolAnimator:Play("Load");
		
		if configurations.HideCrosshair ~= nil then
			characterProperties.HideCrosshair = configurations.HideCrosshair;
		else
			characterProperties.HideCrosshair = true;
		end
		if configurations.UseViewmodel == false then
			characterProperties.UseViewModel = false;
		end
		if configurations.CustomViewModel then
			characterProperties.CustomViewModel = configurations.CustomViewModel;
		end
		
		local useTimer = tick();
		local function PrimaryFireRequest(isActive, packet)
			if not characterProperties.CanAction then return end;
			if configurations.UseCooldown and tick()-useTimer <= configurations.UseCooldown then return end;
			useTimer = tick();
			
			properties.IsActive = not properties.IsActive;
			
			if isActive then
				properties.IsActive = isActive;
			end
			
			if properties.IsActive then
				toolAnimator:Play("Use");
				
				if configurations.UseFOV then
					characterProperties.FieldOfView = configurations.UseFOV;
				end
			else
				toolAnimator:Stop("Use");
				characterProperties.FieldOfView = nil;

			end
	
			local inputPacket = {
				Action="action";
				ActionIndex=1;
				Siid=storageItem.ID;
				IsActive=properties.IsActive;
				ClientPacket=(toolPackage.ClientPrimaryFire and toolPackage.ClientPrimaryFire(handler) or nil);
			};
			if packet then
				for k, v in pairs(packet) do
					inputPacket[k] = v;
				end
			end
			remoteToolInputHandler:FireServer(modRemotesManager.Compress(inputPacket));
		end
		
		local function SecondaryFireRequest()
			if not characterProperties.CanAction then return end;
	
			remoteToolInputHandler:FireServer(modRemotesManager.Compress({
				Action="action";
				ActionIndex=2;
				Siid=storageItem.ID;
				IsActive=properties.IsActive;
				ClientPacket=(toolPackage.ClientSecondaryFire and toolPackage.ClientSecondaryFire(handler) or nil);
			}));
		end
		
		local function ItemPromptRequest()
			if not characterProperties.CanAction then return end;
			if characterProperties.ActiveInteract ~= nil and characterProperties.ActiveInteract.CanInteract and characterProperties.ActiveInteract.Reachable then return end;
			
			if toolPackage.ClientItemPrompt then
				toolPackage.ClientItemPrompt(handler);
			end
		end
		
		if configurations.DisableMovement then
			characterProperties.CanMove = false;
		end
		

		handler.Binds["KeyFire"] = PrimaryFireRequest;
		handler.Binds["KeyFocus"] = SecondaryFireRequest;
		handler.Binds["KeyInteract"] = ItemPromptRequest;

		if toolPackage.ClientEquip then
			toolPackage.ClientEquip(handler);
		end
		if toolPackage.ClientItemPrompt then
			if UserInputService.KeyboardEnabled then
				local hintString = configurations.ItemPromptHint or (" to use "..itemLib.Name)
				hintString = "Press ["..modKeyBindsHandler:ToString("KeyInteract").."]"..hintString;

				modClientGuis.hintWarning(hintString, function(element)
					element.TextColor = Color3.fromRGB(255, 255, 255);
				end);
			end
			
			local touchControlElement: InterfaceElement = modClientGuis.getElement("TouchControlsElement");
			if touchControlElement then
				touchControlElement.ItemPromptIcon = itemLib.Icon;
				touchControlElement.ItemPromptClick = ItemPromptRequest;
			end

		elseif toolPackage.ToolWindow then
			local quickButton = modClientGuis.ActiveInterface:NewQuickButton(itemLib.Name, nil, itemLib.Icon);
			quickButton.Name = toolPackage.ToolWindow;
			quickButton.LayoutOrder = 999;
			quickButton:WaitForChild("BkFrame").Visible = true;
			modClientGuis.ActiveInterface:ConnectQuickButton(quickButton, "KeyInteract");
			
			handler.Garbage:Tag(function()
				quickButton:Destroy();
				modClientGuis.toggleWindow(toolPackage.ToolWindow, false);
			end);

		end
	end

	function toolHandler.ClientUnequip(handler: ToolHandlerInstance)
		local modCharacter = modData:GetModCharacter();
		local characterProperties = modCharacter.CharacterProperties;
		local toolPackage = handler.ToolPackage;
		local configurations = toolPackage.Configurations;
		local properties = toolPackage.Properties;

		properties.IsActive = false;

		if toolPackage.ClientUnequip then
			toolPackage.ClientUnequip(handler);
		end

		if configurations.DisableMovement then
			characterProperties.CanMove = true;
		end

		characterProperties.CustomViewModel = nil;
		characterProperties.FieldOfView = nil;
	end


elseif RunService:IsServer() then -- MARK: Server
	function toolHandler.ServerEquip(handler: ToolHandlerInstance)
		local healthComp: HealthComp = handler.CharacterClass.HealthComp;
		if healthComp.IsDead then return end;

		local toolPackage = handler.ToolPackage;

		if toolPackage.ServerEquip then
			toolPackage.ServerEquip(handler);
		end

		if handler.CharacterClass.ClassName ~= "NpcClass" then return end;

		local equipmentClass: EquipmentClass = handler.EquipmentClass;
		local toolAnimator: ToolAnimator = handler.ToolAnimator;

		local animations = toolPackage.Animations;

		toolAnimator:LoadAnimations(animations, toolPackage.DefaultAnimatorState, handler.Prefabs);
		toolAnimator:Play("Core");
	end

	function toolHandler.ActionEvent(handler: ToolHandlerInstance, packet)
		local healthComp: HealthComp = handler.CharacterClass.HealthComp;
		if healthComp.IsDead then return end;

		local toolPackage = handler.ToolPackage;

		if toolPackage.ActionEvent then
			toolPackage.ActionEvent(handler, packet);
		end
	end

	function toolHandler.InputEvent(handler: ToolHandlerInstance, inputData)
		local healthComp: HealthComp = handler.CharacterClass.HealthComp;
		if healthComp.IsDead then return end;
		
		local toolPackage = handler.ToolPackage;

		if toolPackage.InputEvent then
			toolPackage.InputEvent(handler, inputData);
		end
	end

end

return toolHandler;