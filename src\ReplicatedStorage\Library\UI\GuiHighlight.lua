local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local GuiHighlight = {
	HideBackground=nil;
}

local RunService = game:GetService("RunService");
local TweenService = game:GetService("TweenService");
local GuiService = game:GetService("GuiService");

if RunService:IsServer() then return GuiHighlight end;

local modComponents = shared.require(game.ReplicatedStorage.Library.UI.Components);

local localPlayer = game.Players.LocalPlayer;
local playerGui = localPlayer.PlayerGui;

local templateHighlighter = script:WaitForChild("UIHighlight");
local activeHighlighter = nil;

local childConns = {};
local visConns = {};
local pageIndex = 0;

local function disconnectAllConnections()
	pcall(function() RunService:UnbindFromRenderStep("GuiHighlight"); end)
	
	for obj, objConns in pairs(childConns) do
		for a=1, #objConns.Conns do
			if objConns.Conns[a] then
				objConns.Conns[a]:Disconnect();
			end
		end
	end
	childConns = {};

	for a=1, #visConns do
		if visConns[a] then
			visConns[a]:Disconnect();
		end
	end
	table.clear(visConns);
end

local tweeninfo = TweenInfo.new(0.3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true, 0);

local frameObjects = {};

local ObjectFrame = {};
ObjectFrame.__index = ObjectFrame;
function ObjectFrame.new(object, size)
	local self = {
		Object=object;
		Size=size;
	}

	local mainInterface = playerGui.MainInterface;
	
	local frame = Instance.new("Frame");
	frame.AnchorPoint = Vector2.new(0.5, 0.5);
	frame.Visible = false;
	frame.BackgroundTransparency = 1;
	frame.Name = object.Name;
	frame.Parent = mainInterface;
	
	self.Frame = frame;
	
	setmetatable(self, ObjectFrame);
	return self;
end

function ObjectFrame:Update()
	if not self.Object:IsDescendantOf(workspace) then return end
	
	local camera = workspace.CurrentCamera;
	local screenPoint, onScreen = camera:WorldToViewportPoint(self.Object.Position);
	
	if onScreen and screenPoint.Z >= 16 then
		self.Frame.Visible = true;
		self.Frame.Position = UDim2.new(0, screenPoint.X, 0, screenPoint.Y);
		self.Frame.Size = UDim2.new(0, screenPoint.Z, 0, screenPoint.Z);
		
	else
		self.Frame.Visible = false;
	end
	
end

local isBindedFrames = false;
local function refreshGuiObjectFrames()
	if not isBindedFrames then
		isBindedFrames = true;
		RunService:BindToRenderStep("GuiObjectFrames", Enum.RenderPriority.Last.Value, function()
			if #frameObjects <= 0 then
				isBindedFrames = false;
				RunService:UnbindFromRenderStep("GuiObjectFrames");
				return;
			end
			
			for a=#frameObjects, 1, -1 do
				local objectFrame = frameObjects[a];
				if objectFrame.Object:IsDescendantOf(workspace) then
					objectFrame:Update();
				else
					table.remove(frameObjects, a);
				end
			end
		end)
	end
end

function GuiHighlight.FrameWorldObject(object, size)
	table.insert(frameObjects, ObjectFrame.new(object, size));
	refreshGuiObjectFrames();
end

function GuiHighlight.Set(...)
	local player = game.Players.LocalPlayer;
	local playerGui = player.PlayerGui;
	
	pageIndex = pageIndex +1;
	local activePage = pageIndex;
	
	disconnectAllConnections();
	local params = {...};
	if #params <= 0 then
		if activeHighlighter then
			activeHighlighter:Destroy();
			activeHighlighter = nil;
		end
		
	else
		local parent = playerGui and playerGui:FindFirstChild(params[1]) or nil;
		if activeHighlighter == nil or activeHighlighter.Parent == nil then 
			activeHighlighter = templateHighlighter:Clone(); 
		end
		
		local highlightObject = {
			Text = nil;
			TextLayout = nil;
		};
		highlightObject.Pages = {};
		
		highlightObject.RefreshAll = function()
			if pageIndex ~= activePage then return end;
			
			for a=1, #highlightObject.Pages do
				highlightObject.Pages[a].Refresh();
			end
		end

		local function search(page, index, path)
			local directory = page.Directory;

			for a=index, #directory do
				local name = directory[a];
				local guiObject = path and path:FindFirstChild(name) or nil;
				
				if guiObject == nil and name:sub(1,9) == "contains:" then
					local searchName = name:sub(10, #name);
					local searchGuiObject = path and path:FindFirstChild(searchName) or nil;

					if searchGuiObject then
						name = searchGuiObject.Name;
						directory[a] = name;
						guiObject = searchGuiObject.Parent;
					end
				end

				local parentPath = path;

				if childConns[parentPath] == nil then
					local objConns = {
						Seek={};
						Conns={};
					}
					childConns[parentPath] = objConns;

					table.insert(objConns.Conns, parentPath.ChildAdded:Connect(function(c)
						if pageIndex ~= activePage then return end;
						--task.wait(0.1);

						local seekInfo = objConns.Seek[c.Name];
						if seekInfo then
							--Debugger:Log("Found ", c.Name, "parent",parentPath," ", seekInfo);
							search(seekInfo.Page, seekInfo.Index, parentPath);
						end
					end));
					table.insert(objConns.Conns, parentPath.ChildRemoved:Connect(function(c)
						if pageIndex ~= activePage then return end;
						
						local seekInfo = objConns.Seek[c.Name];
						if seekInfo then
							search(seekInfo.Page, seekInfo.Index, parentPath);
						end
					end))
				end

				childConns[parentPath].Seek[name] = {Page=page; Index=a;};

				if guiObject then
					if guiObject:IsA("GuiObject") then
						table.insert(visConns, guiObject:GetPropertyChangedSignal("Visible"):Connect(highlightObject.RefreshAll))
						
						page.GuiObjects[a] = guiObject;
						if not guiObject.Visible then page.Visible = false; end;

						if a == #directory then
							page.GuiObject = guiObject;
						end
					end
					path = guiObject;

					highlightObject.RefreshAll();
				else
					break;
				end
			end
		end
		
		local function set(...)
			local page = {Visible=true;} :: any;
			
			page.GuiObjects = {};
			page.Directory = {...};
			
			if typeof(page.Directory[#page.Directory]) == "boolean" then
				table.remove(page.Directory, #page.Directory);
				page.Required = true;
			end
			
			page.Refresh = function()
				local v = true;
				
				if page.GuiObject == nil then
					v = false;
					
				else
					for k, guiObj in pairs(page.GuiObjects) do
						if guiObj == nil then continue end

						if guiObj:IsDescendantOf(playerGui) and guiObj:IsA("GuiObject") then
							if not modComponents.IsTrulyVisible(guiObj) then 
								v = false;
								break;
							end;
						else
							page.GuiObjects[k] = nil;
						end
					end
					
				end
				
				page.Visible = v;
			end
			
			table.insert(highlightObject.Pages, page);
			search(page, 1, playerGui);
		end
		
		set(...);
		highlightObject.Next = set;
		
		activeHighlighter.Parent = parent;
		activeHighlighter.ImageColor3 = Color3.fromRGB(130, 20, 20);
		
		if GuiHighlight.HideBackground then
			for _, obj in pairs(activeHighlighter:GetChildren()) do
				if obj:IsA("GuiObject") then
					obj.Visible = false;
				end
			end
		end

		TweenService:Create(activeHighlighter, tweeninfo, {ImageColor3=Color3.fromRGB(255, 200, 200)}):Play();

		local textLabel: TextLabel = activeHighlighter:WaitForChild("TextLabel");
		textLabel.BackgroundColor3 = Color3.fromRGB(130, 20, 20);
		TweenService:Create(textLabel, tweeninfo, {BackgroundColor3=Color3.fromRGB(255, 200, 200)}):Play();

		RunService:BindToRenderStep("GuiHighlight", Enum.RenderPriority.Last.Value, function()
			if activeHighlighter ~= nil then
				local visibleExist = false;
				
				if #highlightObject.Pages > 0 then
					local guiObject = highlightObject.Pages[#highlightObject.Pages].GuiObject;
					local currentObj = guiObject;
					
					if currentObj then
						while currentObj:IsDescendantOf(playerGui) do
							currentObj = currentObj.Parent;

							if currentObj:IsA("ScrollingFrame") then
								local diff = guiObject.AbsolutePosition.Y-currentObj.AbsolutePosition.Y;
								if math.abs(diff) >= 6 then
									currentObj.CanvasPosition = currentObj.CanvasPosition + Vector2.new(0, diff/3);
								end
							end

						end
					end
				end

				for a=1, #highlightObject.Pages do
					if highlightObject.Pages[a].Required == true and not highlightObject.Pages[a].Visible then
						
						for b=1, #highlightObject.Pages do
							if b > a then
								highlightObject.Pages[b].Visible = false;
							end
						end
						
						break;
					end
				end
				
				for a=#highlightObject.Pages, 1, -1 do
					local visible, guiObject = highlightObject.Pages[a].Visible, highlightObject.Pages[a].GuiObject;
					
					if visible and guiObject and guiObject:IsDescendantOf(playerGui) then
						visibleExist = true;
						activeHighlighter.Visible = visible;
						activeHighlighter.Size = UDim2.new(0, guiObject.AbsoluteSize.X+24, 0, guiObject.AbsoluteSize.Y+24);
						activeHighlighter.Position = UDim2.new(0, guiObject.AbsolutePosition.X-12, 0, guiObject.AbsolutePosition.Y+GuiService.TopbarInset.Height-12);
						
						break;
					end
				end
				if not visibleExist then
					activeHighlighter.Visible = false;
				end

				if activeHighlighter.Visible then
					if highlightObject.Text then
						textLabel.Text = highlightObject.Text;
						textLabel.Visible = true;

						if highlightObject.TextLayout == "BLI" then
							--bottomleft
							textLabel.AnchorPoint = Vector2.new(0, 0);
							textLabel.Position = UDim2.new(0, 0, 0, 1);
						elseif highlightObject.TextLayout == "BCI" then
							--bottom center inside
							textLabel.AnchorPoint = Vector2.new(0.5, 1);
							textLabel.Position = UDim2.new(0.5, 0, 1, -1);
						end

					else
						textLabel.Visible = false;
					end
				end
			end
		end)
		
		return highlightObject;
	end

	return;
end

return GuiHighlight;