local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");
local CollectionService = game:GetService("CollectionService");

local modEventSignal = shared.require(game.ReplicatedStorage.Library.EventSignal);
local modConfigVariable = shared.require(game.ReplicatedStorage.Library.ConfigVariable);
local modGarbageHandler = shared.require(game.ReplicatedStorage.Library.GarbageHandler);
local modInfoBubbles = shared.require(game.ReplicatedStorage.Library.InfoBubbles);
local modLayeredVariable = shared.require(game.ReplicatedStorage.Library.LayeredVariable);
local modInteractables = shared.require(game.ReplicatedStorage.Library.Interactables);
local modPropertiesVariable = shared.require(game.ReplicatedStorage.Library.PropertiesVariable);
local modNpcAnimator = shared.require(game.ServerScriptService.ServerLibrary.Entity.NpcAnimator);

local modTables = shared.require(game.ReplicatedStorage.Library.Util.Tables);

local modHealthComponent = shared.require(game.ReplicatedStorage.Components.HealthComponent);
local modStatusComponent = shared.require(game.ReplicatedStorage.Components.StatusComponent);
local modWieldComponent = shared.require(game.ReplicatedStorage.Components.WieldComponent);

local DamageData = shared.require(game.ReplicatedStorage.Data.DamageData);

local NpcClass = {};
NpcClass.__index = NpcClass;
NpcClass.meta = NpcClass;

NpcClass.ClassName = "NpcClass";
NpcClass.IdCount = 0;
NpcClass.NpcComponents = {};
--==

-- MARK: new
function NpcClass.new(npcPackage: anydict)
    NpcClass.IdCount = NpcClass.IdCount +1;

    local self = {
        -- @properties
		NpcPackage = npcPackage;
        Id = NpcClass.IdCount;
        Name = npcPackage.Name;

        HumanoidType = npcPackage.HumanoidType;
        Character = nil;
        Head = nil;
        Humanoid = nil;
        RootPart = nil;
        Actor = nil;
        OnThink = nil;

        HealthComp = nil;
		StatusComp = nil;
		WieldComp = nil;
		
		NpcComponentsList = {};
		Storages = {};

        -- @signals
        Configurations = modConfigVariable.new(npcPackage.Configurations and modTables.DeepClone(npcPackage.Configurations) or {});
        Properties = modPropertiesVariable.new(modTables.DeepClone(npcPackage.Properties or {}));
    };

    setmetatable(self, NpcClass);

    return self;
end

local unneededHumanoidStates = {
	--Enum.HumanoidStateType.Climbing;
	Enum.HumanoidStateType.Dead;
	Enum.HumanoidStateType.FallingDown; --
	Enum.HumanoidStateType.Flying;
	--Enum.HumanoidStateType.Freefall;
	--Enum.HumanoidStateType.GettingUp;
	--Enum.HumanoidStateType.Jumping;
	--Enum.HumanoidStateType.Landed;
	--Enum.HumanoidStateType.None;
	Enum.HumanoidStateType.Physics; --
	--Enum.HumanoidStateType.PlatformStanding;
	Enum.HumanoidStateType.Ragdoll; --
	--Enum.HumanoidStateType.Running;
	Enum.HumanoidStateType.RunningNoPhysics;
	--Enum.HumanoidStateType.Seated;
	Enum.HumanoidStateType.StrafingNoPhysics;
	Enum.HumanoidStateType.Swimming; --
};


-- MARK: NpcClass:Setup
function NpcClass:Setup(baseNpcModel: Model, npcModel: Model)
	if RunService:IsClient() then return end;
	local npcPackage = self.NpcPackage;

	-- @properties
	self.Character = npcModel;
	self.Head = npcModel:WaitForChild("Head") :: BasePart;
	self.Humanoid = npcModel:FindFirstChildWhichIsA("Humanoid") :: Humanoid;
	self.RootPart = self.Humanoid.RootPart :: BasePart;
	self.Humanoid.Name = self.HumanoidType;

	npcModel:SetAttribute("EntityId", self.Id);
	
	self.Actor = npcModel:GetActor();

	local actorEvent: BindableEvent = Instance.new("BindableEvent");
	actorEvent.Name = "ActorEvent";
	actorEvent.Parent = npcModel;
	self.ActorEvent = actorEvent;

    self.Garbage = modGarbageHandler.new();

	self.PathAgent = {
		AgentRadius=1;
		AgentHeight=6;

		AgentCanClimb=true;

		WaypointSpacing = math.huge;

		Costs={
			Climb = 4;
			Water = 10;
			Avoid = 10;
			Barricade = 100;
			DefinePath = self.HumanoidType == "Human" and 0.25 or 0.5;
			Walkway = self.HumanoidType == "Human" and 0.5 or 0.75;
			Slowpath = self.HumanoidType == "Human" and 2 or nil;
			DoorPortal = self.HumanoidType == "Human" and 1 or nil;
		};
	};

	-- @signals
	self.OnThink = shared.EventSignal.new(`{self.Name}:Think`);


	--MARK: init Properties
	local properties: PropertiesVariable<{}> = self.Properties;
	properties.Cache = {};
	properties.OnChanged:Connect(function(k, v, ov)
		if k == "IsMoving" then
			self.Humanoid:SetAttribute("IsMoving", v);
		end
	end)


	--MARK: init HealthComp
    local healthComp: HealthComp = modHealthComponent.new(self :: any);
    self.HealthComp = healthComp;




	--MARK: init StatusComp
	local statusComp: StatusComp = modStatusComponent.new(self :: ComponentOwner);
	statusComp.OnProcess:Connect(function()
		if self.HealthComp.IsDead then return end;
		self.Think:Fire();
	end)
	npcModel:SetAttribute("StatusCompId", statusComp.Id);
	self.StatusComp = statusComp;
	


	--MARK: init WieldComp
	local wieldComp: WieldComp = modWieldComponent.new(self :: ComponentOwner);

	wieldComp.TargetableTags.Destructibles = true;
	if self.HumanoidType == "Human" then
		wieldComp.TargetableTags.Human = false;
        wieldComp.TargetableTags.Zombie = true;
		wieldComp.TargetableTags.Bandit = true;
		wieldComp.TargetableTags.Cultist = true;
		wieldComp.TargetableTags.Rat = true;

	elseif self.HumanoidType == "Zombie" then
		wieldComp.TargetableTags.Human = true;
        wieldComp.TargetableTags.Zombie = true;
		wieldComp.TargetableTags.Bandit = true;
		wieldComp.TargetableTags.Cultist = true;
		wieldComp.TargetableTags.Rat = true;

	elseif self.HumanoidType == "Bandit" then
		wieldComp.TargetableTags.Human = true;
        wieldComp.TargetableTags.Zombie = true;
		wieldComp.TargetableTags.Bandit = false;
		wieldComp.TargetableTags.Cultist = true;
		wieldComp.TargetableTags.Rat = true;

	elseif self.HumanoidType == "Rats" then
		wieldComp.TargetableTags.Human = true;
        wieldComp.TargetableTags.Zombie = true;
		wieldComp.TargetableTags.Bandit = true;
		wieldComp.TargetableTags.Cultist = true;
		wieldComp.TargetableTags.Rat = false;
		
	elseif self.HumanoidType == "Cultist" then
		wieldComp.TargetableTags.Human = true;
        wieldComp.TargetableTags.Zombie = true;
		wieldComp.TargetableTags.Bandit = true;
		wieldComp.TargetableTags.Cultist = false;
		wieldComp.TargetableTags.Rat = true;

	end

	self.WieldComp = wieldComp;
	--
    
	self.JointRotations = {
		WaistRot = modLayeredVariable.new(0);
		NeckRot = modLayeredVariable.new(0);
	};

	self.Garbage:Tag(self.JointRotations.WaistRot.Changed:Connect(function()
		local value = self.JointRotations.WaistRot:Get();
		local NpcWaist = npcModel:FindFirstChild("Waist", true);
		if NpcWaist then
			NpcWaist.C1 = CFrame.new(NpcWaist.C1.p) * CFrame.Angles(0, value, 0);
			NpcWaist.Parent:SetAttribute("WaistRot", value);
		end
	end))
	
	self.Garbage:Tag(self.JointRotations.NeckRot.Changed:Connect(function()
		local value = self.JointRotations.NeckRot:Get();
		local NpcNeck = npcModel:FindFirstChild("Neck", true);
		if NpcNeck then
			NpcNeck.C0 = CFrame.new(NpcNeck.C0.p) * CFrame.Angles(0, value, 0);
		end
	end))
    
	for a=1, #unneededHumanoidStates do
		self.Humanoid:SetStateEnabled(unneededHumanoidStates[a], false);
	end
	
	local newNpcInstanceModule = game.ServerScriptService.ServerLibrary.Entity.NpcClassInstance:Clone();
	newNpcInstanceModule.Parent = npcModel;

	local npcInstance = shared.require(newNpcInstanceModule);
	npcInstance.NpcClass = self;

	if npcPackage.DialogueInteractable == true then
		if npcModel:FindFirstChild("Interactable") then
			game.Debris:AddItem(npcModel:FindFirstChild("Interactable"), 0);
		end

		local newInteractable = modInteractables.createInteractable("Dialogue");
        newInteractable:SetAttribute("NpcName", self.Name);
        newInteractable.Parent = npcModel;
		self.Interactable = newInteractable;
	end


    local parallelNpcTemplate = game.ServerScriptService.ServerLibrary.Entity.ParallelNpc;
    local newParallelHandler = parallelNpcTemplate:Clone();
    newParallelHandler.Parent = npcModel;
    
    self.Remote = newParallelHandler:WaitForChild("NpcRemote");
    
    self.ActorEvent.Event:Connect(function(action, ...)
        if action == "init" then 
            local pNpc = ...;
            self.ParallelNpc = pNpc;
            return;
        end;
    end)
    
    local bindActor: BindableFunction = Instance.new("BindableFunction");
    bindActor.Name = "ActorBind";
    bindActor.Parent = npcModel;
    self.Bind = bindActor;

    newParallelHandler.Enabled = true;

	task.spawn(function()
		local rightArm: BasePart? = baseNpcModel:FindFirstChild("RightUpperArm") and (npcModel:WaitForChild("RightUpperArm") :: BasePart) or nil;
		local rightHand: BasePart? = baseNpcModel:FindFirstChild("RightHand") and (npcModel:WaitForChild("RightHand") :: BasePart) or nil;
		local rightPoint: BasePart? = baseNpcModel:FindFirstChild("RightPoint") and (npcModel:WaitForChild("RightPoint") :: BasePart) or nil;
		if rightArm and rightHand and rightPoint then
			local middle: BasePart? = baseNpcModel:FindFirstChild("RightMiddle") and (npcModel:WaitForChild("RightMiddle") :: BasePart) or nil;
			local pinky: BasePart? = baseNpcModel:FindFirstChild("RightPinky") and (npcModel:WaitForChild("RightPinky") :: BasePart) or nil;

			local function updateHand()
                if rightPoint then
                    rightPoint.Color = rightHand.Color;
                    rightPoint.Transparency = rightHand.Transparency;
                end
                if middle then
                    middle.Color = rightHand.Color;
                    middle.Transparency = rightHand.Transparency;
                end
                if pinky then
                    pinky.Color = rightHand.Color;
                    pinky.Transparency = rightHand.Transparency;
                end
			end
			rightHand:GetPropertyChangedSignal("Color"):Connect(updateHand);
			rightHand:GetPropertyChangedSignal("Transparency"):Connect(updateHand);
			updateHand()
		end

		local leftArm: BasePart? = baseNpcModel:FindFirstChild("LeftUpperArm") and (npcModel:WaitForChild("LeftUpperArm") :: BasePart) or nil;
		local leftHand: BasePart? = baseNpcModel:FindFirstChild("LeftHand") and (npcModel:WaitForChild("LeftHand") :: BasePart) or nil;
		local leftPoint: BasePart? = baseNpcModel:FindFirstChild("LeftPoint") and (npcModel:WaitForChild("LeftPoint") :: BasePart) or nil;
		if leftArm and leftHand and leftPoint then
			local middle: BasePart? = baseNpcModel:FindFirstChild("LeftMiddle") and (npcModel:WaitForChild("LeftMiddle") :: BasePart) or nil;
			local pinky: BasePart? = baseNpcModel:FindFirstChild("LeftPinky") and (npcModel:WaitForChild("LeftPinky") :: BasePart) or nil;

			local function updateHand()
				if leftPoint then
					leftPoint.Color = leftHand.Color;
                    leftPoint.Transparency = leftHand.Transparency;
				end
				if middle then
					middle.Color = leftHand.Color;
					middle.Transparency = leftHand.Transparency;
				end
				if pinky then
					pinky.Color = leftHand.Color;
					pinky.Transparency = leftHand.Transparency;
				end
			end
			leftHand:GetPropertyChangedSignal("Color"):Connect(updateHand);
			leftHand:GetPropertyChangedSignal("Transparency"):Connect(updateHand);
			updateHand()
		end
	end)


    -- default components
	self:AddComponent("Move");
	self:AddComponent("BehaviorTree");
    if npcModel:GetAttribute("HasRagdoll") == true then
		self:AddComponent("Ragdoll")();
	end

	self.Garbage:Tag(function()
		for _, jointLV in pairs(self.JointRotations) do
			jointLV:Destroy();
		end
	end)

	self.Garbage:Tag(self.Humanoid.StateChanged:Connect(function(new, old)
		self:RefreshRagdollJoints();
	end));
	
	if npcPackage.AddComponents then
		for a=1, #npcPackage.AddComponents do
			self:AddComponent(npcPackage.AddComponents[a]);
		end
	end	
	if npcPackage.AddBehaviorTrees then
		for a=1, #npcPackage.AddBehaviorTrees do
			local treeName = npcPackage.AddBehaviorTrees[a];
			self.BehaviorTree:LoadTree(treeName);
		end
	end

	if npcPackage.Spawning then
		npcPackage.Spawning(self);
	end

	self.Configurations:Calculate();

	self.Garbage:Tag(self.Humanoid.Died:Connect(function()
		self:Kill();
	end));
	self.Garbage:Tag(npcModel.ChildRemoved:Connect(function(child)
		if child.Name ~= "HumanoidRootPart" then return end;
		self:Kill();
	end))
	self.Garbage:Tag(npcModel.Destroying:Connect(function()
		self:Kill();
	end));
	if RunService:IsStudio() then
		self.Garbage:Tag(npcModel.AncestryChanged:Connect(function()
			if npcModel.Parent ~= nil then return end;
			if self == nil then return end;
			
			self:Kill();
		end));
	end

	self.RootPart:AddTag("EntityRootPart");
	-- move to workspace
	npcModel.Parent = workspace.Entity;
	self:SetNetworkOwner(nil);

	modNpcAnimator(self);
	
	if npcPackage.Spawned then
		npcPackage.Spawned(self);
	end
	
	local defaultBehaviorTree = npcPackage.Script:FindFirstChild("DefaultTree")
	if defaultBehaviorTree then
		self.Garbage:Tag(self.OnThink:Connect(function()
			self.BehaviorTree:RunTree(defaultBehaviorTree, true);
		end));
	end

	self.IsReady = true;
end

function NpcClass.initHealthComp(npcClass: NpcClass, healthComp: HealthComp)
    healthComp.OnHealthChanged:Connect(function()
		local humanoid: Humanoid = npcClass.Humanoid;
		
		humanoid.Health = healthComp.CurHealth;
		humanoid.MaxHealth = healthComp.MaxHealth;
	end)
	healthComp.OnIsDeadChanged:Connect(function()
		local humanoid: Humanoid = npcClass.Humanoid;
		
		humanoid:SetAttribute("IsDead", healthComp.IsDead);
		if healthComp.IsDead then
			humanoid:ChangeState(Enum.HumanoidStateType.Dead);

			npcClass:Kill();
		end
	end)

	function healthComp:TakeDamage(damageData: DamageData)
		local npcClass: NpcClass = self.CompOwner;
        local properties = npcClass.Properties;

		local amount = damageData.Damage;

		local damageBy: CharacterClass? = damageData.DamageBy;
		local hitPart = damageData.TargetPart;
		local damageType = damageData.DamageType;
		local damageCate = damageData.DamageCate;

		local toolHandler: ToolHandlerInstance? = damageData.ToolHandler;
		local storageItem: StorageItem? = damageData.StorageItem;

		local dmgForce = damageData.DamageForce or Vector3.zero;
		local dmgPosition = damageData.DamagePosition or (hitPart and hitPart.Position);

		local immortality = properties.Immortal;
		local rootPart: BasePart = npcClass.RootPart;

		if damageType == "Heal" then
			amount = -amount;

		else
			if immortality and immortality > 0 then
				amount = amount * math.clamp(1-immortality, 0, 1);
			end

		end

		local initAmount = amount;

		if damageType == "Fire" then
			if npcClass and npcClass.OnFlammableIgnite then
				npcClass.OnFlammableIgnite();
			end
		end

		local immunity = npcClass:GetImmunity(damageType, damageCate);

		local infoBubblePlayers = npcClass:GetAttackers();
		if damageBy and damageBy.ClassName == "PlayerClass" then
			local player: Player = (damageBy :: PlayerClass):GetInstance();
			if player and table.find(infoBubblePlayers, player) == nil then
				table.insert(infoBubblePlayers, player);
			end
		end

		local toxicDmgTaken = false;
		if amount > 0 then
			if immunity > 1 then -- invert damage (1, inf]
				amount = amount * -immunity;

			elseif immunity < 0 and damageType ~= "Toxic" then -- extra damage [-inf, 0)

				if toolHandler then
					local configurations = toolHandler.EquipmentClass.Configurations;

					local toolDamage = configurations.PreModDamage;
					local toxicDamage = (toolDamage or 100) * (-immunity);

					if toxicDamage > 0 then
						task.delay(0.5, function()
							local newDmgData = DamageData.new{
								Damage = toxicDamage;
								ToolHandler = toolHandler;
								TargetPart = hitPart;
								DamageType = "Toxic";
							};
							healthComp:TakeDamage(newDmgData);
						end)
					end
					toxicDmgTaken = true;
				end

			elseif immunity > 0 then -- reduced damage (0, 1]
				amount = amount * (1-immunity);

			end
		end

		if initAmount > 0 and npcClass.CustomHealthbar and npcClass.CustomHealthbar.OnDamaged then
			local returnPacket = npcClass.CustomHealthbar:OnDamaged(initAmount, damageBy, storageItem, hitPart);

			if npcClass.Weapons == nil then npcClass.Weapons = {} end;
			if damageBy and damageBy.ClassName == "PlayerClass" then
				npcClass.Weapons[damageBy.Character.Name] = {};
			end

			if typeof(returnPacket) == "table" then
				amount = returnPacket.Amount or amount;
				immunity = returnPacket.Immunity or immunity;

			elseif returnPacket == true then -- stop damage processing here
				local healthInfo = npcClass.CustomHealthbar.Healths[hitPart.Name];
				local damage = initAmount;
				if healthInfo and healthInfo.Health <= 0 then
					damage = 0;
				end

				modInfoBubbles.Create{
					Players = infoBubblePlayers;
					Position = hitPart.Position;
					DamageType = damageType;
					DamageCate = damageCate;
					Value = damage;
				};
				return;

			elseif returnPacket == false then -- stop damage pass through;

				return;
			end
		end

		if amount > 0 then
			if npcClass:GetComponent("OnDamaged") then
				npcClass.OnDamaged(amount, damageBy, storageItem, hitPart, damageType);
			end
		end

		if amount < 0 then
			if npcClass.OnHealed then
				npcClass.OnHealed(amount, damageBy, storageItem, hitPart);
			end
		end

		local armorDmg, breakArmor = nil, false;
		if healthComp == nil then return end;
		
		if amount > 0 then -- On Damaged;
			if healthComp.FirstDamageTaken == nil then
				healthComp.FirstDamageTaken = workspace:GetServerTimeNow();
			end

			healthComp.LastDamageTaken = workspace:GetServerTimeNow();

			if healthComp.CurArmor > 0 then
				if healthComp.CurArmor - amount <= 0 then
					breakArmor = true;
				end
				healthComp:SetArmor(healthComp.CurArmor-amount);
				armorDmg = amount;

			else
				if tonumber(immortality) == nil or healthComp.CurHealth-amount > 10 then
					healthComp:SetHealth(healthComp.CurHealth-amount);
				end
			end

		elseif amount < 0 then -- On Heal;
			if healthComp.CurHealth + math.abs(amount) > healthComp.MaxHealth then
				healthComp:SetHealth(healthComp.MaxHealth);
			else
				healthComp:SetHealth(healthComp.CurHealth-amount);
			end
		end

		local killShotBool = false;

		hitPart = hitPart or npcClass.RootPart;
		if amount >= 1 then
			local dmgType = armorDmg and "Armor" or "Damage";
			if npcClass.Immunity then
				dmgType = (immunity > 0 and "Shield") or (immunity < 0 and "AntiShield");
			end
			dmgType = damageType or dmgType;

			local isLethal = healthComp.CurHealth <= 0;

			local killMarkerSnd;
			if isLethal and npcClass.KilledMark == nil then
				npcClass.KilledMark = true;
				killShotBool = true;

				killMarkerSnd = (hitPart and (hitPart.Name == "Head" or hitPart:GetAttribute("IsHead") == true)) and "KillHead" or "KillFlesh";

				if dmgPosition then
					rootPart:ApplyImpulseAtPosition(dmgForce * rootPart.AssemblyMass, dmgPosition);
				end

				local netown = damageBy;
				if npcClass.Player and damageBy and typeof(damageBy) == "Instance" and not damageBy:IsA("Player") then
					netown = npcClass.Player;
				end

				if typeof(netown) == "Instance" and netown:IsA("Player") then
					npcClass.KillerPlayer = netown;
				end
			end

			task.spawn(function()
				-- Skip joint breaking if conditions aren't met
				if amount <= healthComp.MaxHealth*0.05 then return end; --math.random(10, 20)/100

				-- Get the dealer's root part for force calculation
				local dealerRootPart: BasePart = nil;
				if damageBy == nil then
					return;
				elseif typeof(damageBy) == "Instance" then
					if damageBy:IsA("Player") then
						local classPlayer = shared.modPlayers.get(damageBy);
						dealerRootPart = classPlayer and classPlayer.RootPart;
					elseif damageBy:IsA("Model") then
						dealerRootPart = damageBy.PrimaryPart;
					end
				elseif typeof(damageBy) == "table" and damageBy.RootPart then
					dealerRootPart = damageBy.RootPart;
				end
				if dealerRootPart == nil then return end;


				local motor: Motor6D = hitPart and hitPart:FindFirstChildWhichIsA("Motor6D") or nil;

				local exludeList = {
					Root=true;
					Waist=(not isLethal);
					Neck=(not isLethal);
					LeftHip=(not isLethal);
					RightHip=(not isLethal);
					ToolGrip=(not isLethal);
				};

				local leftWieldJoints = {
					LeftShoulder=true;
					LeftElbow=true;
					LeftWrist=true;
				};
				local rightWieldJoints = {
					RightShoulder=true;
					RightElbow=true;
					RightWrist=true;
				};

				local toolHandler = npcClass.WieldComp.ToolHandler;
				if toolHandler then
					for _, grip in pairs(toolHandler.ToolGrips) do
						if grip.Name == "ToolGrip" or grip.Name == "RightToolGrip" then
							exludeList.RightShoulder = math.random(1, 16) ~= 1;
							exludeList.RightElbow = math.random(1, 16) ~= 1;
							exludeList.RightWrist = math.random(1, 16) ~= 1;
						elseif grip.Name == "LeftToolGrip" then
							exludeList.LeftShoulder = math.random(1, 16) ~= 1;
							exludeList.LeftElbow = math.random(1, 16) ~= 1;
							exludeList.LeftWrist = math.random(1, 16) ~= 1;
						end
					end
				end

				if npcClass.JointsStrength then
					for key, value in pairs(npcClass.JointsStrength) do
						exludeList[key] = math.random(1, math.max(value, 2)) ~= 1;
					end
				end

				if npcClass.Properties == nil or npcClass.Properties.BasicEnemy ~= true then return end;
				if motor == nil or exludeList[motor.Name] == true then return end;


				if toolHandler then
					local leftToolGripExist = false;
					local rightToolGripExist = false;
					for _, grip in pairs(toolHandler.ToolGrips) do
						if grip.Name == "ToolGrip" or grip.Name == "RightToolGrip" then
							rightToolGripExist = true;
						elseif grip.Name == "LeftToolGrip" then
							leftToolGripExist = true;
						end
					end
					if (leftWieldJoints[motor.Name] and leftToolGripExist) 
					or (rightWieldJoints[motor.Name] and rightToolGripExist) then
						npcClass.WieldComp:Unequip();
					end
				end

				local activeDmg = (npcClass.Properties.AttackDamage or 0) - (npcClass.DamageReduction or 0);
				npcClass.DamageReduction = (npcClass.DamageReduction or 0) + activeDmg*0.05;


				local part1: BasePart = motor.Part1 :: BasePart;

				npcClass:BreakJoint(motor);

				local dir = (part1.Position-dealerRootPart.Position).Unit;
				part1:ApplyImpulse(dir * part1.AssemblyMass * math.random(80, 140));
			end)

			task.delay(0.1, function()
				if isLethal and npcClass.IsDead ~= true and npcClass.OnDeath then
					npcClass.OnDeath();
				end
			end)

			if hitPart then
				modInfoBubbles.Create{
					Players=infoBubblePlayers;
					Position=hitPart.Position;
					Value=amount;
					Type=dmgType;

					KillSnd=killMarkerSnd;
					BreakSnd=breakArmor;
				};
			end

		elseif amount <= -1 then

			modInfoBubbles.Create{
				Players=infoBubblePlayers;
				Position=dmgPosition;
				Value=math.abs(amount);
				Type="Heal";
			};

		else
			if toxicDmgTaken ~= true then
				local attackers = infoBubblePlayers;

				if typeof(damageBy) == "Instance" then
					if damageBy:IsA("Player") and table.find(attackers, damageBy) == nil then
						table.insert(attackers, damageBy);

					else
						local attackerPlayer = game.Players:GetPlayerFromCharacter(damageBy);
						if attackerPlayer and table.find(attackers, attackerPlayer) == nil then
							table.insert(attackers, attackerPlayer);
						end

					end
				end

				modInfoBubbles.Create{
					Players=attackers;
					Position=hitPart.Position;
					Type="Immune";
				};
			end
		end

		-- Clone damage data for event
		local cloneDmgData = damageData:Clone();
		cloneDmgData.DamageBy = damageBy;
		cloneDmgData.DamageTo = npcClass;
		cloneDmgData.Killed = killShotBool;
		cloneDmgData.Immunity = immunity;

		local eventPacket: EventPacket = shared.modEventService:ServerInvoke("Npcs_BindDamaged", {}, cloneDmgData);
	end

	function healthComp:CanTakeDamageFrom(attackerCharacter: CharacterClass?)
		if attackerCharacter == nil then return true; end;
		local npcClass: NpcClass = self.CompOwner;
		if attackerCharacter == npcClass then return false end;

		if attackerCharacter.ClassName == "PlayerClass" then
			local player = (attackerCharacter :: PlayerClass):GetInstance();
			if self.NetworkOwners == nil then return true end;
			for a=1, #self.NetworkOwners do
				if self.NetworkOwners[a] == player then
					return true;
				end
			end
			return false;
		end

		return true;
	end

	local configurations: ConfigVariable = npcClass.Configurations;
	if configurations == nil then return end;

	if configurations.MaxHealth then
		local newMaxHealth = math.max(configurations.MaxHealth, 1);
		healthComp:SetMaxHealth(newMaxHealth);
		healthComp:SetHealth(newMaxHealth);

		Debugger:Warn(`Set max health`);
	end
end

function NpcClass:Kill()
	local healthComp = self.HealthComp;
	healthComp:SetIsDead(true);

	local rootPart: BasePart = self.RootPart;
	local prefab: Model = self.Prefab;
	local humanoid: Humanoid = self.Humanoid;
	
	humanoid.PlatformStand = true;
	humanoid.EvaluateStateMachine = false;
	humanoid.HealthDisplayDistance = 0;

	if humanoid.SeatPart then
		for _, weld in pairs(humanoid.SeatPart:GetChildren()) do
			if weld:IsA("Weld") and weld.Part1 == rootPart then
				game.Debris:AddItem(weld, 0);
			end
		end
	end

	if self.Animator then
		for _, track: AnimationTrack in pairs(self.Animator:GetPlayingAnimationTracks()) do
			track:Stop();
		end
	end

	if self.BehaviorTree then
		self.BehaviorTree.Disabled = true;
		self.BehaviorTree:StopTree();
	end

	self:SetNetworkOwner(nil, true);

	task.spawn(function()
		if rootPart then
			rootPart:RemoveTag("Enemies");
			for _, tag in pairs(rootPart:GetTags()) do
				rootPart:RemoveTag(tag);
			end
			local physicSleepDisabler = Instance.new("BodyForce");
			physicSleepDisabler.Parent = rootPart;
		end

		if prefab then
			for _, tag in pairs(prefab:GetTags()) do
				prefab:RemoveTag(tag);
			end
			prefab:SetAttribute("DeadbodyTick", tick());
			prefab:AddTag("Deadbody");
			task.delay(0.5, function()
				if not workspace.Entity:IsAncestorOf(prefab) then return end;
				prefab.Parent = workspace.Entities;
			end)
			task.delay(5, function()
				rootPart.Anchored = true;
				for _, obj in pairs(prefab:GetChildren()) do
					if not obj:IsA("BasePart") or obj.AssemblyRootPart == nil then continue end;
					obj.AssemblyRootPart.Anchored = true;
					obj.CanCollide = false;
				end
			end)
		end
		self:RefreshRagdollJoints();

	end)
	
	if self.WieldComp then
		self.WieldComp:Destroy();
	end
	if self.StatusComp then
		self.StatusComp:Destroy();
	end

	if self.ActorEvent then
		self.ActorEvent:Destroy();
	end

	if self.Properties and self.Properties.Destroy then
		self.Properties:Destroy();
	end
	
	for _, npcComponent in pairs(self:ListComponents()) do
		if typeof(npcComponent) ~= "table" then continue end;
		if npcComponent.Destroy then
			npcComponent:Destroy();
		end
	end

end

function NpcClass:AddComponent(component: string | ModuleScript)
	if type(component) == "string" then
        local componentName = component;
		if NpcClass.NpcComponents[componentName] == nil then
			local findModule = game.ServerScriptService.ServerLibrary.NpcComponents:FindFirstChild(componentName);
			if findModule == nil then error(`NpcComponent ({componentName}) does not exist.`); end;
			NpcClass.NpcComponents[componentName] = shared.require(findModule);
		end

        local npcComponent = NpcClass.NpcComponents[componentName];

		if self[componentName] == nil then
			self[componentName] = npcComponent.new(self);
			table.insert(self.NpcComponentsList, componentName);
		end;

		return self[componentName];

	elseif type(component) == "userdata" and component.ClassName == "ModuleScript" then
        local componentModule = component;
        local componentName = componentModule.Name;

		if self[componentName] == nil then
			self[componentName] = shared.require(componentModule).new(self);
			table.insert(self.NpcComponentsList, componentName);
		end

		return self[componentName];
	end
	error(`NpcClass: ({self.Name}) Component Module is not a module.`);
end

function NpcClass:GetComponent(componentName: string)
	local npcComponent = self[componentName];
	
	if npcComponent == nil and table.find(self.NpcComponentsList, componentName) == nil then
		Debugger:Warn(`NpcClass: ({self.Name}) Component ({componentName}) does not exist.`);
	end

	return npcComponent;
end

function NpcClass:ListComponents()
	local components = {};
	for a=1, #self.NpcComponentsList do
		local npcComponent = self[self.NpcComponentsList[a]];
		if npcComponent then
			table.insert(components, npcComponent);
		end
	end
	return components;
end

function NpcClass:SetNetworkOwner(value, lock)
	if typeof(value) == "Instance" and not value:IsA("Player") then return end;
	if self.LockNo == true then return end;
	if lock then self.LockNo = true end;

	local rootPart: BasePart = self.RootPart;
	local function setNO(v)
		if not rootPart:CanSetNetworkOwnership() then return end;

		if v == "auto" then
			local players = game.Players:GetPlayers();
			if #players > 0 then
				local closestDist, closestPlayer = math.huge, nil;
				for a=1, #players do
					local playerDist = players[a]:DistanceFromCharacter(rootPart.Position);
					if playerDist < closestDist then
						closestDist = playerDist;
						closestPlayer = players[a];
					end
				end

				rootPart:SetNetworkOwner(closestPlayer);

			else
				rootPart:SetNetworkOwnershipAuto();

			end

		else
			rootPart:SetNetworkOwner(v);

		end
	end

	if not workspace:IsAncestorOf(rootPart) then return end;
	if self.IsDestroyed then return end;

	setNO(value);
end

function NpcClass:SetCFrame(cframe: CFrame, cfAngle: CFrame?)
	if self.Humanoid and self.Humanoid.SeatPart and self.Humanoid.SeatPart:FindFirstChild("SeatWeld") then 
		self.Humanoid.SeatPart.SeatWeld:Destroy();
	end
	if cframe then
		local cfAng = cframe.Rotation;
		if cfAng == CFrame.Angles(0, 0, 0) then
			cfAng = self.RootPart.CFrame.Rotation;
		end
		if cfAngle then
			cfAng = cfAngle;
		end
		self.RootPart.CFrame = CFrame.new(cframe.Position) * cfAng;

	elseif self.Player 
		and self.Player.Character 
		and self.Player.Character.PrimaryPart 
		and self.Player.Character.PrimaryPart:IsDescendantOf(workspace) then

		self.RootPart.CFrame = self.Player.Character.PrimaryPart.CFrame;
	end
end

function NpcClass:ToggleInteractable(bool: boolean)
	if self.Interactable == nil then
		self.Interactable = self.Prefab:FindFirstChild("Interactable");
	end;
	if self.Interactable == nil then return end;

	self.Interactable.Name = bool and "Interactable" or "InteractableDisabled";
end

function NpcClass:UseInteractable(interactableId, values)
	local interactConfig: Configuration;

	for _, config in pairs(CollectionService:GetTagged("Interactable")) do
		if config:GetAttribute("_Id") ~= interactableId then continue end;
		interactConfig = config;
		break;
	end

	if interactConfig == nil then 
		Debugger:Warn(`{self.Name} UseInteractable>> Interactable ({interactableId}) does not exist.`);
		return;
	end;

	modInteractables.serverInteract(interactConfig, {
		NpcClass = self;
		Values = values;
	});
end

function NpcClass:IsRagdolling()
	local ragdollEnabled = self.Humanoid:GetAttribute("Ragdoll") == true;
	if self.HealthComp.IsDead then ragdollEnabled = true end;

	return ragdollEnabled;
end

function NpcClass:RefreshRagdollJoints()
	local npcModel: Model = self.Character;
	if npcModel == nil then return end;

	local ragdollEnabled = self:IsRagdolling();

	local canRagdoll = npcModel:GetAttribute("HasRagdoll") == true;
	if canRagdoll then
		for _, obj in pairs(npcModel:GetDescendants()) do
			if obj:IsA("Motor6D") and obj:GetAttribute("RagdollJoint") == true then
				if obj == nil or not npcModel:IsAncestorOf(obj) then continue end;
				if obj.Parent == nil then continue end;

				obj.Parent.BallSocketConstraint.Enabled = ragdollEnabled;
				obj.Enabled = not ragdollEnabled;

			elseif obj:IsA("BasePart") and obj.Parent == npcModel then
				if obj.Name == "CollisionRootPart" then continue end;

				local defaultCanCollide = obj:GetAttribute("DefaultCanCollide");
				if defaultCanCollide == nil then
					obj:SetAttribute("DefaultCanCollide", obj.CanCollide);
					defaultCanCollide = obj.CanCollide;
				end
				if ragdollEnabled then
					if obj.Name ~= "LeftUpperLeg" and obj.Name ~= "RightUpperLeg" and obj.Name ~= "HumanoidRootPart" and obj.CanQuery then
						obj.CanCollide = true;
					else
						obj.CanCollide = false;
					end
				else
					obj.CanCollide = defaultCanCollide;
				end

			end
		end
	end
end

function NpcClass:LoadClientScript(players)
	local clientScript = self.NpcPackage.ClientScript;
	if clientScript == nil then
		clientScript = self.ClientScript;
	end

	if clientScript == nil then return end;
	players = typeof(players) == "table" and players or {players};

	for _, player in pairs(players) do
		if player.Character == nil then continue end;

		local exist = false;
		for _, obj in pairs(player.Character:GetChildren()) do
			if obj:IsA("LocalScript") and obj:GetAttribute("EntityId") == self.Prefab:GetAttribute("EntityId") then
				exist = true;
				break;
			end
		end

		if not exist then
			local clientEffects = clientScript:Clone();
			clientEffects:SetAttribute("EntityId", self.Prefab:GetAttribute("EntityId"));

			local prefabTag = clientEffects:WaitForChild("Prefab");
			prefabTag.Value = self.Prefab;
			task.defer(function()
				clientEffects.Parent = player.Character;
			end)

			self.Character.Destroying:Connect(function()
				game.Debris:AddItem(clientEffects, 0);
			end)
		end
	end
end

function NpcClass:IsInVision(object: BasePart, visionAngleDeg: number)
	if self.Head == nil then Debugger:Warn(self.Name.." can't see without a head."); return end;
	if object == nil then Debugger:Warn("Missing object", debug.traceback()); return end;

	local hitCount = 0;
	local maxCount = 0;
	
	local function scan(part)
		local direction = (part.Position-self.Head.Position).Unit;
		local relativeCframe = self.RootPart.CFrame:toObjectSpace(part.CFrame);
		
		if visionAngleDeg then
			local dirAngle = math.deg(math.atan2(relativeCframe.X, -relativeCframe.Z));
			if math.abs(dirAngle) > visionAngleDeg then
				return;
			end
		end
		
		local ray = Ray.new(self.Head.Position, direction*(self.VisionDistance or 64));
		local hit, point = workspace:FindPartOnRayWithWhitelist(ray, {workspace.Environment; workspace.Terrain; part}, true);
		if hit and hit == part then
			hitCount = hitCount +1;
			if maxCount < hitCount then
				maxCount = hitCount;
			end
		end
	end
	
	if object:IsA("Model") then
		local parts = object:GetChildren();
		for a=1, #parts do
			if parts[a]:IsA("BasePart") then
				maxCount = maxCount +1;
				scan(parts[a]);
			end
		end
	else
		scan(object);
	end
	
	return (hitCount > 0), math.clamp(hitCount/maxCount, 0, 1);
end;

function NpcClass:DistanceFrom(pos: Vector3)
	if pos == nil then Debugger:Warn("Missing pos param."); return 0 end;
	return self.RootPart and (self.RootPart.Position - pos).Magnitude;
end

function NpcClass:SendActorMessage(...)
	if self.Actor == nil then return end;
	if not workspace:IsAncestorOf(self.Character) then return end;
	self.Character:SendMessage(...);
end


function NpcClass:GetImmunity(damageType, damageCate)
	local immunity = self.Immunity or 0;

	if damageCate == DamageData.DamageCategory.ExplosiveBarrel then
		damageType = nil;
		immunity = 0;
	end

	if damageType == "Frost" then immunity = 0 end;

	if self.MeleeImmunity and self.MeleeImmunity > 0 and damageCate == DamageData.DamageCategory.Melee then
		immunity = self.MeleeImmunity;
	end

	local statusComp: StatusComp = self.StatusComp;
	local toxicStatusClass: StatusClassInstance = statusComp:GetOrDefault("ToxicBarrage");
	if toxicStatusClass then
		immunity = immunity-toxicStatusClass.Values.ImmunityReduction;
	end

	return immunity;
end

function NpcClass:GetMapLocation()
	local properties = self.Properties;
	local cache = properties.Cache;
	if cache.lastGetMapLocation == nil or tick()-cache.lastGetMapLocation > 2 then
		cache.lastGetMapLocation = tick();
		
		local modMapLibrary = shared.require(game.ReplicatedStorage.Library.MapLibrary);
		local layerName, _layerData = modMapLibrary:GetLayer(self.RootPart.Position);
		properties.MapLocation = layerName;
		self.Character:SetAttribute("Location", layerName);
	end

	return properties.MapLocation;
end

function NpcClass:Sit(seatPart: Seat)
	seatPart:Sit(self.Humanoid);
end

function NpcClass:Destroy()

end


return NpcClass;