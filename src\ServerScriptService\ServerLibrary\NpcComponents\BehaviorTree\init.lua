local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--== Script;
local RunService = game:GetService("RunService");

local modLogicTree = shared.require(game.ReplicatedStorage.Library.LogicTree);
local modLogicString = shared.require(game.ReplicatedStorage.Library.LogicString);

local behaviorTreesFolder = script;
--==
local Component = {};
Component.__index = Component;

function Component.new(npcClass)
	local self = {
		NpcClass = npcClass;
		Trees = {};
		
		Thread = nil;
		Running = false;
		
		Success = modLogicTree.Success;
		Failure = modLogicTree.Failure;
		Pass = modLogicTree.Pass;
		End = modLogicTree.End;
	};
	
	setmetatable(self, Component);
	return self;
end

function Component:StopTree()
	--Debugger:Log("think", self.ThinkThread and coroutine.status(self.ThinkThread) or "nil");
	self.Running = false;
	
	-- for key, logicTree in pairs(self.Trees) do
	-- 	logicTree.Disabled = true;
	-- end
	--if self.Thread and coroutine.status(self.Thread) ~= "dead" then
	--	local stopThinkS, stopThinkE = pcall(function() coroutine.close(self.Thread) end);

	--	self.Thread = nil;

	--	if not stopThinkS then
	--		Debugger:Warn("thinkthread ",self.Name," failed ", stopThinkE); 
	--	end
	--end;
end

function Component:RunTreeLeaf(treeName, leafName, ...)
	local tree = self.Trees[treeName];
	if tree == nil then
		return;
	end

	tree:Call(leafName, self, self.NpcClass, ...);
end

function Component:LoadTree(treeObj)
	if self.Disabled then return end;

	local treeSrc = nil;
	local treeName = treeObj;
	
	if typeof(treeObj) == "Instance" then
		treeSrc = treeObj;
		treeName = treeObj.Name;
	end
	
	if self.Trees[treeName] then
		return self.Trees[treeName];
	end
	
	if treeSrc == nil then
		treeSrc = behaviorTreesFolder:FindFirstChild(treeName);
	end

	if treeSrc then
		local treePackage = shared.require(treeSrc);
		if typeof(treePackage) == "function" then
			self.Trees[treeName] = shared.require(treeSrc)(self.NpcClass);
			self.Trees[treeName].Name = treeName;

		elseif typeof(treePackage) == "table" then
			if treePackage.Logic then
				if treePackage.Tree then
					self.Trees[treeName] = treePackage.Tree;

				else
					local tree = modLogicTree.new(treePackage.Logic);
					tree.Name = treeName;

					for a=1, #tree.NodeNames do
						local nodeName = tree.NodeNames[a];

						if treePackage[nodeName] and typeof(treePackage[nodeName]) == "function" then
							tree:Hook(nodeName, treePackage[nodeName]);

						else
							local nodeTreeScr = behaviorTreesFolder:FindFirstChild(nodeName);
							if nodeTreeScr then
								tree:Hook(nodeName, function(tree, npcClass)
									return npcClass.BehaviorTree:RunTree(nodeTreeScr, false);
								end);
							end
						end
					end
					treePackage.Tree = tree;
					
					self.Trees[treeName] = tree;
				end

			elseif treePackage.LogicString then

				if treePackage.LogicStringEvaluator == nil then
					local logicStringEvaluator = modLogicString.newEvaluator(treePackage.LogicString);
					logicStringEvaluator:Setup();
					treePackage.LogicStringEvaluator = logicStringEvaluator;
				end

				self.Trees[treeName] = treePackage;
			end
		end
	end
		
	if self.Trees[treeName] == nil then
		Debugger:Warn("Behavior tree did not load: ", treeName);
	end

	return self.Trees[treeName];
end

function Component:RunTree(treeObj, isRoot)
	if self.Disabled then return end;
	
	if isRoot then
		if self.Running then return end;
		
		self.Thread = coroutine.running();
		self.Running = true;
		
	end

	local treeSrc = nil;
	local treeName = treeObj;
	
	if typeof(treeObj) == "Instance" then
		treeSrc = treeObj;
		treeName = treeObj.Name;
	end

	local treePackage = self.Trees[treeName];

	if treePackage == nil and isRoot then
		treePackage = self:LoadTree(treeObj);
	end

	if treePackage == nil then
		return;
	end

	local tl = tick();
	local eval;
	if treePackage.LogicStringEvaluator then
		local logicEvaluator = treePackage.LogicStringEvaluator;
		eval = logicEvaluator:Evaluate(treePackage, self.NpcClass);

		if typeof(eval) == "string" and treePackage[eval] then
			treePackage[eval](self.NpcClass);
			if not isRoot then
				return eval;
			end
		end
	else
		eval = treePackage:Process(self, self.NpcClass);
	end
	
	if isRoot then
		self.State = treePackage.State;
		self.Status = treePackage.Status;
		
		if treePackage.LogicStringEvaluator then
			if RunService:IsStudio() or workspace:GetAttribute("IsDev") then
				local att = self.NpcClass.Head:FindFirstChild("ThinkHudPrint") or Debugger:HudPrint(Vector3.zero, ``);
				att.Name = `ThinkHudPrint`;
				att.Position = Vector3.new(0, 1, 0);
				att.Parent = self.NpcClass.Head;
				att:SetAttribute(`Text`, `Think: {eval}`);
			end
		else
			if self.NpcClass.Character:GetAttribute("Debug") == true then
				Debugger:Log(self.NpcClass.Name, 
					"Behavior:", 
					`{self.State} = {treePackage.Status}`, 
					`Time: {string.format("%.3f", (tick()-tl))}s`
				);
			end
		end
	end
	
	if isRoot then
		self.Running = false;
	end
	
	return eval;
end

return Component;