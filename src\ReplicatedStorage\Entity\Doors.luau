local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local TweenService = game:GetService("TweenService");
local RunService = game:GetService("RunService");

local modGlobalVars = shared.require(game.ReplicatedStorage:WaitForChild("GlobalVariables"));
local modRemotesManager = shared.require(game.ReplicatedStorage.Library.RemotesManager);


local Doors = {};
Doors.__index = Doors;
Doors.ClassName = "Door";

Doors.InstanceList = {};

Doors.Types = {
	Normal = "Normal";
	Sliding = "Sliding";
};

local remoteDoorEntity;
local DOOR_TWEENINFO = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out);
--==
function Doors.onRequire()
	remoteDoorEntity = modRemotesManager:Get("DoorEntity");
	
	if RunService:IsServer() then
        remoteDoorEntity.OnServerEvent:Connect(function(player, action, config)
            local playerClass: PlayerClass = shared.modPlayers.get(player);

            if action == "toggle" then
                local doorInstance: DoorInstance = Doors.getOrNew(config);
                if doorInstance == nil then
                    Debugger:Warn("Door does not exist.");
                    return;
                end

                doorInstance:Toggle(nil, playerClass:GetCFrame(), player);
            end
        end)
    end
end

function Doors.clearOldInstance()
	for config, instance in pairs(Doors.InstanceList) do
		local destroy = false;

		if not game:IsAncestorOf(config) then
			destroy = true;
		end

		if destroy then
			Doors.InstanceList[config] = nil;
		end
	end
end

function Doors.getOrNew(config)
    if config == nil or not config:IsA("Configuration") then return end;

	local doorInstance = Doors.InstanceList[config];
	if doorInstance == nil then
		doorInstance = Doors.new(config);
		Doors.InstanceList[config] = doorInstance;

		config.Destroying:Once(function()
			Debugger:Warn(`Doors config destroying:`, config:GetFullName());
			Doors.InstanceList[config] = nil;
			Doors.clearOldInstance();
		end)
	end

	return doorInstance;
end

function Doors.createDoor()
    local new = Instance.new("Configuration");
    new.Name = "Door";
    new:AddTag("Door");
    return new;
end

function Doors.new(config)
	local self = {
        Config = config;
        Model = config.Parent;

        Open = false;
        DoorType = "Normal";
    };

    setmetatable(self, Doors);

    local doorType = config:GetAttribute("DoorType") :: string;
    if doorType then
        self.DoorType = doorType;
    end

    return self;
end

function Doors:Toggle(openVal, openerCFrame, player)
	if RunService:IsClient() then 
		remoteDoorEntity:FireServer("toggle", self.Config);
        return;
    end;
	
	if openVal ~= nil then
		if self.Open == openVal then return end;
		self.Open = openVal; 
		
	else
		self.Open = not self.Open;
		
	end;

	self.Config:SetAttribute("DoorOpen", self.Open);
	

	local isBehind = false;
	if openerCFrame then
		local dirAngle = modGlobalVars.GetAngleFront(self.Model.PrimaryPart.CFrame, openerCFrame);
		isBehind = not (dirAngle < 90 and dirAngle > -90);
	end


	local doorMotors = {}
	for _, obj in pairs(self.Model.PrimaryPart:GetChildren()) do
		if obj:IsA("Motor6D") and obj.Name:match("Door") then
			table.insert(doorMotors, obj);
		end
	end
	
	for a=1, #doorMotors do
		local dMotor = doorMotors[a];
		
		local doorPrefab = dMotor.Part1.Parent;
		
		local pathfindingMods = {};
		for _, obj in pairs(doorPrefab:GetDescendants()) do
			if obj:IsA("BasePart") and obj.CanCollide == true then
				local pathfindingMod = obj:FindFirstChildWhichIsA("PathfindingModifier");
				if pathfindingMod == nil then
					pathfindingMod = Instance.new("PathfindingModifier");
					pathfindingMod.Label = "Doorway";
					pathfindingMod.Parent = obj;
				end
				
				table.insert(pathfindingMods, pathfindingMod);
			end
		end
		
		local tweenObject;
		local newC1;
		
		if self.DoorType == Doors.Types.Normal then
			local invert = dMotor.Name == "DoorRBase" and 1 or -1;
			
			invert = invert * (isBehind and 1 or -1);
			
			newC1 = CFrame.Angles(0, self.Open and math.rad(self.OpenAngle or (invert*90)) or 0, 0);
			tweenObject = TweenService:Create(dMotor, self.TweenInfo or DOOR_TWEENINFO, {C1=newC1;});
			
		elseif self.DoorType == Doors.Types.Sliding then
			local invert = dMotor.Name == "DoorRBase" and 1 or -1;
			
			if self.SlideOneDoorOnly then
				if self.Open then
					if (isBehind and a > 1) or (not isBehind and a <= 1) then
						newC1 = self.OpenCFrame or CFrame.new(0, 0, -3.83);
						tweenObject = TweenService:Create(dMotor, self.TweenInfo or DOOR_TWEENINFO, {C1=newC1});
						
					end

				else
					-- close;
					newC1 = CFrame.new(0,0,0);
					tweenObject = TweenService:Create(dMotor, self.TweenInfo or DOOR_TWEENINFO, {C1=newC1});

				end
				
			else
				newC1 = (self.Open and (self.OpenCFrame or CFrame.new(0, 0, -3.83)) or CFrame.new(0,0,0));
				tweenObject = TweenService:Create(dMotor, self.TweenInfo or DOOR_TWEENINFO, {C1=newC1});
				
			end
		end
		
		if not self.Open then
			for a=1, #pathfindingMods do
				pathfindingMods[a].PassThrough = true;
			end
		end
		
		if tweenObject then
			tweenObject.Completed:Once(function(playbackState)
				if playbackState == Enum.PlaybackState.Completed then
					task.defer(function()
						dMotor.C1 = newC1;
					end)
					
					if self.Open then
						for a=1, #pathfindingMods do
							pathfindingMods[a].PassThrough = false;
						end
					end
				end
			end)
			
			tweenObject:Play();
		end
	end
end


return Doors;